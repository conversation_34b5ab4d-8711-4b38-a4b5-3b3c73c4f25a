#!/bin/zsh

# Set your MFA serial number
MFA_SERIAL_NUMBER="arn:aws:iam::255229891353:mfa/NewPortal"
# AWS profile - replace with yours
AWS_PROFILE="derp"
# AWS region - replace with yours
AWS_DEFAULT_REGION="eu-west-2"

# Prompt the user for the AWS token code
vared -p "Enter your AWS token code: " -c token_code

session_token=$(aws sts get-session-token --serial-number "$MFA_SERIAL_NUMBER" --profile "$AWS_PROFILE" --token-code "$token_code" --output json)

# Extract Access Key, Secret Key, and Session Token
AWS_ACCESS_KEY_ID=$(echo "$session_token" | jq -r .Credentials.AccessKeyId)
AWS_SECRET_ACCESS_KEY=$(echo "$session_token" | jq -r .Credentials.SecretAccessKey)
AWS_SESSION_TOKEN=$(echo "$session_token" | jq -r .Credentials.SessionToken)

# Check if credentials were retrieved successfully
if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ] || [ -z "$AWS_SESSION_TOKEN" ]; then
    echo "Failed to retrieve temporary AWS credentials."
    exit 1
fi

# Use sed to update or add the AWS credentials in ~/.zshrc
# The format accommodates both macOS and GNU sed versions
sed -i '' '/export AWS_ACCESS_KEY_ID=/d' ~/.zshrc
sed -i '' '/export AWS_SECRET_ACCESS_KEY=/d' ~/.zshrc
sed -i '' '/export AWS_SESSION_TOKEN=/d' ~/.zshrc
sed -i '' '/export AWS_DEFAULT_REGION=/d' ~/.zshrc

# Append new values to ~/.zshrc
echo "export AWS_ACCESS_KEY_ID=\"$AWS_ACCESS_KEY_ID\"" >> ~/.zshrc
echo "export AWS_SECRET_ACCESS_KEY=\"$AWS_SECRET_ACCESS_KEY\"" >> ~/.zshrc
echo "export AWS_SESSION_TOKEN=\"$AWS_SESSION_TOKEN\"" >> ~/.zshrc
echo "export AWS_DEFAULT_REGION=\"$AWS_DEFAULT_REGION\"" >> ~/.zshrc

echo "Temporary AWS credentials have been added to ~/.zshrc."
echo "Run 'source ~/.zshrc' to apply them in the current session." 
