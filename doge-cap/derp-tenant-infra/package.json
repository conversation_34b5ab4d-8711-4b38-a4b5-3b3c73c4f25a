{"name": "derp-tenant-infra", "version": "0.1.0", "engines": {"node": "^22", "pnpm": "^10"}, "bin": {"derp-tenant-infra": "bin/derp-tenant-infra.js"}, "scripts": {"preinstall": "npx only-allow pnpm", "build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk", "format": "prettier . --write"}, "devDependencies": {"@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/jest": "^29.5.14", "@types/node": "22.10.2", "aws-cdk": "2.173.2", "jest": "^29.7.0", "prettier": "3.4.2", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "~5.7.2"}, "dependencies": {"aws-cdk-lib": "2.173.2", "constructs": "^10.4.2"}, "prettier": {"plugins": ["@trivago/prettier-plugin-sort-imports"], "importOrder": ["^@core/(.*)$", "^@server/(.*)$", "^@ui/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true}}