# Derp infrastructure

<!-- TOC -->
* [Derp infrastructure](#derp-infrastructure)
  * [Prerequisites](#prerequisites)
    * [1. Install Node.js 22 and pnpm](#1-install-nodejs-22-and-pnpm)
      * [MacOS (using brew)](#macos-using-brew)
      * [Linux](#linux)
    * [2. Install project dependencies](#2-install-project-dependencies)
  * [Usage](#usage)
    * [Show difference between local changes and targeted AWS environment](#show-difference-between-local-changes-and-targeted-aws-environment)
    * [Synthesize a CloudFormation template](#synthesize-a-cloudformation-template)
    * [Deploy a stack](#deploy-a-stack)
    * [Other useful commands](#other-useful-commands)
  * [Bootstrapping an AWS environment with CDK](#bootstrapping-an-aws-environment-with-cdk)
  * [Deploying](#deploying)
  * [Appendix](#appendix)
    * [Publish a message destined for `clienta` to the SNS](#publish-a-message-destined-for-clienta-to-the-sns)
    * [Retrieve the private key of an SSH key-pair created by CDK/Cloudformation](#retrieve-the-private-key-of-an-ssh-key-pair-created-by-cdkcloudformation)
    * [SSH tunnel from bastion to RDS of `clienta`](#ssh-tunnel-from-bastion-to-rds-of-clienta)
    * [SSH tunnel from bastion to RDS of `kfllp`](#ssh-tunnel-from-bastion-to-rds-of-kfllp)
<!-- TOC -->

## Prerequisites

### 1. Install [Node.js 22](https://nodejs.org/en/download) and [pnpm](https://pnpm.io/installation)

#### MacOS (using [brew](https://brew.sh/))

```bash
brew install node@22 pnpm
```

#### Linux

```bash
# installs nvm (Node Version Manager)
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.0/install.sh | bash
# download and install Node.js
nvm install 22
# download and install pnpm
curl -fsSL https://get.pnpm.io/install.sh | sh -
```

### 2. Install project dependencies

From the root of the project execute:

```bash
pnpm install
```

## Usage

Currently, this project contains the following CDK stacks:

- `doge-to-derp-pipeline` - data pipeline between Doge and Derp
- `derp-default-cognito-stack` - default "catch-all" Cognito User Pool that serves all invalid subdomains
- `derp-default-routing-stack` - main Derp routing, like the central Application
  Load Balancer with its default catch-all listener and dummy UI Fargate service
- `derp-tenant-clienta-cognito-stack` - tenant `clienta` Cognito User Pool
- `derp-tenant-clienta-services-stack` - tenant `clienta` services in dedicated subnets

Adding or removing (or commenting out) blocks from each stack will be detected when invoking CDK and the differences
will be deployed.

### Show difference between local changes and targeted AWS environment

```bash
cdk diff derp-default-cognito-stack
```

### Synthesize a CloudFormation template

```bash
cdk synth derp-default-cognito-stack
```

### Deploy a stack

```bash
cdk deploy derp-default-cognito-stack
```

### Other useful commands

- `pnpm run build` compile typescript to js
- `pnpm run watch` watch for changes and compile
- `pnpm run test` perform the jest unit tests
- `cdk deploy` deploy this stack to your default AWS account/region
- `cdk diff` compare deployed stack with current state
- `cdk synth` emits the synthesized CloudFormation template

## Bootstrapping an AWS environment with CDK

[Bootstrapping is the process of provisioning resources for the AWS CDK before you can deploy AWS CDK apps into an AWS environment.](https://docs.aws.amazon.com/cdk/v2/guide/bootstrapping.html)
This is a one-time action and unless you are working on a completely brand-new environment you will never have to run
this.

There is an [open GitHub ticket](https://github.com/aws/aws-cdk/issues/21937) regarding what would be the least
privileges to run this, instead of using an admin account.

A policy
is [currently created](https://us-east-1.console.aws.amazon.com/iamv2/home?region=eu-west-2#/policies/details/arn%3Aaws%3Aiam%3A%3A************%3Apolicy%2Fcdk-bootstrapper?section=policy_permissions)
that contains the necessary permissions.

If you want to run this yourself, then attach that policy to your CLI user and given that your account ID
is `************` and the AWS region is `eu-west-2`, you can run:

```bash
cdk bootstrap aws://************/eu-west-2
```

The actual policy content is also referenced here:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Action": [
        "cloudformation:CreateChangeSet",
        "cloudformation:DeleteStack",
        "cloudformation:DescribeChangeSet",
        "cloudformation:DescribeStackEvents",
        "cloudformation:DescribeStacks",
        "cloudformation:ExecuteChangeSet",
        "cloudformation:GetTemplate",
        "cloudformation:DeleteChangeSet"
      ],
      "Resource": "arn:aws:cloudformation:eu-west-2:************:stack/CDKToolkit/*",
      "Effect": "Allow",
      "Sid": "CloudFormationPermissions"
    },
    {
      "Action": [
        "iam:CreateRole",
        "iam:DeleteRole",
        "iam:GetRole",
        "iam:AttachRolePolicy",
        "iam:DetachRolePolicy",
        "iam:DeleteRolePolicy",
        "iam:PutRolePolicy",
        "iam:UpdateAssumeRolePolicy"
      ],
      "Effect": "Allow",
      "Resource": [
        "arn:aws:iam::************:policy/*",
        "arn:aws:iam::************:role/cdk-*"
      ]
    },
    {
      "Action": [
        "s3:CreateBucket",
        "s3:DeleteBucket",
        "s3:PutBucketPolicy",
        "s3:DeleteBucketPolicy",
        "s3:PutBucketPublicAccessBlock",
        "s3:PutBucketVersioning",
        "s3:PutEncryptionConfiguration",
        "s3:PutLifecycleConfiguration"
      ],
      "Effect": "Allow",
      "Resource": ["arn:aws:s3:::cdk-*"]
    },
    {
      "Action": [
        "ssm:DeleteParameter",
        "ssm:GetParameter",
        "ssm:GetParameters",
        "ssm:PutParameter"
      ],
      "Effect": "Allow",
      "Resource": [
        "arn:aws:ssm:eu-west-2:************:parameter/cdk-bootstrap/*"
      ]
    },
    {
      "Action": [
        "ecr:CreateRepository",
        "ecr:DeleteRepository",
        "ecr:DescribeRepositories",
        "ecr:SetRepositoryPolicy",
        "ecr:PutLifecyclePolicy"
      ],
      "Effect": "Allow",
      "Resource": ["arn:aws:ecr:eu-west-2:************:repository/cdk-*"]
    }
  ]
}
```

## Deploying

In order to be able to deploy a CDK stack, your AWS CLI user needs to have attached a policy that allows them to assume
a CDK role.

This kind of policy
is [currently created](https://us-east-1.console.aws.amazon.com/iamv2/home?region=eu-west-2#/policies/details/arn%3Aaws%3Aiam%3A%3A************%3Apolicy%2Fcdk-deployer?section=policy_permissions)
that contains the necessary permissions.

Then from the root of the project run:

```bash
cdk deploy
```

The actual policy content is also referenced here:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": ["sts:AssumeRole"],
      "Resource": ["arn:aws:iam::*:role/cdk-*"]
    }
  ]
}
```

## Appendix

### Publish a message destined for `clienta` to the SNS

`--message-group-id` is required because we are using FIFO topics
`--message-attributes` contains the tenant name for which the message will be routed to

```bash
aws sns publish --topic-arn arn:aws:sns:eu-west-2:************:dogetoderppipeline-dogetoderpsnstopic-5CBBE48A.fifo --message '{"id": "test"}' --message-group-id 1111 --message-attributes '{"tenant": {"DataType": "String", "StringValue": "clienta"}}'
```

### Retrieve the private key of an SSH key-pair created by CDK/Cloudformation

Also seen in
the [official AWS documentation](https://docs.aws.amazon.com/AWSEC2/latest/UserGuide/create-key-pairs.html#create-key-pair-cloudformation).

Use the describe-key-pairs command as follows to get the ID of the key pair. Assuming your key name
is `derp-clienta-bastion-key-pair`:

```bash
aws ec2 describe-key-pairs --filters Name=key-name,Values=derp-clienta-bastion-key-pair --query KeyPairs[*].KeyPairId --output text
```

The following is an example output:

```bash
key-05abb699beEXAMPLE
```

Use the `get-parameter` command as follows to get the parameter for your key and save the key material in a .pem file.

```bash
aws ssm get-parameter --name /ec2/keypair/key-05abb699beEXAMPLE --with-decryption --query Parameter.Value --output text > derp-clienta-bastion-key-pair.pem
```

### SSH tunnel from bastion to RDS of `clienta`

```bash
ssh -i "derp-clienta-bastion-key-pair.pem" -v -N -L 5431:derp-tenant-clienta-services-derpclientadbafe3df8c-vrcvy8teiwwo.ceadtzohrwyh.eu-west-2.rds.amazonaws.com:5432 <EMAIL>
```

### SSH tunnel from bastion to RDS of `kfllp`

```bash
ssh -i "derp-clienta-bastion-key-pair.pem" -v -N -L 5431:derp-tenant-kfllp-services-sta-derpkfllpdbc65c81f8-9jnz60groa36.ceadtzohrwyh.eu-west-2.rds.amazonaws.com:5432 <EMAIL>
```
