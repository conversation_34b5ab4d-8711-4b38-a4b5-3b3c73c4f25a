import * as cdk from "aws-cdk-lib";
import { RemovalPolicy } from "aws-cdk-lib";
import * as acm from "aws-cdk-lib/aws-certificatemanager";
import * as ec2 from "aws-cdk-lib/aws-ec2";
import { Repository } from "aws-cdk-lib/aws-ecr";
import * as ecs from "aws-cdk-lib/aws-ecs";
import * as elbv2 from "aws-cdk-lib/aws-elasticloadbalancingv2";
import {
  ApplicationListener,
  SslPolicy,
} from "aws-cdk-lib/aws-elasticloadbalancingv2";
import * as iam from "aws-cdk-lib/aws-iam";
import * as logs from "aws-cdk-lib/aws-logs";
import { Construct } from "constructs";

export class DerpDefaultRoutingStack extends cdk.Stack {
  public readonly centralVpc: ec2.Vpc;
  public readonly centralListener: ApplicationListener;

  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    this.centralVpc = new ec2.Vpc(this, "derp-central-vpc", {
      ipAddresses: ec2.IpAddresses.cidr("10.0.0.0/16"),
      maxAzs: 2,
      subnetConfiguration: [
        // default
        {
          cidrMask: 26,
          name: "derp-default-private-isolated",
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
        {
          cidrMask: 26,
          name: "derp-default-private-with-egress",
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        {
          cidrMask: 26,
          name: "derp-default-public",
          subnetType: ec2.SubnetType.PUBLIC,
        },
        // clienta
        {
          cidrMask: 26,
          name: "derp-clienta-private-isolated",
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
        {
          cidrMask: 26,
          name: "derp-clienta-private-with-egress",
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        {
          cidrMask: 26,
          name: "derp-clienta-public",
          subnetType: ec2.SubnetType.PUBLIC,
        },
        // kfllp
        {
          cidrMask: 26,
          name: "derp-kfllp-private-isolated",
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
        {
          cidrMask: 26,
          name: "derp-kfllp-private-with-egress",
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        {
          cidrMask: 26,
          name: "derp-kfllp-public",
          subnetType: ec2.SubnetType.PUBLIC,
        },
        // dcc
        {
          cidrMask: 26,
          name: "derp-dcc-private-isolated",
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
        {
          cidrMask: 26,
          name: "derp-dcc-private-with-egress",
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        {
          cidrMask: 26,
          name: "derp-dcc-public",
          subnetType: ec2.SubnetType.PUBLIC,
        },
        // fidelity
        {
          cidrMask: 26,
          name: "derp-fidelity-private-isolated",
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
        {
          cidrMask: 26,
          name: "derp-fidelity-private-with-egress",
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        {
          cidrMask: 26,
          name: "derp-fidelity-public",
          subnetType: ec2.SubnetType.PUBLIC,
        },
        // demo
        {
          cidrMask: 26,
          name: "derp-demo-private-isolated",
          subnetType: ec2.SubnetType.PRIVATE_ISOLATED,
        },
        {
          cidrMask: 26,
          name: "derp-demo-private-with-egress",
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
        {
          cidrMask: 26,
          name: "derp-demo-public",
          subnetType: ec2.SubnetType.PUBLIC,
        },
      ],
    });

    // ACM certificate - already existing, importing it using its arn
    // https://eu-west-2.console.aws.amazon.com/acm/home?region=eu-west-2#/certificates/343465e8-721a-491b-86d5-28a33fb89085
    const arnCertificate =
      "arn:aws:acm:eu-west-2:255229891353:certificate/343465e8-721a-491b-86d5-28a33fb89085";
    const certificate = acm.Certificate.fromCertificateArn(
      this,
      "derp-central-alb-certificate",
      arnCertificate,
    );

    const logGroup = new logs.LogGroup(this, "derp-central-vpc-log-group", {
      logGroupName: "derp-central-vpc-log-group",
      removalPolicy: RemovalPolicy.DESTROY,
    });

    const role = new iam.Role(this, "derp-central-vpc-flow-logs-role", {
      assumedBy: new iam.ServicePrincipal("vpc-flow-logs.amazonaws.com"),
    });

    new ec2.FlowLog(this, "derp-central-vpc-flow-logs", {
      flowLogName: "derp-central-vpc-flow-logs",
      resourceType: ec2.FlowLogResourceType.fromVpc(this.centralVpc),
      destination: ec2.FlowLogDestination.toCloudWatchLogs(logGroup, role),
    });

    // VPC endpoints
    this.centralVpc.addInterfaceEndpoint("derp-central-ecr-dkr-vpc-endpoint", {
      service: ec2.InterfaceVpcEndpointAwsService.ECR_DOCKER,
      subnets: {
        subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
      },
    });
    this.centralVpc.addInterfaceEndpoint("derp-central-ecr-api-vpc-endpoint", {
      service: ec2.InterfaceVpcEndpointAwsService.ECR,
      subnets: {
        subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
      },
    });
    this.centralVpc.addInterfaceEndpoint("derp-central-ecr-logs-vpc-endpoint", {
      service: ec2.InterfaceVpcEndpointAwsService.CLOUDWATCH_LOGS,
      subnets: {
        subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
      },
    });
    this.centralVpc.addInterfaceEndpoint(
      "derp-central-secretsmanager-vpc-endpoint",
      {
        service: ec2.InterfaceVpcEndpointAwsService.SECRETS_MANAGER,
        subnets: {
          subnetType: ec2.SubnetType.PRIVATE_WITH_EGRESS,
        },
      },
    );
    this.centralVpc.addGatewayEndpoint("derp-central-s3-gateway-vpc-endpoint", {
      service: ec2.GatewayVpcEndpointAwsService.S3,
    });

    // Create the load balancer in a VPC. 'internetFacing' is 'false'
    // by default, which creates an internal load balancer.
    const centralApplicationLoadBalancer = new elbv2.ApplicationLoadBalancer(
      this,
      "derp-central-alb",
      {
        vpcSubnets: { subnetGroupName: "derp-default-public" },
        loadBalancerName: "derp-central-alb",
        vpc: this.centralVpc,
        internetFacing: true,
      },
    );

    // Fargate cluster
    const cluster = new ecs.Cluster(this, "derp-default-cluster", {
      clusterName: "derp-default-cluster",
      containerInsights: true,
      vpc: this.centralVpc,
    });

    this.centralListener = centralApplicationLoadBalancer.addListener(
      "derp-central-ui-listener",
      {
        port: 443,
        protocol: elbv2.ApplicationProtocol.HTTPS,
        sslPolicy: SslPolicy.RECOMMENDED_TLS,
        certificates: [certificate],
      },
    );

    // UI
    // https://eu-west-2.console.aws.amazon.com/ecr/repositories/private/255229891353/derp-ui?region=eu-west-2
    const derpUiImageRepository = Repository.fromRepositoryArn(
      this,
      "derp-default-ui-docker-image-repository",
      "arn:aws:ecr:eu-west-2:255229891353:repository/derp-ui",
    );

    const derpUiTaskDefinition = new ecs.FargateTaskDefinition(
      this,
      "derp-default-task-definition",
      {
        memoryLimitMiB: 512,
        cpu: 256,
      },
    );

    // Add a container to your task
    derpUiTaskDefinition.addContainer("derp-default-ui-container", {
      image: ecs.ContainerImage.fromEcrRepository(
        derpUiImageRepository,
        "default",
      ),
      portMappings: [
        {
          containerPort: 3000,
        },
      ],
    });

    // Load-balanced Fargate service Derp UI
    const derpUiServiceSecurityGroup = new ec2.SecurityGroup(
      this,
      "derp-default-ui-service-security-group",
      {
        vpc: this.centralVpc,
        allowAllOutbound: false,
        allowAllIpv6Outbound: false,
      },
    );
    derpUiServiceSecurityGroup.addEgressRule(
      ec2.Peer.anyIpv4(),
      ec2.Port.tcp(443),
      "Allow outbound traffic to the Derp UI",
    );

    const targetGroup = this.centralListener.addTargets(
      "derp-default-ui-target-group",
      {
        targetGroupName: "derp-default-ui-target-group",
        healthCheck: { path: "/" },
        protocol: elbv2.ApplicationProtocol.HTTP,
        port: 3000,
        protocolVersion: elbv2.ApplicationProtocolVersion.HTTP1,
      },
    );

    this.centralListener.addAction("derp-default-ui-tg-action", {
      action: elbv2.ListenerAction.forward([targetGroup]),
    });

    const fargateService = new ecs.FargateService(
      this,
      "derp-default-ui-service",
      {
        cluster,
        serviceName: "derp-default-ui-service",
        assignPublicIp: false,
        taskDefinition: derpUiTaskDefinition,
        securityGroups: [derpUiServiceSecurityGroup],
        desiredCount: 1,
        vpcSubnets: { subnetGroupName: "derp-default-private-with-egress" },
      },
    );
    targetGroup.addTarget(fargateService);
  }
}
