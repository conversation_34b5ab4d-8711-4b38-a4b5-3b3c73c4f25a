import * as cdk from "aws-cdk-lib";
import { RemovalPolicy } from "aws-cdk-lib";
import * as iam from "aws-cdk-lib/aws-iam";
import * as kms from "aws-cdk-lib/aws-kms";
import * as s3 from "aws-cdk-lib/aws-s3";
import * as sns from "aws-cdk-lib/aws-sns";
import { Construct } from "constructs";

export class DogeToDerpPipelineInfraStack extends cdk.Stack {
  public readonly dogeToDerpSnsTopic: sns.Topic;
  public readonly centralAssetsBucket: s3.Bucket;

  constructor(scope: Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // KMS encryption key that encrypts data in transit for the SNS between Doge and Derp
    const snsEncryptionKey = new kms.Key(
      this,
      "doge-to-derp-sns-encryption-key",
      {
        enableKeyRotation: true,
      },
    );
    snsEncryptionKey.addAlias("doge-to-derp-sns-encryption-key");

    // SNS between <PERSON><PERSON> and <PERSON>p
    const dogeToDerpSnsTopic = new sns.Topic(this, "doge-to-derp-sns-topic", {
      contentBasedDeduplication: true,
      displayName: `doge-to-derp-sns-topic`,
      masterKey: snsEncryptionKey,
      fifo: true,
    });

    const topicPolicy = new sns.TopicPolicy(
      this,
      "doge-to-derp-sns-topic-policy",
      {
        topics: [dogeToDerpSnsTopic],
      },
    );

    topicPolicy.document.addStatements(
      new iam.PolicyStatement({
        actions: [
          "SNS:GetTopicAttributes",
          "SNS:SetTopicAttributes",
          "SNS:AddPermission",
          "SNS:RemovePermission",
          "SNS:DeleteTopic",
          "SNS:Subscribe",
          "SNS:ListSubscriptionsByTopic",
          "SNS:Publish",
        ],
        principals: [new iam.AnyPrincipal()],
        conditions: {
          StringEquals: {
            "AWS:SourceOwner": "255229891353",
          },
        },
        resources: [dogeToDerpSnsTopic.topicArn],
      }),
    );

    topicPolicy.document.addStatements(
      new iam.PolicyStatement({
        actions: ["SNS:Publish"],
        principals: [
          new iam.ArnPrincipal(
            "arn:aws:iam::709954374541:role/SSMInstanceProfile",
          ),
        ],
        resources: [dogeToDerpSnsTopic.topicArn],
      }),
      new iam.PolicyStatement({
        actions: ["SNS:Publish"],
        principals: [
          new iam.ArnPrincipal(
            "arn:aws:iam::709954374541:user/doge-to-derp-sns",
          ),
        ],
        resources: [dogeToDerpSnsTopic.topicArn],
      }),
    );

    // Allow usage of the key from the Doge EC2 instance role and user 'doge-to-derp-sns' that will write to the SNS between Doge and Derp
    snsEncryptionKey.addToResourcePolicy(
      new iam.PolicyStatement({
        sid: "Allow use of the key from the Doge EC2 instance role and user doge-to-derp-sns",
        effect: iam.Effect.ALLOW,
        resources: ["*"],
        principals: [
          new iam.ArnPrincipal(
            "arn:aws:iam::709954374541:role/SSMInstanceProfile",
          ),
          new iam.ArnPrincipal(
            "arn:aws:iam::709954374541:user/doge-to-derp-sns",
          ),
        ],
        actions: [
          "kms:Encrypt",
          "kms:Decrypt",
          "kms:ReEncrypt*",
          "kms:GenerateDataKey*",
          "kms:DescribeKey",
        ],
      }),
    );
    this.dogeToDerpSnsTopic = dogeToDerpSnsTopic;

    // S3 bucket containing all exported assets from Doge
    this.centralAssetsBucket = new s3.Bucket(
      this,
      `derp-central-assets-bucket`,
      {
        bucketName: `derp-central-assets-bucket`,
        blockPublicAccess: s3.BlockPublicAccess.BLOCK_ALL,
        encryption: s3.BucketEncryption.S3_MANAGED,
        enforceSSL: true,
        versioned: true,
        removalPolicy: RemovalPolicy.RETAIN,
      },
    );
  }
}
