#!/usr/bin/env node
import * as cdk from "aws-cdk-lib";
import { Tags } from "aws-cdk-lib";

import { DerpDefaultCognitoStack } from "../lib/derp-default-cognito-stack";
import { DerpDefaultRoutingStack } from "../lib/derp-default-routing-stack";
import { DerpTenantClientaCognitoStack } from "../lib/derp-tenant-clienta-cognito-stack";
import { DerpTenantClientaServicesStack } from "../lib/derp-tenant-clienta-services-stack";
import { DerpTenantDccCognitoStack } from "../lib/derp-tenant-dcc-cognito-stack";
import { DerpTenantDccServicesStack } from "../lib/derp-tenant-dcc-services-stack";
import { DerpTenantDemoCognitoStack } from "../lib/derp-tenant-demo-cognito-stack";
import { DerpTenantDemoServicesStack } from "../lib/derp-tenant-demo-services-stack";
import { DerpTenantFidelityCognitoStack } from "../lib/derp-tenant-fidelity-cognito-stack";
import { DerpTenantFidelityServicesStack } from "../lib/derp-tenant-fidelity-services-stack";
import { DerpTenantKfllpCognitoStack } from "../lib/derp-tenant-kfllp-cognito-stack";
import { DerpTenantKfllpServicesStack } from "../lib/derp-tenant-kfllp-services-stack";
import { DogeToDerpPipelineInfraStack } from "../lib/doge-to-derp-pipeline-infra-stack";

const CDK_DEFAULT_ACCOUNT = "************";
const CDK_DEFAULT_REGION = "eu-west-2";
const CDK_DERP_BASE_DOMAIN = "cap.security.accenture.com";

const app = new cdk.App();

// Stack for the SNS that will receive data from Doge and direct it to Derp, with its dedicated encryption key in KMS,
// policy to allow publishing messages, and user to perform these actions
const dogeToDerpPipelineInfraStack = new DogeToDerpPipelineInfraStack(
  app,
  "doge-to-derp-pipeline",
  {
    env: { account: CDK_DEFAULT_ACCOUNT, region: CDK_DEFAULT_REGION },
  },
);

Tags.of(dogeToDerpPipelineInfraStack).add("project", "derp");

//////////////// Default - catch-all stack
const derpDefaultCognitoStack = new DerpDefaultCognitoStack(
  app,
  "derp-default-cognito-stack",
  "default",
  {
    env: { account: CDK_DEFAULT_ACCOUNT, region: CDK_DEFAULT_REGION },
  },
);
Tags.of(derpDefaultCognitoStack).add("project", "derp");
Tags.of(derpDefaultCognitoStack).add("tenant", "default");

const derpDefaultRoutingStack = new DerpDefaultRoutingStack(
  app,
  "derp-default-routing-stack",
  {
    env: { account: CDK_DEFAULT_ACCOUNT, region: CDK_DEFAULT_REGION },
  },
);
Tags.of(derpDefaultRoutingStack).add("project", "derp");
Tags.of(derpDefaultRoutingStack).add("tenant", "default");

//////////////// clienta
const derpTenantClientaCognitoStack = new DerpTenantClientaCognitoStack(
  app,
  "derp-tenant-clienta-cognito-stack",
  "clienta",
  {
    env: { account: CDK_DEFAULT_ACCOUNT, region: CDK_DEFAULT_REGION },
  },
);
Tags.of(derpTenantClientaCognitoStack).add("project", "derp");
Tags.of(derpTenantClientaCognitoStack).add("tenant", "clienta");

const derpTenantClientaServicesStack = new DerpTenantClientaServicesStack(
  app,
  `derp-tenant-clienta-services-stack`,
  "clienta",
  CDK_DERP_BASE_DOMAIN,
  CDK_DEFAULT_REGION,
  derpDefaultRoutingStack.centralVpc,
  derpDefaultRoutingStack.centralListener,
  1,
  derpTenantClientaCognitoStack.cognitoUserPoolId,
  derpTenantClientaCognitoStack.cognitoAppClientId,
  dogeToDerpPipelineInfraStack.dogeToDerpSnsTopic,
  dogeToDerpPipelineInfraStack.centralAssetsBucket,
  {
    env: { account: CDK_DEFAULT_ACCOUNT, region: CDK_DEFAULT_REGION },
  },
);
Tags.of(derpTenantClientaServicesStack).add("project", "derp");
Tags.of(derpTenantClientaServicesStack).add("tenant", "clienta");

//////////////// kfllp
const derpTenantKfllpCognitoStack = new DerpTenantKfllpCognitoStack(
  app,
  "derp-tenant-kfllp-cognito-stack",
  "kfllp",
  {
    env: { account: CDK_DEFAULT_ACCOUNT, region: CDK_DEFAULT_REGION },
  },
);
Tags.of(derpTenantKfllpCognitoStack).add("project", "derp");
Tags.of(derpTenantKfllpCognitoStack).add("tenant", "kfllp");

const derpTenantKfllpServicesStack = new DerpTenantKfllpServicesStack(
  app,
  `derp-tenant-kfllp-services-stack`,
  "kfllp",
  CDK_DERP_BASE_DOMAIN,
  CDK_DEFAULT_REGION,
  derpDefaultRoutingStack.centralVpc,
  derpDefaultRoutingStack.centralListener,
  2,
  derpTenantKfllpCognitoStack.cognitoUserPoolId,
  derpTenantKfllpCognitoStack.cognitoAppClientId,
  dogeToDerpPipelineInfraStack.dogeToDerpSnsTopic,
  dogeToDerpPipelineInfraStack.centralAssetsBucket,
  {
    env: { account: CDK_DEFAULT_ACCOUNT, region: CDK_DEFAULT_REGION },
  },
);
Tags.of(derpTenantKfllpCognitoStack).add("project", "derp");
Tags.of(derpTenantKfllpServicesStack).add("tenant", "kfllp");

//////////////// dcc
const derpTenantDccCognitoStack = new DerpTenantDccCognitoStack(
  app,
  "derp-tenant-dcc-cognito-stack",
  "dcc",
  {
    env: { account: CDK_DEFAULT_ACCOUNT, region: CDK_DEFAULT_REGION },
  },
);
Tags.of(derpTenantDccCognitoStack).add("project", "derp");
Tags.of(derpTenantDccCognitoStack).add("tenant", "dcc");

const derpTenantDccServicesStack = new DerpTenantDccServicesStack(
  app,
  `derp-tenant-dcc-services-stack`,
  "dcc",
  CDK_DERP_BASE_DOMAIN,
  CDK_DEFAULT_REGION,
  derpDefaultRoutingStack.centralVpc,
  derpDefaultRoutingStack.centralListener,
  3,
  derpTenantDccCognitoStack.cognitoUserPoolId,
  derpTenantDccCognitoStack.cognitoAppClientId,
  dogeToDerpPipelineInfraStack.dogeToDerpSnsTopic,
  dogeToDerpPipelineInfraStack.centralAssetsBucket,
  {
    env: { account: CDK_DEFAULT_ACCOUNT, region: CDK_DEFAULT_REGION },
  },
);
Tags.of(derpTenantDccCognitoStack).add("project", "derp");
Tags.of(derpTenantDccServicesStack).add("tenant", "dcc");

//////////////// fidelity
const derpTenantFidelityCognitoStack = new DerpTenantFidelityCognitoStack(
  app,
  "derp-tenant-fidelity-cognito-stack",
  "fidelity",
  {
    env: { account: CDK_DEFAULT_ACCOUNT, region: CDK_DEFAULT_REGION },
  },
);
Tags.of(derpTenantFidelityCognitoStack).add("project", "derp");
Tags.of(derpTenantFidelityCognitoStack).add("tenant", "fidelity");

const derpTenantFidelityServicesStack = new DerpTenantFidelityServicesStack(
  app,
  `derp-tenant-fidelity-services-stack`,
  "fidelity",
  CDK_DERP_BASE_DOMAIN,
  CDK_DEFAULT_REGION,
  derpDefaultRoutingStack.centralVpc,
  derpDefaultRoutingStack.centralListener,
  4,
  derpTenantFidelityCognitoStack.cognitoUserPoolId,
  derpTenantFidelityCognitoStack.cognitoAppClientId,
  dogeToDerpPipelineInfraStack.dogeToDerpSnsTopic,
  dogeToDerpPipelineInfraStack.centralAssetsBucket,
  {
    env: { account: CDK_DEFAULT_ACCOUNT, region: CDK_DEFAULT_REGION },
  },
);
Tags.of(derpTenantFidelityCognitoStack).add("project", "derp");
Tags.of(derpTenantFidelityServicesStack).add("tenant", "fidelity");

//////////////// demo
const derpTenantDemoCognitoStack = new DerpTenantDemoCognitoStack(
  app,
  "derp-tenant-demo-cognito-stack",
  "demo",
  {
    env: { account: CDK_DEFAULT_ACCOUNT, region: CDK_DEFAULT_REGION },
  },
);
Tags.of(derpTenantDemoCognitoStack).add("project", "derp");
Tags.of(derpTenantDemoCognitoStack).add("tenant", "demo");

const derpTenantDemoServicesStack = new DerpTenantDemoServicesStack(
  app,
  `derp-tenant-demo-services-stack`,
  "demo",
  CDK_DERP_BASE_DOMAIN,
  CDK_DEFAULT_REGION,
  derpDefaultRoutingStack.centralVpc,
  derpDefaultRoutingStack.centralListener,
  5,
  derpTenantDemoCognitoStack.cognitoUserPoolId,
  derpTenantDemoCognitoStack.cognitoAppClientId,
  dogeToDerpPipelineInfraStack.dogeToDerpSnsTopic,
  dogeToDerpPipelineInfraStack.centralAssetsBucket,
  {
    env: { account: CDK_DEFAULT_ACCOUNT, region: CDK_DEFAULT_REGION },
  },
);
Tags.of(derpTenantDemoCognitoStack).add("project", "derp");
Tags.of(derpTenantDemoServicesStack).add("tenant", "demo");
