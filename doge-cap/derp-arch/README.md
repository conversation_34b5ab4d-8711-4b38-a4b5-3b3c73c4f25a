# Doge Enterprise Portal architecture

Generate the Doge Enterprise portal architecture diagram using Python and
the [Diagrams library](https://diagrams.mingrammer.com/).

The online documentation of the library contains
additional [icons](https://diagrams.mingrammer.com/docs/nodes/aws), how they can be imported, along
with [complete examples](https://diagrams.mingrammer.com/docs/getting-started/examples).

<!-- TOC -->
* [Doge Enterprise Portal architecture](#doge-enterprise-portal-architecture)
  * [Architecture diagram](#architecture-diagram)
  * [Prerequisites](#prerequisites)
    * [1. Install Python 3 and uv](#1-install-python-3-and-uv)
    * [2. Create a virtual environment with all necessary dependencies](#2-create-a-virtual-environment-with-all-necessary-dependencies)
    * [3. Activate your virtual environment](#3-activate-your-virtual-environment)
  * [Generate architecture diagram](#generate-architecture-diagram)
<!-- TOC -->

## Architecture diagram

![Architecture diagram](doge_enterprise_portal_architecture.png "Architecture diagram")

## Prerequisites

- [Python 3.13.\*](https://www.python.org/downloads/)
- [uv](https://docs.astral.sh/uv/)

### 1. Install Python 3 and uv

**MacOS (using `brew`)**

```bash
brew install python@3.13 uv
```

**Ubuntu/Debian**

```bash
# Install Python 3.13 and pipx
sudo apt install python3.13 python3.13-venv pipx
pipx ensurepath

# Install uv
pipx install uv
```

### 2. Create a virtual environment with all necessary dependencies

From the root of the project execute:

```bash
uv sync
```

### 3. Activate your virtual environment

From the root of the project execute:

```bash
source .venv/bin/activate
```

## Generate architecture diagram

From the root of the project execute:

```bash
python main.py
```
