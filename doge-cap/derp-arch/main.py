from diagrams import Cluster, Diagram
from diagrams.aws.compute import EC2, Fargate, Lambda, ECS
from diagrams.aws.compute import ElasticContainerServiceContainer
from diagrams.aws.database import RDSPostgresqlInstance
from diagrams.aws.general import Client
from diagrams.aws.integration import SNS, SQS
from diagrams.aws.management import Cloudwatch
from diagrams.aws.mobile import Amplify
from diagrams.aws.network import ALB, Route53, Endpoint, NATGateway
from diagrams.aws.security import (
    ACM,
    SecretsManager,
    Cognito,
    IdentityAndAccessManagementIamDataEncryptionKey,
)
from diagrams.aws.storage import S3

with Diagram("Doge Enterprise Portal architecture", show=False):
    client = Client("Client")

    cognito_user_pool_jwks = IdentityAndAccessManagementIamDataEncryptionKey(
        "Cognito User Pool JWKS"
    )

    vpc_gateway_endpoint_service_s3 = S3("S3")
    vpc_interface_endpoint_service_ecs_dkr = ECS("ECS Docker")
    vpc_interface_endpoint_service_ecs_api = ECS("ECS API")
    vpc_interface_endpoint_service_cloudwatch = Cloudwatch("Cloudwatch")
    vpc_interface_endpoint_service_secretsmanager = SecretsManager("Secrets Manager")

    with Cluster("Doge VPC"):
        doge = EC2("Doge")

    with Cluster("Doge Enterprise Portal infrastructure"):
        amplify_ui_integration = Amplify("Amplify integration")
        cognito_user_pool = Cognito("Cognito User Pool")
        derp_ui_dns = Route53("*.zero-cool.co.uk Public Hosted Zone")
        derp_api_dns = Route53("*.api.zero-cool.co.uk Private Hosted Zone")
        certificate_manager = ACM("*.zero-cool.co.uk Custom Certificate")

        sns_topic = SNS("SNS")
        sqs_client = SQS("SQS")

        with Cluster("VPC - tenant-specific"):
            with Cluster("Public subnet"):
                nat_gateway = NATGateway("NAT Gateway")
                application_load_balancer_ui = ALB("UI Application Load Balancer")
            with Cluster("Private subnet"):
                vpc_endpoint = Endpoint("VPC endpoints")
                application_load_balancer_api = ALB("API Application Load Balancer")
                fargate_task_derp_api = Fargate("derp-api")
                fargate_task_derp_ui = Fargate("derp-ui")
                with Cluster("API Target Group"):
                    fargate_containers_api = [
                        ElasticContainerServiceContainer("API Fargate container"),
                        ElasticContainerServiceContainer("API Fargate container"),
                    ]
                with Cluster("UI Target Group"):
                    fargate_containers_ui = [
                        ElasticContainerServiceContainer("UI Fargate container"),
                        ElasticContainerServiceContainer("UI Fargate container"),
                    ]
            with Cluster("Isolated subnet"):
                tenant_database = RDSPostgresqlInstance("Database")
            lambda_add_client_case_to_portal = Lambda("add-client-case-to-portal")

    # Doge to Derp data pipeline
    (
        doge
        >> sns_topic
        >> sqs_client
        >> lambda_add_client_case_to_portal
        >> tenant_database
    )

    # Frontend
    certificate_manager - application_load_balancer_ui
    certificate_manager - derp_ui_dns
    certificate_manager - derp_api_dns
    amplify_ui_integration - application_load_balancer_ui
    cognito_user_pool - amplify_ui_integration

    # VPC endpoints
    fargate_task_derp_api - vpc_endpoint - vpc_interface_endpoint_service_secretsmanager
    fargate_task_derp_api - vpc_endpoint - vpc_interface_endpoint_service_ecs_api
    fargate_task_derp_api - vpc_endpoint - vpc_interface_endpoint_service_ecs_dkr
    fargate_task_derp_api - vpc_endpoint - vpc_gateway_endpoint_service_s3
    fargate_task_derp_api - vpc_endpoint - vpc_interface_endpoint_service_cloudwatch

    (
        lambda_add_client_case_to_portal
        - vpc_endpoint
        - vpc_interface_endpoint_service_secretsmanager
    )

    derp_api_dns - application_load_balancer_api
    derp_ui_dns - application_load_balancer_ui

    (
        client
        >> application_load_balancer_ui
        >> fargate_task_derp_ui
        >> fargate_containers_ui
        >> application_load_balancer_api
        >> fargate_task_derp_api
        >> fargate_containers_api
        >> tenant_database
    )

    # Cognito JWKS integration
    cognito_user_pool - cognito_user_pool_jwks
    fargate_task_derp_api - nat_gateway - cognito_user_pool_jwks
