-- Scoring systems
INSERT INTO public.scoring_systems (id, name)
VALUES (1, 'ContextRiskRating');
INSERT INTO public.scoring_systems (id, name)
VALUES (2, 'CVSS3Rating');
INSERT INTO public.scoring_systems (id, name)
VALUES (3, 'CVSS31Rating');

-- Tests
INSERT INTO public.tests (id, engagement_number, scoring_system_id, name, time_of_last_export, export_status)
VALUES ('4a6a9275-d35f-4e0d-946c-416a19fc479d', 123, 1, 'Example Multi Phase Test', '2024-01-05 11:00:37.121025', 'OK');

-- Phases
INSERT INTO public.phases (guid, number, test_id, name)
VALUES ('8e894cef-ba93-45a7-8046-9bc6a5e6034b', 1, '4a6a9275-d35f-4e0d-946c-416a19fc479d',
        'Web Application Assessment');
INSERT INTO public.phases (guid, number, test_id, name)
VALUES ('bc9daeb9-3b4a-4cd4-a337-89cc9891f86b', 2, '4a6a9275-d35f-4e0d-946c-416a19fc479d', 'Infrastructure Assessment');

-- Findings
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('509800ca-2446-4941-a97e-f3ee14de99b0', 4, '8e894cef-ba93-45a7-8046-9bc6a5e6034b',
        'Encrypted Channel Not Configured', 'HIGH', null, 'I:H/L:M/R:H', 'OPEN',
        '<p>The application did not use a secure HTTPS channel. An attacker who has the ability to intercept or capture network traffic to the application could use freely available tools to eavesdrop on the communication. Any information sent between the user and application would be visible to the attacker, including login credentials, which can be used to compromise customer accounts, and user financial data</p>', e'<p><strong>Use Encrypted Channels for All Network Communications</strong></p>
<p>To reduce the risk of inadvertent information disclosure, social engineering attacks and remote attacks against the application, ensure that all content - including third party content and any analytics services - are retrieved only via HTTPS connections.</p>',
        '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('46723ba7-0541-4dfb-b6e5-a147e0a2dab6', 9, '8e894cef-ba93-45a7-8046-9bc6a5e6034b',
        'Session Remains Active After Logout', 'LOW', null, 'I:L/L:L/R:L', 'OPEN', e'<p>When a user logs out of the application, the session tokens are removed from the HTTP headers using a set-cookie HTTP header to overwrite the cookie value. However, the application does not terminate the session on the server.</p>
<p>If a valid session token can be captured or obtained by an attacker, it can be used to continue to access the session and the user\'s data.</p>', e'<p><strong>Terminate Session Within Server Application</strong></p>
<p>The application should abandon the session and remove it from the active session table. This should occur in response to the following user actions:</p>
<ul>
<li>The user browses away from the site</li>
<li>The user closes the browser</li>
<li>The user logs out of the application</li>
</ul>', '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('143e3236-f6a7-449f-bc1b-e009001eb065', 13, 'bc9daeb9-3b4a-4cd4-a337-89cc9891f86b',
        'Weak SSL/TLS Cipher Suites Supported', 'LOW', null, 'I:M/L:VL/R:L', 'OPEN', e'<p>Web servers within the Core Banking Internals environment support cipher suites that are vulnerable to one or more known weaknesses which increase the exposure of transmitted data.</p>
<p>The following weaknesses were identified:</p>
<ul>
<li>Ciphers with short keys (less than 128 bits) that may be susceptible to brute-force attacks. This includes triple-DES ciphers where the theoretical key length is 168 bits but a meet-in-the-middle attack reduces the effective key length to 112 bits;</li>
<li>Ciphers with small cipher block sizes (64 bits), such as triple-DES ciphers that are vulnerable to collision attacks (SWEET32). The attack relies on the attacker being able to force the browser to generate many thousands of requests via a technique such as cross-site scripting;</li>
<li>CBC-mode ciphers used in conjunction with TLSv1.0, which are vulnerable to the BEAST attack, although the attack also relies on the attacker being able to force the browser to generate many thousands of chosen plaintext through a malicious applet or cross-site scripting. Additionally all modern browsers now provide mitigations for this attack;</li>
<li>RC4 ciphers that are known to introduce biases in the key stream. Over many messages (millions), these ciphers reveal information about the original plain text; RFC 7465 requires that TLS clients and servers never negotiate the use of RC4 cipher suites when they establish connections. As a consequence of the RC4 support, perfect forward secrecy was not provided for some client connections. This means that a compromise of the private key could be used to decrypt previously captured network traffic;</li>
<li>Cipher suites that use key exchange mechanisms, which do not provide Perfect Forward Secrecy (such as would be provided by a Diffie-Hellman key exchange with ephemeral parameters). This means that a compromise of the private key could be used to decrypt previously captured network traffic.</li>
</ul>
<p>An attacker in a position to intercept network traffic between the application and a client when using an insecure cipher suite could exploit these weaknesses to decrypt secure communications without authorisation. As a result the attacker could view information such as login credentials, session tokens and personally identifiable user information.</p>
<p>Services running on 6 ports across 6 hosts are affected by this finding.</p>', e'<p><strong>Remove Support for Weak Ciphers</strong></p>
<p>The web server should be hardened to remove support for weak cipher suites. This will prevent clients from connecting using those cipher suites.</p>
<p>The following guidelines for cipher selection are recommended:</p>
<ul>
<li>Authenticated encryption ciphers (currently those that use AES-GCM) are preferred. These are only available in TLSv1.2 and above.</li>
<li>Cipher suites that use key exchange mechanisms that provide Perfect Forward Secrecy, such as ECDHE and DHE.</li>
<li>Cipher suites that use SHA-2 hash functions and above for Message Authentication Codes (MACs).</li>
<li>AES Ciphers that operate in CBC mode in TLSv1.1 or above.</li>
</ul>
<p>CBC mode ciphers, when used with TLS1.0, are vulnerable to the BEAST attack. Many modern browsers use the split-record mitigation. If the above ciphers cannot be used because of users with legacy browsers then monitoring and blocking large amounts of traffic can help mitigate the BEAST attack.</p>
<p>The following links contain further information on hardening Server SSL configurations</p>
<ul>
<li><a href="https://wiki.mozilla.org/Security/Server_Side_TLS">https://wiki.mozilla.org/Security/Server_Side_TLS</a></li>
<li><a href="https://github.com/ssllabs/research/wiki/SSL-and-TLS-Deployment-Best-Practices">https://github.com/ssllabs/research/wiki/SSL-and-TLS-Deployment-Best-Practices</a></li>
</ul>', e'<table class="Context-T1" style="width: 100%; height: 136px;">
<tbody>
<tr style="height: 18px;">
<td style="width: 50%; height: 18px;">Vulnerability</td>
<td style="width: 50%; height: 18px;">IP:Port</td>
</tr>
<tr style="height: 46px;">
<td style="width: 50%; height: 46px;">
<p><span style="font-size: 10pt;">BEAST Attack</span></p>
</td>
<td style="width: 50%; height: 46px;">
<p><span style="font-size: 10pt;">***************:3389</span></p>
<p><span style="font-size: 10pt;">***************:3389</span></p>
<p><span style="font-size: 10pt;">**************:4122</span></p>
<p><span style="font-size: 10pt;">**************:4122</span></p>
<p><span style="font-size: 10pt;">***************:3389</span></p>
<p><span style="font-size: 10pt;">***************:3389</span></p>
</td>
</tr>
<tr style="height: 18px;">
<td style="width: 50%; height: 18px;">
<p><span style="font-size: 10pt;">POODLE Attack</span></p>
</td>
<td style="width: 50%; height: 18px;">
<p><span style="font-size: 10pt;">***************:3389</span></p>
<span style="font-size: 10pt;">***************:3389</span></td>
</tr>
<tr style="height: 18px;">
<td style="width: 50%; height: 18px;">
<p><span style="font-size: 10pt;">Missing Perfect Forward Secrecy</span></p>
</td>
<td style="width: 50%; height: 18px;">
<p><span style="font-size: 10pt;">***************:3389</span></p>
<p><span style="font-size: 10pt;">***************:3389</span></p>
<p><span style="font-size: 10pt;">**************:4122</span></p>
<p><span style="font-size: 10pt;">**************:4122</span></p>
<p><span style="font-size: 10pt;">***************:3389</span></p>
<span style="font-size: 10pt;">***************:3389</span></td>
</tr>
<tr style="height: 18px;">
<td style="width: 50%; height: 18px;">
<p><span style="font-size: 10pt;">Keystream Bias</span></p>
</td>
<td style="width: 50%; height: 18px;">
<p><span style="font-size: 10pt;">***************:3389</span></p>
<p><span style="font-size: 10pt;">***************:3389</span></p>
<p><span style="font-size: 10pt;">***************:3389</span></p>
<span style="font-size: 10pt;">***************:3389</span></td>
</tr>
<tr style="height: 18px;">
<td style="width: 50%; height: 18px;">
<p><span style="font-size: 10pt;">TripleDES Meet-in-the-Middle</span></p>
<p><span style="font-size: 10pt;">SWEET32</span></p>
</td>
<td style="width: 50%; height: 18px;">
<p><span style="font-size: 10pt;">***************:3389</span></p>
<p><span style="font-size: 10pt;">***************:3389</span></p>
<p><span style="font-size: 10pt;">***************:3389</span></p>
<span style="font-size: 10pt;">***************:3389</span></td>
</tr>
</tbody>
</table>
<p>Please find further information on the identified SSL/TLS issues below:</p>
<p><strong>BEAST Attack</strong>: Ciphers using the Cipher Block Chaining mode of operation in TLSv1.0 and SSLv3 are vulnerable to an attack that could allow for the contents of encrypted communications to be retrieved. For the attack to succeed, an attacker must be able to inject arbitrary plaintext into communications between users and a server, e.g., using a malicious applet or a Cross-Site-Scripting (XSS) vulnerability, and then observe the corresponding cipher texts. Given several thousand requests, an attacker can then use this to determine the subsequent plaintext blocks by encrypting the same messages multiple times. The vulnerability has been fixed as of TLSv1.1 and client-side mitigations have been implemented by all current browsers.</p>
<p><strong>POODLE Attack</strong>: Padding Oracle On Downgraded Legacy Encryption (POODLE) is an attack targeting cipher suites in SSLv3 that use Cipher Block Chaining (CBC). Even if the more secure TLS protocol is enabled, backward compatibility with SSLv3 allows an attacker to force the downgrade of the encryption protocol from TLSv1.0 to SSLv3. Once the downgrade attack is complete the padding oracle attack may continue. This combination is known as the POODLE attack.&nbsp; The feasibility of the attack is limited by several preconditions that must be met. Padding Oracle On Downgraded Legacy Encryption. For more details on the attack please refer to https://www.openssl.org/~bodo/ssl-poodle.pdf. Many modern browsers have SSLv3 disabled completely (e.g. Firefox, Chrome 40) or disable fallback to SSLv3. This partially mitigates the problem but does not prevent users with older browsers from being exposed to such attacks. Hence SSLv3 should be explicitly disabled on the server-side.</p>
<p><strong>Missing Perfect Forward Secrecy</strong>: Ciphers that provide perfect forward secrecy (PFS) use the Diffie-Hellman key exchange to establish a temporary session key that is used for further data encryption. Without this the client and the server will agree on a symmetric master secret, which is used to generate the session keys. This means that if an attacker were able to gain access to the web server\'s private key, they could decrypt all encrypted communications that had previously been sent between the user and the application.</p>
<p><strong>Keystream Bias</strong>: The RC4 cipher has statistical biases in the random number generator (RNG). The byte stream produced by this RNG contains insufficient entropy. If a plaintext is encrypted repeatedly (as is the case for HTTP cookies), an attacker could use several million ciphertexts to recover the plaintext of the encrypted connections.</p>
<p><strong>TripleDES Meet-in-the-Middle</strong>: Although a first glance 3DES in encrypt-decrypt-encrypt (EDE) mode would suggest it to have a security strength of 168 bit (3x56 bit key length), the effective strength can be reduced to 112 bit by using a precomputing attack termed &ldquo;Meet-In-The-Middle&rdquo;. While there are currently no known practical attacks leveraging this, the effective strength is below the recommended minimum of 128 bit and it is recommended to no longer use this cipher.</p>
<p><strong>Sweet32 Collision Attacks</strong>: The DES ciphers (and triple-DES) only have a 64-bit block size. This enables an attacker to run JavaScript in a browser and send large amounts of traffic during the same TLS connection, creating a collision. With this collision, the attacker is able to retrieve information from a session cookie.</p>');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('285cca75-13b3-4deb-ba50-62fa3bae6714', 1, '8e894cef-ba93-45a7-8046-9bc6a5e6034b', 'SQL Injection', 'CRITICAL',
        null, 'I:C/L:H/R:C', 'OPEN', e'<p>The web application suffers from an SQL injection vulnerability within the payments functionality &ldquo;Limit&rdquo; parameter. This allows an attacker to manipulate an SQL query within the application by sending additional SQL commands to the application server.</p>
<p>An attacker can use this to perform malicious tasks such as extract or change sensitive information within the database supporting the application, and potentially run system commands on the database host.</p>
<p>The injection point was error based; it is possible to see the response of SQL queries within an error message returned by the application. Using this vulnerability it was possible to enumerate the database and extract content such as account numbers, usernames, and passwords.</p>', e'<p><strong>Use Prepared Statements &amp; Validate Input</strong></p>
<p>Use prepared statements with parameterised queries to prevent SQL injection. With prepared statements, parameter values (\'bind variables\') are substituted for placeholders in query templates after the database has parsed the statement. This means they cannot be used to change the structure of the query. This requires that the initial template is not derived from external input.</p>
<p>In some cases, a dynamic value cannot be used for a parameter. Refactor the code to avoid the need to do this. If this is not possible, correct input validation will provide a defence.</p>
<p>Input validation should always be used in addition to prepared statements. External input should be validated against an allow list of expected values, regardless of whether it comes from a perceived trusted source or not. This is a fundamental secure coding principle that would prevent potentially malicious code being written to the database (for example, via second-order SQLi).</p>
<p>Finally, ensure the database is sufficiently hardened and accessed with a minimal set of privileges in case SQLi is achieved.</p>
<p>Further information can be found below:</p>
<ul>
<li><a href="https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html">https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html</a></li>
<li><a href="https://cheatsheetseries.owasp.org/cheatsheets/Query_Parameterization_Cheat_Sheet.html">https://cheatsheetseries.owasp.org/cheatsheets/Query_Parameterization_Cheat_Sheet.html</a></li>
</ul>', e'<p>The "parameter x" parameter of the payment functionality was found to be vulnerable to error based SQL injection. When a non-alphabetic character is provided as a value for the "payeename" parameter the application was observed to throw an SQL error. It is therefore possible to enter SQL queries as a value for this parameter which is then run by the database and the output is returned within the error message. For example the following SQL query returns the current database user, "admin":</p>
<pre class="http hljs"><span class="hljs-keyword">POST</span> <span class="hljs-string">/SomeDatabaseHandler</span> HTTP/1.1
<span class="hljs-attribute">Host</span>: a-fake-server.contextis.com
<span class="hljs-attribute">User-Agent</span>: Mozilla/5.0 (Windows NT 10.0; WOW64; rv:58.0) Gecko/20100101 Firefox/58.0
<span class="hljs-attribute">Accept</span>: text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8
<span class="hljs-attribute">Accept-Language</span>: en-GB,en;q=0.5
<span class="hljs-attribute">Accept-Encoding</span>: gzip, deflate
<span class="hljs-attribute">Referer</span>: another-fake-domain.contextis.com
<span class="hljs-attribute">Content-Type</span>: text/html
<span class="hljs-attribute">Content-Length</span>: 7056
<span class="hljs-attribute">Cookie</span>: AnAuthCookie=aaaabbbbbbbbbbbcccccccdddddddd;
<span class="hljs-attribute">Connection</span>: close
<span class="hljs-attribute">Upgrade-Insecure-Requests</span>: 1

<span class="lisp">limit=(<span class="hljs-name">CAST</span>((<span class="hljs-name">CHR</span>(<span class="hljs-number">123</span>)||CHR(<span class="hljs-number">123</span>)||CHR(<span class="hljs-number">123</span>)||CHR(<span class="hljs-number">123</span>)||CHR(<span class="hljs-number">123</span>))||(<span class="hljs-name">COALESCE</span>(<span class="hljs-name">CAST</span>(<span class="hljs-name">CURRENT_USER</span> AS CHARACTER(<span class="hljs-number">10000</span>)),(<span class="hljs-name">CHR</span>(<span class="hljs-number">32</span>)))):<span class="hljs-symbol">:text</span>||(<span class="hljs-name">CHR</span>(<span class="hljs-number">123</span>)||CHR(<span class="hljs-number">123</span>)||CHR(<span class="hljs-number">123</span>)||CHR(<span class="hljs-number">123</span>)||CHR(<span class="hljs-number">123</span>)) AS NUMERIC))
</span></pre>
<p>The following error is returned containing the returned value (highlighted):</p>
<pre class="http hljs">HTTP/1.1 <span class="hljs-number">500</span> OK
<span class="hljs-attribute">Server</span>: Apache 2.4.54
<span class="hljs-attribute">Date</span>: Wed, 13 Mar 2019 11:04:51 GMT
<span class="hljs-attribute">Content-Type</span>: text/html; charset=utf-8
<span class="hljs-attribute">Content-Length</span>: 64387
<span class="hljs-attribute">Connection</span>: close
<span class="hljs-attribute">Set-Cookie</span>: AnAuthCookie=aaabbbcccc; secure
<span class="hljs-attribute">Pragma</span>: no-cache
<span class="hljs-attribute">Cache-Control</span>: no-cache no-store must-revalidate proxy-revalidate
<span class="hljs-attribute">X-Frame-Options</span>: SAMEORIGIN
<span class="hljs-attribute">Strict-Transport-Security</span>: max-age=********; includeSubdomains;

<span class="elm"><span class="hljs-type">Error</span>: invalid input syntax for <span class="hljs-keyword">type</span> numeric &ldquo;{{{{{<span class="highlighted">admin</span>{{{{{&ldquo; during <span class="hljs-type">SQL</span> [select payeename from banking where (payeename ilike &lsquo;% %&rsquo;) order by payeename limit (<span class="hljs-type">CAST</span>((<span class="hljs-type">CHR(123)</span>||<span class="hljs-type">CHR(123)</span>||<span class="hljs-type">CHR(123)</span>||(<span class="hljs-type">COALESCE</span>(<span class="hljs-type">CAST</span>(<span class="hljs-type">CURRENT_USER</span> <span class="hljs-type">AS</span> <span class="hljs-type">CHARACTER(10000)</span>),(<span class="hljs-type">CHR(32)</span>))))::text||(<span class="hljs-type">CHR(123)</span>||<span class="hljs-type">CHR(123)</span>||<span class="hljs-type">CHR(123)</span><span class="hljs-type">CHR(123)</span>||<span class="hljs-type">CHR(123)</span> <span class="hljs-type">AS</span> <span class="hljs-type">NUMERIC</span>))]
</span></pre>
<p>By using this method it was possible to extract information from the database as shown below:</p>
<table class="Context-T1" style="width: 95.3459%; height: 72px;">
<tbody>
<tr style="height: 18px;">
<td style="width: 56.3612%; height: 18px;"><strong>Information</strong></td>
<td style="width: 61.2062%; height: 18px;"><strong>Value</strong></td>
</tr>
<tr style="height: 18px;">
<td style="width: 56.3612%; height: 18px;">Back-End DBMS Operating System</td>
<td style="width: 61.2062%; height: 18px;">Linux Ubuntu</td>
</tr>
<tr style="height: 18px;">
<td style="width: 56.3612%; height: 18px;">Back-End DBMS</td>
<td style="width: 61.2062%; height: 18px;">PostgreSQL</td>
</tr>
<tr style="height: 18px;">
<td style="width: 56.3612%; height: 18px;">Current Database User</td>
<td style="width: 61.2062%; height: 18px;">admin</td>
</tr>
</tbody>
</table>
<p>&nbsp;</p>
<p>Furthermore, Accenture was able to dump the contents of the &lsquo;User&rsquo;s table from the &lsquo;banking&rsquo; database using this method. This contained all application users&rsquo; credentials, account numbers and account balance. A number of users were found to have the same password hash indicating they are using the same password and that it has not been salted. A sample is shown below:</p>
<table class="Context-T1" style="width: 95.4844%; height: 72px;">
<tbody>
<tr style="height: 18px;">
<td style="width: 15.8617%; height: 18px;">Username</td>
<td style="width: 28.344%; height: 18px;">Password Hash</td>
<td style="width: 23.932%; height: 18px;">Account Number</td>
<td style="width: 27.3486%; height: 18px;">Account Balance ($)</td>
</tr>
<tr style="height: 18px;">
<td style="width: 15.8617%; height: 18px;">User1</td>
<td style="width: 28.344%; height: 18px;">abcdefghijklmnop123</td>
<td style="width: 23.932%; height: 18px;">********</td>
<td style="width: 27.3486%; height: 18px;">1000</td>
</tr>
<tr style="height: 18px;">
<td style="width: 15.8617%; height: 18px;">User2</td>
<td style="width: 28.344%; height: 18px;">sjducsdefasdcvvfd123</td>
<td style="width: 23.932%; height: 18px;">********</td>
<td style="width: 27.3486%; height: 18px;">30000</td>
</tr>
<tr style="height: 18px;">
<td style="width: 15.8617%; height: 18px;">User3</td>
<td style="width: 28.344%; height: 18px;">abcdefghijklmnop123</td>
<td style="width: 23.932%; height: 18px;">********</td>
<td style="width: 27.3486%; height: 18px;">5000</td>
</tr>
</tbody>
</table>');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('0a861ef0-98d5-4f0a-91e5-e05903e51ffa', 2, '8e894cef-ba93-45a7-8046-9bc6a5e6034b',
        'Application Passwords Hashed With No Salt', 'HIGH', null, 'I:H/L:H/R:H', 'OPEN', e'<p>The web application did not salt the password hash before storing them within the "banking" database.&nbsp;<br /><br />If an attacker was able to compromise the database in a similar way, it may be trivial to perform a dictionary attack on the un-salted hashed passwords to retrieve their original clear-text value.</p>
<p>This issue was discovered following compromise of the database outlined in issue&nbsp;<span class="link-to" data-node-id="4674" data-format-string="{finding_id} - {title}">123-1-01 - SQL Injection</span></p>', e'<p><strong>Hash Passwords Using Salt</strong></p>
<p>The application should store the user\'s password in a secure manner. This prevents an attacker from easily gaining access to plain text passwords in the event that the system is compromised.</p>
<p>Passwords should be stored using an adaptive hashing algorithm such as PBKDF2 or bcrypt which are specifically designed for the storage of passwords and are far more resilient to brute force attacks than standard hashing algorithms such as MD5, SHA-1, SHA-256 etc. If the use of an adaptive hashing algorithm cannot be implemented then Accenture recommends the use of SHA-256 salted with a unique randomly generated string.</p>',
        '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('e47d66a5-2377-4caa-bfe2-48eb2f68db30', 5, '8e894cef-ba93-45a7-8046-9bc6a5e6034b',
        'Default Administrative Credentials in Use', 'HIGH', null, 'I:H/L:M/R:H', 'OPEN', e'<p>It was possible to authenticate to the Splunk Management Console within the Core Banking Internals environment using default credentials. This application is used for remote monitoring and log centralisation of the hosts within Core Banking Internals.</p>
<p>Admin access to Splunk provides an attacker with the ability to execute arbitrary code on the Splunk server itself as well as other systems within the environment by creating and running malicious Splunk applications. These applications can be deployed to every client that is connected to the Splunk management console. This can lead to a widespread privileged compromise of the Core Banking Internals environment.</p>
<p>Services running on 1 port on 1 host are affected by this finding.</p>', e'<p><strong>Restrict Access to Management Interfaces, and Change Default Credentials</strong></p>
<p>Although only accessible from within the Core Banking Internals environment, ECI should consider further restricting access to the Splunk interface to just admin users who require access to the service.&nbsp;This can be achieved by ensuring the Splunk service is only reachable from a management network with appropriate network controls in place to.</p>
<p>Additionally, ensure the password associated with the "Admin" account is changed from the defaults, such that the new password:</p>
<ul>
<li>Is of at least 8 characters in length;</li>
<li>Contains upper- and lower-case alphanumeric characters, and symbols; and</li>
<li>Is not a commonly-used password value, such as \'password\', the username, or the organisation name, or iterations thereof.</li>
</ul>', e'<p>The following screenshot shows the Splunk Management Interface after logging in with default credentials. An attacker who manages to log in could obtain all centralised log files that could contain sensitive PII data like credentials and addresses, or sensitive corporate information:</p>
<p><img class="image-border" src="/files/images/4a6a9275-d35f-4e0d-946c-416a19fc479d/14700133062f890178e5a329ade7a167.png" /></p>');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('b3c9c9c4-e4a2-4796-b2ea-da5ce955227b', 6, '8e894cef-ba93-45a7-8046-9bc6a5e6034b',
        'Cookie Set without ''HttpOnly'' Attribute', 'LOW', null, 'I:M/L:L/R:L', 'OPEN', e'<p>The web application&nbsp;does not have the HttpOnly attribute set on the cookies value. As a result, client side JavaScript would be able to access the value of the cookie.&nbsp;For example, the value of "document.cookie&rdquo; will be blank, preventing cross-site scripting attacks from accessing and "stealing" the session token.</p>
<p>The following sensitive cookies were found to not implement the HTTPOnly attribute:</p>
<pre class="http hljs"><span class="hljs-attribute">Set-Cookie</span>: cookie=xxxxxxxxxxxxx; Path=/; Secure
</pre>', e'<p><strong>Set \'HttpOnly\' Cookie Attribute</strong></p>
<p>Sensitive session cookies should not be accessible by browser scripts (such as JavaScript). This can be achieved by including the &lsquo;HttpOnly&rsquo; attribute when setting the cookie using HTTP headers and can also be combined with other cookie security attributes, as follows:</p>
<pre class="nohighlight">Set-Cookie: ESSID=xxxxxxxxxxx; Path=/; HttpOnly; Secure; SameSite=Lax</pre>', '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('33522b2c-5149-4fcc-a881-c02c7487f821', 8, '8e894cef-ba93-45a7-8046-9bc6a5e6034b',
        'HTTP Header Discloses Software Version Details', 'LOW', null, 'I:L/L:M/R:L', 'OPEN', e'<p>The web application&nbsp;discloses the web server software name and version details in the server header of the HTTP responses. An attacker could use this information during the reconnaissance phase to determine possible attack vectors and vulnerabilities associated with the web server and application.</p>
<p>The web server returns the following HTTP headers:</p>
<pre>Server: Apache 2.4.54</pre>
<p>&nbsp;</p>', '', e'<p><strong>Remove Web Banners</strong></p>
<p>Any HTTP banners that reveal software version details should be removed. This can be achieved through the use of a Web Application Firewall, Reverse Proxy or similar technology.</p>
<p>For Apache, detailed information in the Server header banner can be minimised by adding the following lines to the httpd.conf file:</p>
<pre>ServerSignature Off
ServerTokens Prod</pre>
<p>The ServerTokens Prod line removes version details from the banner header, whilst the ServerSignature removes Apache version details from error pages and other generated pages.</p>
<p>Apache offers no built-in method to completely remove the Server header. In order to completely remove it, a Web Application Firewall or Reverse Proxy can be used to filter out the response header. For example, the Apache module "mod_security" allows you to manipulate the header by using the \'SecServerSignature\' directive.</p>');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('e348d42f-d28c-4042-b7a9-1ec867bcd87d', 10, '8e894cef-ba93-45a7-8046-9bc6a5e6034b',
        'RC4 Cipher Suites supported', 'MINIMAL', null, 'I:V/L:L/R:MI', 'OPEN', e'<p>The IIS host was found to support weak cipher suites that are vulnerable to attacks that increase the exposure of the transmitted data.</p>
<p>RC4 is a stream cipher that has known practical attacks.</p>', e'<p><strong>Disable RC4 Cipher Suites</strong></p>
<p>To disable RC4 40/128, ensure the following key is absent. If the key is present, ensure it is set to 0.</p>
<pre>HKLM \\System\\CurrentControlSet\\Control\\SecurityProviders\\SCHANNEL\\Ciphers\\RC4 40/128\\Enabled</pre>
<p>To disable RC4 56/128, ensure the following key is absent. If the key is present, ensure it is set to 0.</p>
<pre>HKLM\\System\\CurrentControlSet\\Control\\SecurityProviders\\SCHANNEL\\Ciphers\\RC4 56/128\\Enabled  </pre>
<p>To disable RC4 64/128, ensure the following key is absent. If the key is present, ensure it is set to 0.</p>
<pre>HKLM\\System\\CurrentControlSet\\Control\\SecurityProviders\\SCHANNEL\\Ciphers\\RC4 64/128\\Enabled </pre>
<p>To disable RC4 128/128, ensure the following key is set to 0. RC4 128/128 is not enabled by default on Server 2008 SP2 but is enabled by default on R2.</p>
<pre>HKLM\\System\\CurrentControlSet\\Control\\SecurityProviders\\SCHANNEL\\Ciphers\\RC4 128/128\\Enabled</pre>
<p>Please refer to the following Microsoft support article for more details:</p>
<ul>
<li><a href="https://docs.microsoft.com/en-us/troubleshoot/windows-server/windows-security/restrict-cryptographic-algorithms-protocols-schannel">https://docs.microsoft.com/en-us/troubleshoot/windows-server/windows-security/restrict-cryptographic-algorithms-protocols-schannel</a></li>
</ul>', '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('4b6591bb-b0b9-45bf-b3cc-12824ed49359', 7, '8e894cef-ba93-45a7-8046-9bc6a5e6034b',
        'Denial of Service - Application Lockout', 'LOW', null, 'I:M/L:L/R:L', 'OPEN', e'<p>The web application performed a complete account lockout after 15 consecutive failed attempts. This leaves the application vulnerable to a Denial-of-Service (DoS) condition.</p>
<p>An attacker could perform an automated attack to lockout all valid users preventing legitimate access to the application. User accounts would then be unlocked by company\'s help desk, potentially impacting on operational work.</p>', e'<p><strong>Implement Anti-Automation Controls on Password Failure</strong></p>
<p>Consider requiring users to solve an obscured text to graphic &lsquo;CATPCHA&rsquo; puzzle when submitting their password if they have failed to authenticate successfully a defined number of times (e.g. three failed login attempts).</p>
<p>This will reduce the effectiveness of automated attacks aimed at intentionally locking users out of their accounts, whilst also preventing automated password guessing attacks. Attempts should also be logged for investigation purposes</p>
<p>Also, consider formulating a lockout policy such that a user is locked out for a period of time in response to multiple authentication failures. A value between 3 and 5 is commonly considered sufficient. The account is then re-enabled after a set amount of time to remove the ability for a malicious user to create a Denial-of-Service scenario through an automated brute force attack. This also minimises the requirement for administrative support to re-enable locked accounts for legitimate users. Furthermore, consider locking out the account for increasing periods of time, following subsequent failed login attacks (i.e. 3rd invalid attempt = 5 min lockout; 6th = 10 min; 9th = 15 min; and so on). This deters manual password guessing attempts and makes automated assaults impractical.</p>',
        '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('0d4658e4-e3b1-4774-a07c-d73482b66e17', 3, '8e894cef-ba93-45a7-8046-9bc6a5e6034b',
        'Non-Persistent Cross-Site Scripting', 'HIGH', null, 'I:H/L:M/R:H', 'OPEN', e'<p>The web application is vulnerable to non-persistent Cross-Site Scripting (XSS) attacks.</p>
<p>Cross-Site Scripting (XSS) occurs when HTML and/or JavaScript is injected into the application and is rendered in the victim\'s browser without being safely encoded. In a non-persistent XSS attack, an attacker could embed malicious scripts in the affected parameters that could be used to&nbsp;take control of users\' sessions, steal credentials or other sensitive data, or deliver further payloads to attack the victim\'s client applications.</p>
<p>A successful attack would require some form of social engineering, such as enticing a user to access a malicious link or website.</p>', e'<p><strong>Validate User Input and HTML Encode Output</strong></p>
<p>Limit client-supplied input to what is reasonable for the type of information being provided. For example, many user input fields could be restricted to solely alphanumeric characters. This solves many input validation issues besides Cross-Site Scripting.</p>
<p>If non-alphanumeric characters are required, encode them as HTML entities before using them in an HTTP response, so that they cannot be used to modify the structure of the HTML document.</p>
<p>Modern frameworks provide high-level widgets to abstract the developer from the underlying dynamic HTML creation. This can be used to prevent XSS by automatically encoding HTML entities that are placed into their value fields.</p>
<p>Further information can be found at the following location:</p>
<ul>
<li><a href="https://cheatsheetseries.owasp.org/cheatsheets/Input_Validation_Cheat_Sheet.html">https://cheatsheetseries.owasp.org/cheatsheets/Input_Validation_Cheat_Sheet.html</a></li>
<li><a href="https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html">https://cheatsheetseries.owasp.org/cheatsheets/Cross_Site_Scripting_Prevention_Cheat_Sheet.html</a></li>
</ul>
<p>&nbsp;</p>', e'<p>A number of locations within the online banking application were found to be vulnerable to non-persistent Cross-Site Scripting (XSS). The following example was observed within the person parameter of the Account Details functionality. It is possible to set the value of this parameter to include HTML and JavaScript which is rendered within the browser when the URL is loaded:</p>
<p><img class="image-border" src="/files/images/4a6a9275-d35f-4e0d-946c-416a19fc479d/dcf991376c81caeac22a5c9d353a104e.png" /></p>
<p>The following table contains the locations of additional identified instances and the parameters which are vulnerable:</p>
<table class="Context-T1" style="width: 100%;">
<tbody>
<tr>
<td style="width: 33.3333%;"><strong>Functionality</strong></td>
<td style="width: 33.3333%;"><strong>Example URL</strong></td>
<td style="width: 33.3333%;"><strong>Affected Parameters</strong></td>
</tr>
<tr>
<td style="width: 33.3333%;">Payments</td>
<td style="width: 33.3333%;">www.context-bank.com/payments.cgi?payee=a"&gt;&lt;script src=\'http://www.contextis.co.uk/alertbox/xss.js\'&gt;&lt;/script&gt;</td>
<td style="width: 33.3333%;">payee</td>
</tr>
<tr>
<td style="width: 33.3333%;">Transfers</td>
<td style="width: 33.3333%;">www.context-bank.com/transfers.cgi?account=a"&gt;&lt;script src=\'http://www.contextis.co.uk/alertbox/xss.js\'&gt;&lt;/script&gt;</td>
<td style="width: 33.3333%;">account</td>
</tr>
<tr>
<td style="width: 33.3333%;">Savings</td>
<td style="width: 33.3333%;">www.context-bank.com/savings.cgi?savings_type=a"&gt;&lt;script src=\'http://www.contextis.co.uk/alertbox/xss.js\'&gt;&lt;/script&gt;</td>
<td style="width: 33.3333%;">savings_type</td>
</tr>
</tbody>
</table>');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('98feba09-84b8-4556-91d4-33278f39ec1f', 8, 'bc9daeb9-3b4a-4cd4-a337-89cc9891f86b',
        'Vulnerable Version of PostgreSQL in Use', 'MEDIUM', null, 'I:M/L:H/R:M', 'OPEN', e'<p>The version of PostgreSQL database in use by the web application was identified as 9.1.4. The version was identified by querying the database via the SQL injection vulnerability described in&nbsp;<span class="link-to" data-node-id="4674" data-format-string="{finding_id} - {title}">123-1-01 - SQL Injection</span>. The latest version of PostgreSQL 9.1.x branch is 9.1.13. A number of vulnerabilities exist in the installed version that have been rectified in later versions including the following:</p>
<ul>
<li>Multiple denial of service vulnerabilities (CVE-2013-1899, CVE-2014-0066)</li>
<li>Multiple Code Execution Vulnerabilities (CVE-2013-1899, CVE -2014-0063)</li>
</ul>
<p>Further information can be found here:</p>
<ul>
<li><a href="http://www.postgresql.org/support/security/">http://www.postgresql.org/support/security/</a></li>
</ul>', e'<p><strong>Upgrade to Latest Version</strong></p>
<p>The database be upgraded to the latest stable release of PostgreSQL in order to address existing vulnerabilities.</p>
<p>Furthermore, review the patching policy in use to ensure that future security updates and releases are identified and applied on a regular basis.</p>',
        '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('933bd069-9ba8-4a26-8a11-cdebdd5fad2d', 1, 'bc9daeb9-3b4a-4cd4-a337-89cc9891f86b',
        'Encrypted Channel Not In Use', 'HIGH', null, 'I:H/L:M/R:H', 'OPEN', e'<p>The services affected were running an Apache Tomcat interface, which is accessible over an unencrypted channel. As such, an attacker with access to the Core Banking Internals environment could use freely available tools to eavesdrop on the communications of users authenticating to the Tomcat server. Any information submitted to the server, including login credentials would be visible to the attacker.</p>
<p>If a user with manager role privileges was compromised via this attack then the attacker could upload a WAR archive containing a .jsp file, which will execute commands on the underlying host. This would result in an attacker gaining unprivileged local access to the server.</p>
<p>Services running on 5 ports across 5 hosts are affected by this finding.</p>', e'<p><strong>Use Encrypted Channels for All Network Communications</strong></p>
<p>Tomcat should be configured to use TLS. TLS should be configured to use a set of strong cipher suites which offer Perfect-Forward Secrecy. Furthermore, it is recommended to use at least TLS 1.2. For detailed instructions refer to the following URL:</p>
<ul>
<li><a href="https://tomcat.apache.org/tomcat-8.0-doc/ssl-howto.html">https://tomcat.apache.org/tomcat-8.0-doc/ssl-howto.html</a></li>
</ul>', '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('b8e8caa7-13ce-4c8b-9d99-d53c68ca2d07', 2, 'bc9daeb9-3b4a-4cd4-a337-89cc9891f86b',
        'Unsupported Operating System', 'HIGH', null, 'I:H/L:M/R:H', 'OPEN', e'<p>The affected host was found to be running Microsoft Windows Server 2003, which ceased support by Microsoft on July 14, 2015.</p>
<p>Consequently, Microsoft will not provide patches for any newly identified security vulnerabilities in this version of the operating system. This will make it impossible to protect against new threats, in addition to being vulnerable to the issues that were disclosed after the end of support date.</p>
<p>There are an extensive number of public vulnerabilities, for a list of some of these see:</p>
<ul>
<li><a href="https://www.cvedetails.com/vulnerability-list/vendor_id-26/product_id-7108/Microsoft-Windows-Server-2003.html">https://www.cvedetails.com/vulnerability-list/vendor_id-26/product_id-7108/Microsoft-Windows-Server-2003.html</a></li>
</ul>
<p>These vulnerabilities would allow an attacker given time and access to the Core Banking Internals network, the ability to target the affected systems, cause denial of service or remotely execute code in some cases.</p>
<p>For more information on the Windows Server 2003 product lifecycle see:</p>
<ul>
<li><a href="https://support.microsoft.com/en-gb/lifecycle/search?alpha=Windows%20Server%202003">https://support.microsoft.com/en-gb/lifecycle/search?alpha=Windows%20Server%202003</a></li>
</ul>
<p>Services running on 5 ports across 5 hosts are affected by this finding.</p>', e'<p><strong>Update Operating System</strong></p>
<p>The operating system on the systems should be updated to a currently supported edition such as Windows Server 2019.</p>
<p>Additionally, the patching policy should be reviewed to ensure applications and operating systems are replaced before extended support for them ceases.</p>',
        '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('37f00db9-749a-4867-8384-2ab405a82a7a', 3, 'bc9daeb9-3b4a-4cd4-a337-89cc9891f86b',
        'IPMI Password Hash Disclosure', 'HIGH', null, 'I:H/L:M/R:H', 'OPEN', e'<p>The assessed system supported Intelligent Platform Management Interface (IPMI) functionality, which allows the retrieval of RMCP+ Authenticated Key-Exchange Protocol (RAKP) authentication hashes from the network.</p>
<p>As a result, an attacker located within the corporate network is able to obtain hashed user password values which can be targeted as part of an offline brute-force attack in an attempt to gain plaintext passwords. If successful, the attacker can authenticate to services located on each host, which can be used as part of a wider attack against each host.</p>
<p>During the assessment, the hash for the Administrator user was extracted, however during the testing window it was not possible to crack the hash. Following discussions with system administrators, the password was shown to be very complex. It would take an attacker a reasonable amount of time to compromise this account.</p>
<p>Services running on 1 port on 1 host are affected by this finding.</p>', e'<p><strong>Disable IPMI If Not Required, Use Strong Passwords, and Implement Network Access Controls</strong></p>
<p>The passwords of the accounts compromised during the engagement should be changed to ensure that any compromise of that password is nullified.</p>
<p>In addition, consider the requirement to support IPMI functionality and, if not required, ensure it is disabled. If it is required, ensure all users set suitably complex passwords, which should:</p>
<ul>
<li>Be of at least 12 characters in length;</li>
<li>Contain upper- and lower-case alphanumeric characters and symbols; and</li>
<li>Not resemble commonly-used passwords, the organisation or system name, or the user\'s username.</li>
</ul>
<p>Finally, ensure network access control lists are implemented to prevent access to IPMI services from the corporate network, ensuring access is only possible from trusted network devices with specific IP addresses.</p>', e'<p>It was possible to extract the Administrator hash from the affected host using a metasploit module as shown below:</p>
<pre class="bash hljs">msf5 auxiliary(scanner/ipmi/ipmi_dumphashes) &gt; <span class="hljs-built_in">set</span> RHOSTS xxx.xxx.xxx.xxx
RHOSTS =&gt; ************
msf5 auxiliary(scanner/ipmi/ipmi_dumphashes) &gt; run

[+] ************:623 - IPMI - Hash found: Administrator:{<span class="hljs-built_in">hash</span> redacted <span class="hljs-keyword">for</span> report}
[*] Scanned 1 of 1 hosts (100% complete)
[*] Auxiliary module execution completed
</pre>');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('b75558bc-94f7-42ff-836d-3aadb177e44e', 4, 'bc9daeb9-3b4a-4cd4-a337-89cc9891f86b', 'SMBv1 Enabled', 'HIGH',
        null, 'I:H/L:M/R:H', 'OPEN', e'<p>Windows hosts within the Core Banking Internals environment are configured to support version 1 of the SMB protocol. SMBv1 does not include security and performance features that have been introduced into subsequent versions, including those that guard against intercept-attacks; increasing the risk of potentially sensitive data being exposed to a network-based attacker.</p>
<p>Additionally, the SMBv1 implementation in Windows has been the target of multiple serious vulnerabilities; and advice from Microsoft is that it should be disabled to reduce the attack surface of the system.</p>
<p>Please note; due to the presence of legacy systems in the environment (<span data-format-string="{finding_id}" data-node-id="28589">12345-2-03</span>), testing should be carried out to ensure disabling SMBv1 does not impact any existing services or workflows.</p>
<p>Services running on 1 port on 1 host are affected by this finding.</p>', e'<p><strong>Consider Disabling Support for SMBv1</strong></p>
<p>Consideration should be given for disabling version 1 of the SMB protocol on the system.</p>
<p>It should however be noted that several appliances utilise SMBv1 to communicate with Windows systems, including web filtering gateways that use it for NTLM authentication; as well as file storage and backup solutions. Therefore thorough testing should be conducted prior to disabling SMBv1 across the estate.</p>
<p>To disable SMBv1, the following registry key should be created and set to "0":</p>
<pre>HKEY_LOCAL_MACHINE\\SYSTEM\\CurrentControlSet\\Services\\LanmanServer\\Parameters\\SMB1</pre>
<p>This can be deployed to multiple machines using Group Policy Preferences.</p>
<p>More detailed information on how to disable SMBv1 can be found at:</p>
<ul>
<li><a href="https://support.microsoft.com/en-us/help/2696547/how-to-enable-and-disable-smbv1-smbv2-and-smbv3-in-windows-and-windows-server">https://support.microsoft.com/en-us/help/2696547/how-to-enable-and-disable-smbv1-smbv2-and-smbv3-in-windows-and-windows-server</a></li>
</ul>', '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('f22025f1-8dd9-42a7-99eb-c4598c0a8533', 5, 'bc9daeb9-3b4a-4cd4-a337-89cc9891f86b',
        'Ineffective Network Segregation', 'MEDIUM', null, 'I:H/L:L/R:M', 'OPEN', e'<p>A scan of the infrastructure was conducted from the corporate network in order to identify which services a normal user is able to view. The result of this showed that corporate users had limited access to the Core Banking Internals environment, but still more than is deemed essential. For example, management interfaces for windows servers, IPMI and Cisco gateways were accessible. This provides a malicious actor on the corporate network an opportunity to conduct attacks with a potential for high impact.</p>
<p>A user could target IPMI in order to gain access to the password used for (something). In addition if an attacker has access to credentials for the Windows host (INTL-CODNAME-JUMBOX-2) this would provide them the ability to pivot into the secure Core Banking Internals environment and then exploit additional findings such as&nbsp;<span class="link-to" data-node-id="4677" data-format-string="{finding_id} - {title}">123-1-04 - Encrypted Channel Not Configured</span>,&nbsp;<span class="link-to" data-node-id="4681" data-format-string="{finding_id} - {title}">123-2-03 - IPMI Password Hash Disclosure</span> and <span class="link-to" data-node-id="4682" data-format-string="{finding_id} - {title}">123-2-04 - SMBv1 Enabled</span>.&nbsp;</p>
<p>Services running on 7 ports across 7 hosts are affected by this finding.</p>', e'<p><strong>Review And Segregate Network Architecture</strong></p>
<p>Review the network layout and consider further segregating systems to prevent unauthorised users from accessing sensitive systems. Access to management interfaces for IPMI, CISCO and Windows jump boxes should be prevented from the corporate LAN. Instead access should be restricted to administrative users, operating from a specific administrative network or hosts.</p>',
        '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('4b7887a8-371f-4fac-b2b8-02e1b9232c24', 6, 'bc9daeb9-3b4a-4cd4-a337-89cc9891f86b',
        'Apache Server No Longer Supported', 'MEDIUM', null, 'I:H/L:L/R:M', 'OPEN', e'<p>The version of Apache running on the host was identified as 2.2.31. The 2.2.x branch of Apache HTTP server is no longer supported and will not receive patches for any newly identified security vulnerabilities. This will make it impossible to protect against new threats.</p>
<p>In addition, known weaknesses are present in this version of Apache for which there is public exploitation code. For example CVE-2017-9798, also known as "Optionsbleed" which could allow an attacker to retrieve pieces of arbitrary memory from the server. The data returned changes after multiple requests allowing an attacker to retrieve an arbitrary amount of memory chunks. This memory could include sensitive financial information, credentials or session tokens that permit an attacker to gain access to ECI customer data.</p>
<p>Please note; the likelihood rating has been decreased due to the affected services not utilising the OPTIONS HTTP verb.</p>
<p>For further information see the following URL:</p>
<ul>
<li><a href="https://httpd.apache.org/security/vulnerabilities_22.html">https://httpd.apache.org/security/vulnerabilities_22.html</a></li>
</ul>
<p>Services running on 5 ports across 5 hosts are affected by this finding.</p>', e'<p><strong>Apply Updates and Review Patch Policy</strong></p>
<p>The most recent patches should be applied to the Apache installation. In addition, it is advised that the patch policy is reviewed to ensure that, in the future, new patches are applied regularly.</p>
<p>Ensure the version used is supported and maintained long term.</p>', '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('10b42f01-9914-4834-9d9f-8f18e6695bcc', 7, 'bc9daeb9-3b4a-4cd4-a337-89cc9891f86b',
        'OpenSSH - Multiple Vulnerabilities', 'MEDIUM', null, 'I:H/L:L/R:M', 'OPEN', e'<p>The version of OpenSSH running on the server was fingerprinted as OpenSSH 7.3p1, the latest version is 8.0p1.</p>
<p>Multiple vulnerabilities have been publicly disclosed with this version which could be exploited by an attacker with access to the Core Banking Internals environment to:</p>
<ul>
<li>Enumerate valid usernames that could then be used in password brute forcing attacks with the goal of gaining unauthorised access to the host (CVE-2018-15473)</li>
<li>Escalate local privileges to root once access has been achieved (CVE-2016-10010)</li>
<li>Interrupt administration of the host by performing a denial of service attack against the SSH server (CVE-2018-15473)</li>
</ul>
<p>For further information see the following URL:</p>
<ul>
<li><a href="https://www.openssh.com/releasenotes.html">https://www.openssh.com/releasenotes.html</a></li>
</ul>
<p>Please note that this may be a false positive if a back-ported package is in use. The version was identified based on the server banner returned by the affected OpenSSH services.</p>
<p>Services running on 5 ports across 5 hosts are affected by this finding.</p>', e'<p><strong>Update OpenSSH Package and Review Patch Policy</strong></p>
<p>Ensure the version of OpenSSH is the latest suitable package available for the affected host (at the time of writing 8.0p1). Furthermore, review the patch management process to ensure future updates are applied in a timely manner.</p>',
        '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('afd48955-e52e-46e6-be44-16b74fc6f570', 9, 'bc9daeb9-3b4a-4cd4-a337-89cc9891f86b',
        'Default SNMP Community Name Configured', 'MEDIUM', null, 'I:M/L:H/R:M', 'OPEN', e'<p>The systems affected were configured to respond to Simple Network Management Protocol (SNMP) queries using the default community name "public".</p>
<p>This is a read only community string which provides access to information such as the system name, IP addresses, interfaces and processes running. An attacker on the corporate network (<span class="link-to" data-node-id="4683" data-format-string="{finding_id} - {title}">Invalid phase ID - Ineffective Network Segregation</span>) can use this information to map the network and target further attacks.</p>
<p>Services running on 5 ports across 5 hosts are affected by this finding.</p>', e'<p><strong>Disable or Harden SNMP Service</strong></p>
<p>If the SNMP service is operationally required, change the default community string to at least a 12 character alphanumeric string that is not based on a dictionary word. Where possible, migrate to SNMP v3 with strong authentication and encryption enabled. Alternatively, disable the SNMP service if it is not used or restrict access to it from the Corporate LAN.</p>',
        '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('967672ca-cee7-4912-9f0d-461950a951fe', 10, 'bc9daeb9-3b4a-4cd4-a337-89cc9891f86b',
        'Weak Password Complexity Rules', 'MEDIUM', null, 'I:M/L:M/R:M', 'OPEN', e'<p>The password complexity rules permit users to set weak account&nbsp;passwords. This could decrease the time required for a successful brute-force or dictionary-based attack.</p>
<p>The current policy requires passwords to be:</p>
<ul>
<li>5 characters in length</li>
<li>Containing at least 1 digit</li>
</ul>
<p>This policy allows users to set weak passwords such as "pass1".</p>
<p>The application locks out accounts after 10 failed login attempts, providing partial mitigation. However, this provides no protection against password spraying attacks where an attacker attempts to use a large number of usernames but with only 2 or 3 weak passwords.</p>', e'<p><strong>Enforce Strong Password Complexity</strong></p>
<p>Implement a stronger password policy. For example:</p>
<ul>
<li>Minimum password length of 10 characters for standard and 12 for high privilege users,</li>
<li>No maximum password length,</li>
<li>Require at least three different character types (e.g. upper and lower case letters, numbers and special characters),</li>
<li>Prevent use of dictionary words and common passwords.</li>
</ul>
<p>Consider also implementing the following:</p>
<ul>
<li>Prevent password reuse by implementing a password history (e.g. the last 5 passwords cannot be re-used).</li>
<li>Enforce a maximum password age (e.g. 90 days), after which users must change their password.</li>
</ul>
<p>Ensure also that the application stores passwords securely, using a password hashing algorithm such as PBKDF2 or bcrypt, with a large number of hash iterations (e.g. 10,000).</p>',
        '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('881a5174-13b7-4361-98cd-efb6d6ef45fd', 11, 'bc9daeb9-3b4a-4cd4-a337-89cc9891f86b', 'SNMPv1/2c In Use',
        'MEDIUM', null, 'I:M/L:M/R:M', 'OPEN', e'<p>The border networking device accessible from the corporate network (<span class="link-to" data-node-id="4683" data-format-string="{finding_id} - {title}">123-2-05 - Ineffective Network Segregation</span>) was found to support versions 1 and 2c of the Simple Network Management Protocol (SNMP).</p>
<p>These versions do not include security features such as robust authentication and authorisation or encryption. As such an attacker on the corporate LAN could perform an intercept-attack against this device and capture community strings being passed over the network in clear-text.</p>
<p>Sending sensitive information unencrypted provides opportunities for malicious users to capture the information that could be used for subsequent attacks, such as network enumeration and gaining persistent access or control over the affected host.</p>
<p>If an attacker is able to compromise a PUBLIC/READ community string, they would be able to read SNMP data (depending on which MIBs are installed) from the remote device. This information might include system time, IP addresses, interfaces, processes running, or usernames.</p>
<p>An attacker able to compromise a PRIVATE/WRITE community string, could make changes to the network device, this could enable a Corporate network based attacker to gain further access into the Core Banking Internals environment.</p>
<p>Services running on 5 ports across 5 hosts are affected by this finding.</p>', e'<p><strong>Consider Upgrading to SNMPv3</strong></p>
<p>Upgrade to SNMPv3 if this is supported by the device. SNMPv3 has built-in support for authentication, privacy and access control mitigating many of the issues found in the older versions of this protocol.</p>
<p>If this is not possible, the SNMP service should be restricted from the corporate LAN and complex credentials (a minimum of 12 alphanumeric characters and not based on a dictionary word) should be set for the PUBLIC and PRIVATE community strings.</p>
<p><strong>Recommendation Title</strong></p>
<p>Text</p>', '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('04b4f1f3-46ad-4936-a236-f7b2e25742df', 12, 'bc9daeb9-3b4a-4cd4-a337-89cc9891f86b',
        'Invalid SSL/TLS Certificates', 'LOW', null, 'I:M/L:L/R:L', 'OPEN', e'<p>The Windows JumpBox (INTL-CODNAME-JUMBOX-2) accessible from the corporate network and 5 web services accessible only from within the Core Banking Internals environment were configured with SSL certificates that are invalid. Services using an invalid certificate (or invalid certificate chain) are susceptible to intercept-attacks by allowing an attacker to masquerade as a legitimate service.</p>
<p>In the case of INTL-CODNAME-JUMBOX-2 this could allow an attacker on the corporate LAN to intercept credentials of administrators authenticating to the JumpBox. This would provide access into the Core Banking Internals environment. An attacker with access to the Core Banking Internals environment could exploit these weaknesses to compromise session tokens or user credentials used to access management interfaces (<span data-format-string="{finding_id}" data-node-id="28447">12345-2-01</span>)</p>
<p>The following problems were identified:</p>
<ul>
<li>Self-signed certificates or the Certificate is signed by an unknown certificate authority (CA) &ndash; Users are typically warned if a certificate is self-signed or is not signed by a trusted CA. If users are accustomed to a warning then they are likely to ignore the same warning if a different self-signed certificate is used by an attacker posing as the real server.</li>
<li>Certificate with a public key shorter than 2048 characters - Such keys have been proven to be weak enough that an attacker with sufficient resources can brute-force the private key. This is especially the case given ever increasing access to high-powered computing resources such as cloud computing and GPU clusters. With the private key, an attacker in a position to intercept network traffic can easily masquerade as the server without being detected by the victim.</li>
</ul>
<p>Services running on 5 ports across 5 hosts are affected by this finding.</p>', e'<p><strong>Re-Issue Appropriate SSL Certificates</strong></p>
<p>Request and install new certificates from a trusted certificate authority. Ensure that these are associated with a key of at least 2048 bits, the common name matches the servers FQDN, valid for an appropriate period and are signed using a secure hash algorithm (SHA256).</p>',
        '');
INSERT INTO public.findings (id, number, phase_id, name, base_severity, severity_additional_context,
                             vector_string, status, description, recommendation, supporting_material)
VALUES ('3843c2da-1110-4d89-b2b7-b75d2d83bf70', 14, 'bc9daeb9-3b4a-4cd4-a337-89cc9891f86b', 'Directory Listing Enabled',
        'LOW', null, 'I:L/L:H/R:L', 'OPEN', e'<p>Directory listings were enabled on the affected web server. This allows an attacker with access to the Core Banking Internals environment to enumerate the contents of directories and obtain information that can be used in further attacks.</p>
<p>During testing it was possible to review the contents of the "/assets" folder. This contained scripts, style sheets and images. It may be possible for an attacker to review these files for weaknesses that could affect the Core Banking Internals banking application.</p>
<p>Services running on 1 port on 1 host are affected by this finding.</p>', e'<p><strong>Disable Directory Listing</strong></p>
<p>Disable directory listing. In Apache use the following directive:</p>
<pre>Options -Indexes</pre>
<p>It can be placed in different locations depending on the application requirements, for instance in the main configuration file o inside an .htaccess file of the web root or of a specific directory. More information can be found in the official documentation:</p>
<ul>
<li><a href="https://httpd.apache.org/docs/2.4/mod/core.html#options">https://httpd.apache.org/docs/2.4/mod/core.html#options</a></li>
</ul>
<p>As an interim solution, placing a blank file named "index.html" in the affected directory should prevent a directory listing from being returned.</p>',
        '');
