[project]
name = "derp-api"
version = "0.1.0"
description = "Doge Enterprise Portal (DERP) API"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON> Panagiotopoulos", email = "<EMAIL>" },
]
requires-python = ">=3.13"
dependencies = [
    "alembic>=1.14.0",
    "boto3>=1.35.77",
    "fastapi[standard]>=0.115.6",
    "psycopg[binary]>=3.2.3",
    "pydantic>=2.9.2",
    "pydantic-extra-types>=2.10.1",
    "pydantic-settings>=2.6.1",
    "sqlalchemy>=2.0.36",
    "structlog>=24.4.0",
    "pyjwt[crypto]>=2.10.1",
    "beautifulsoup4>=4.12.3",
    "cvss>=3.3",
]

[dependency-groups]
dev = ["mypy>=1.13.0", "pytest>=8.3.4", "pytest-cov>=6.0.0", "ruff>=0.8.2"]

[tool.ruff]
# Allow imports relative to the "app" and "test" directories.
src = ["app", "test"]

# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

# Same as Black.
line-length = 88
indent-width = 4

# Assume Python 3.13
target-version = "py313"

[tool.ruff.lint]
select = [
    # Docstrings
    "D",
    # Pycodestyle errors
    "E",
    # Pyflakes
    "F",
    # pep8-naming
    "N",
    # Pycodestyle warnings
    "W",
    # isort
    "I001",
]
ignore = ["D100", "D101", "D104", "D206", "E501", "W191"]

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"

[tool.ruff.lint.mccabe]
# Unlike Flake8, default to a complexity level of 10.
max-complexity = 10

[tool.ruff.lint.pydocstyle]
# Use Google-style docstrings.
convention = "google"
