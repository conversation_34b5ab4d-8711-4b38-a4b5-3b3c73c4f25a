services:
  localstack:
    image: localstack/localstack
    ports:
      - "127.0.0.1:4566:4566" # LocalStack Gateway
      - "127.0.0.1:4510-4559:4510-4559" # external services port range
    environment:
      - DEBUG=${DEBUG-}
      - DOCKER_HOST=unix:///var/run/docker.sock
    volumes:
      - localstackdata:/var/lib/localstack"
      - "/var/run/docker.sock:/var/run/docker.sock"
  db:
    image: postgres:16
    restart: always
    environment:
      - POSTGRES_DB=derp
      - POSTGRES_USER=derp
      - POSTGRES_PASSWORD=derp
    ports:
      - "5432:5432"
    volumes:
      - pgdata:/var/lib/postgresql/data
  web:
    build:
      dockerfile: Dockerfile
    environment:
      - DB_NAME=derp
      - DB_USER=derp
      - DB_PASSWORD=derp
      - DB_HOST=db
      - DB_PORT=5432
      - AWS_DEFAULT_REGION=eu-west-2
      - CORS_ORIGIN_URL=http://localhost:3000
      - COGNITO_APP_CLIENT_ID=changeme
      - COGNITO_USER_POOL_ID=changeme
    ports:
      - "8000:8000"
    depends_on:
      - db
      - localstack

volumes:
  pgdata:
  localstackdata:
