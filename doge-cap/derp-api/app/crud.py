from collections import defaultdict
from enum import Str<PERSON>num
from typing import Type
from uuid import UUID

import structlog
from cvss.exceptions import (
    CVSS3MalformedError,
)
from fastapi import HTTPException
from pydantic import BaseModel, EmailStr, IPvAnyAddress, ValidationError
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy.orm import Session
from starlette.status import HTTP_500_INTERNAL_SERVER_ERROR

from app.assets import replace_img_src_with_base64_encoded_image
from app.config import get_settings
from app.models import (
    Finding,
    Phase,
    ScoringSystem,
    Test,
    User,
    user_tests,
)
from app.scoring.context_risk_rating import (
    ContextRiskRating,
    ContextRiskRatingMalformedError,
)
from app.scoring.cvss3_rating import CVSS3Rating
from app.scoring.scoring_system import MetricGroup, MetricGroups

logger = structlog.get_logger()


class FindingNotFoundError(Exception):
    pass


def retrieve_tests(
    db: Session,
    cognito_user_guid: UUID,
    archived: bool,
    skip: int = 0,
    limit: int = 100,
) -> list:
    """Get a list of all Tests that a user has access to, along with the number of Findings in each severity category.

    Args:
        db (Session): Database session
        cognito_user_guid (UUID): The Cognito user Guid
        archived (bool): Whether to retrieve archived on unarchived tests
        skip (int): Skip parameter for pagination
        limit (int): Limit parameter for pagination

    Returns:
        list: List of tests and their findings grouped by their rating

    Raises:
        fastapi.HTTPException: If the database operation errors
    """
    try:
        results: list[Type[Test]] = (
            db.query(Test)
            .join(user_tests)
            .join(User)
            .filter(
                User.id == cognito_user_guid, user_tests.c.is_test_archived == archived
            )
            .all()
        )

        tests = []
        for result in results:
            findings_counts = defaultdict(int)
            total_findings = 0
            findings_counts.update(
                {"critical": 0, "high": 0, "medium": 0, "low": 0, "minimal": 0}
            )
            test = {
                "document_guid": result.id,
                "engagement_number": result.engagement_number,
                "name": result.name,
                "time_of_last_export": result.time_of_last_export.strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
            }
            for phase in result.phases:
                findings = phase.findings
                for finding in findings:
                    findings_counts[finding.base_severity.lower()] += 1
                    total_findings += 1
            findings_counts["total"] = total_findings
            test["findings"] = findings_counts
            tests.append(test)
            tests.sort(key=lambda x: x["engagement_number"])
        return tests
    except (SQLAlchemyError, Exception) as e:
        logger.error(
            "Database error when retrieving tests",
            error=str(e),
        )
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail="Server error"
        )


class FindingStatus(StrEnum):
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    FALSE_POSITIVE = "FALSE_POSITIVE"


class Scoring(BaseModel):
    metric_groups: list[MetricGroup]
    vector_string: str
    base_severity: str


class AssociatedHost(BaseModel):
    ip_address: IPvAnyAddress
    network: str
    port: int
    protocol: str


class FindingDetails(BaseModel):
    number: int
    engagement_number: int
    phase_number: int
    name: str
    status: FindingStatus
    scoring: Scoring
    description: str | None
    recommendation: str | None
    supporting_material: str | None
    associated_hosts: list[AssociatedHost]


def get_finding(
    db: Session,
    cognito_user_guid: UUID,
    document_guid: UUID,
    phase_number: int,
    finding_number: int,
) -> FindingDetails:
    """Retrieve details of a Finding.

    Args:
        db: Database session
        cognito_user_guid (UUID): The Cognito user Guid
        document_guid (UUID): Test Document Guid
        phase_number (int): The phase number of the finding
        finding_number (int): The finding number

    Returns:
        A finding

    Raises:
        fastapi.HTTPException: If the database operation errors
    """
    try:
        result: tuple[Finding, ScoringSystem, int, int] = (
            db.query(Finding, ScoringSystem, Test.engagement_number, Phase.number)
            .join(Phase, Phase.id == Finding.phase_id)
            .join(Test, Test.id == Phase.test_id)
            # .join(AssociatedHost, AssociatedHost.finding_id == Finding.id)
            .join(Test.users)
            .join(ScoringSystem, ScoringSystem.id == Test.scoring_system_id)
            .where(User.id == cognito_user_guid)
            .where(Finding.number == finding_number)
            .where(Phase.number == phase_number)
            .where(Test.id == document_guid)
            .first()
        )

        finding_result: Finding = result[0]
        scoring_system_result: ScoringSystem = result[1]
        engagement_number: int = result[2]
        phase_number: int = result[3]

        if scoring_system_result.name in ["CVSS3Rating", "CVSS31Rating"]:
            try:
                c = CVSS3Rating(finding_result.vector_string)
                metric_groups: MetricGroups = c.export()
            except CVSS3MalformedError as e:
                logger.error(
                    "Malformed CVSS3Rating vector",
                    vector_string=finding_result.vector_string,
                    finding_number=finding_number,
                    cognito_user_guid=cognito_user_guid,
                    error=str(e),
                )
                raise HTTPException(
                    status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail="Server error"
                )

        elif scoring_system_result.name == "ContextRiskRating":
            try:
                c = ContextRiskRating(finding_result.vector_string)
                metric_groups: MetricGroups = c.export()
            except ContextRiskRatingMalformedError as e:
                logger.error(
                    "Malformed Context Risk Rating vector",
                    vector_string=finding_result.vector_string,
                    finding_number=finding_number,
                    cognito_user_guid=cognito_user_guid,
                    error=str(e),
                )
                raise HTTPException(
                    status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail="Server error"
                )
        else:
            logger.error(
                "Invalid scoring system of Finding during retrieval",
                finding_number=finding_number,
                cognito_user_guid=cognito_user_guid,
            )
            raise HTTPException(
                status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail="Server error"
            )
        # if result is None:
        #     logger.error(
        #         "Finding not found or not accessible to user",
        #         finding_number=finding_number,
        #         user=user_email,
        #     )
        #     raise FindingNotFoundError
        logger.info(
            "User accessing finding details",
            finding_number=finding_number,
            cognito_user_guid=cognito_user_guid,
        )

        associated_hosts: list[AssociatedHost] = []
        for associated_host in finding_result.associated_hosts:
            associated_hosts.append(
                AssociatedHost(
                    ip_address=associated_host.ip_address,
                    network=associated_host.network,
                    port=associated_host.port,
                    protocol=associated_host.protocol,
                )
            )

        finding_details = FindingDetails(
            number=finding_result.number,
            engagement_number=engagement_number,
            phase_number=phase_number,
            name=finding_result.name,
            status=finding_result.status,
            scoring=Scoring(
                metric_groups=metric_groups.metric_groups,
                base_severity=finding_result.base_severity.lower(),
                vector_string=finding_result.vector_string,
            ),
            # Replace img src with a base64 encoded image from S3, for description/recommendation/supporting material
            description=replace_img_src_with_base64_encoded_image(
                finding_result.description, get_settings().tenant_assets_bucket
            ),
            recommendation=replace_img_src_with_base64_encoded_image(
                finding_result.recommendation, get_settings().tenant_assets_bucket
            ),
            supporting_material=replace_img_src_with_base64_encoded_image(
                finding_result.supporting_material,
                get_settings().tenant_assets_bucket,
            ),
            associated_hosts=associated_hosts,
        )
        return finding_details
    except (SQLAlchemyError, ValidationError) as e:
        logger.error(
            "Database error when retrieving finding details",
            finding_number=finding_number,
            error=str(e),
        )
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail="Server error"
        )


class TestNotFoundError(Exception):
    pass


def get_test_details(
    db: Session,
    document_guid: UUID,
    cognito_user_guid: UUID,
    skip: int = 0,
    limit: int = 100,
):
    """Retrieve all findings of a Test if a user has access to it.

    Args:
        db (Session): Database session
        cognito_user_guid (UUID): The Cognito user Guid
        document_guid (UUID): Test Document Guid
        skip (int): Skip parameter for pagination
        limit (int): Limit parameter for pagination

    Returns:
        list: List of Findings

    Raises:
        fastapi.HTTPException: If the database operation errors
    """
    try:
        results = (
            db.query(
                Test.id,
                Test.engagement_number,
                Test.name,
                Phase.number,
                Phase.name,
                Finding,
            )
            .join(Test, Phase.test_id == Test.id)
            .join(Finding, Finding.phase_id == Phase.id)
            .join(Test.users)
            .where(User.id == cognito_user_guid)
            .where(Test.id == document_guid)
            .offset(skip)
            .limit(limit)
            .all()
        )

        if len(results) == 0:
            logger.error(
                "Test not found or not accessible to user",
                document_guid=document_guid,
                cognito_user_guid=cognito_user_guid,
            )
            raise TestNotFoundError

        phase_dict = {}
        test_name = ""
        for result in results:
            document_guid = result[0]
            engagement_number = result[1]
            test_name = result[2]
            phase_number = result[3]
            phase_name = result[4]
            finding = {
                "number": result[5].number,
                "name": result[5].name,
                "status": result[5].status,
                "base_severity": result[5].base_severity.lower(),
                "has_description": result[5].description is not None
                and len(result[5].description) > 0,
                "has_recommendation": result[5].recommendation is not None
                and len(result[5].recommendation) > 0,
                "has_supporting_material": result[5].supporting_material is not None
                and len(result[5].supporting_material) > 0,
                "has_associated_hosts": len(result[5].associated_hosts) > 0,
            }
            if phase_number not in phase_dict:
                phase_dict[phase_number] = {
                    "number": phase_number,
                    "name": phase_name,
                    "findings": [],
                }
            phase_dict[phase_number]["findings"].append(finding)
        phases_list = list(phase_dict.values())

        # Always sort Findings in a Phase by Finding Number
        for phase in phases_list:
            if len(phase["findings"]) > 0:
                phase["findings"].sort(key=lambda x: x["number"])

        # Always sort Phases in a Test by Phase Number
        phases_list.sort(key=lambda x: x["number"])

        logger.info(
            "User accessing test details",
            engagement_number=engagement_number,
            document_guid=document_guid,
            cognito_user_guid=cognito_user_guid,
        )
        return {
            "document_guid": document_guid,
            "engagement_number": engagement_number,
            "name": test_name,
            "phases": phases_list,
        }

    except SQLAlchemyError as e:
        logger.error(
            "Database error when retrieving findings for test",
            engagement_number=engagement_number,
            document_guid=document_guid,
            error=str(e),
        )
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail="Server error"
        )


def get_tests_with_assigned_users(db: Session, skip: int = 0, limit: int = 100) -> dict:
    """Get a list of tests with their assigned users.

    Args:
        db (Session): Database session
        skip (int): Skip parameter for pagination
        limit (int): Limit parameter for pagination
    """
    try:
        tests: list[Test] = db.query(Test).offset(skip).limit(limit).all()

        response_data = []
        for test in tests:
            test_response_data = {
                "document_guid": test.id,
                "engagement_number": test.engagement_number,
                "name": test.name,
                "assigned_users": [],
                "time_of_last_export": test.time_of_last_export.strftime(
                    "%Y-%m-%d %H:%M:%S"
                ),
                "export_status": test.export_status,
            }

            for user in test.users:
                test_response_data["assigned_users"].append(user.email)
            response_data.append(test_response_data)

        return response_data

    except SQLAlchemyError as e:
        logger.error(
            "Database error when retrieving tests with assigned users",
            error=str(e),
        )
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail="Server error"
        )


def get_test_with_assigned_users(db: Session, document_guid: UUID) -> dict:
    """Get a test with its assigned users.

    Args:
        db (Session): Database session
        document_guid (UUID): Test Document Guid
    """
    try:
        test: Test = db.query(Test).filter_by(id=document_guid).first()

        test_response_data = {
            "document_guid": test.id,
            "engagement_number": test.engagement_number,
            "name": test.name,
            "assigned_users": [],
            "time_of_last_export": test.time_of_last_export.strftime(
                "%Y-%m-%d %H:%M:%S"
            ),
            "export_status": test.export_status,
        }

        for user in test.users:
            test_response_data["assigned_users"].append(user.email)

        return test_response_data

    except (SQLAlchemyError, Exception) as e:
        logger.error(
            "Database error when retrieving tests with assigned users",
            error=str(e),
        )
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail="Server error"
        )


class UserAssignmentError(Exception):
    pass


class UserAlreadyAssignedError(Exception):
    pass


def assign_user_to_test(
    db: Session,
    document_guid: UUID,
    cognito_user_guid: UUID,
    cognito_user_email: EmailStr,
) -> None:
    """Assign a user to a test.

    Args:
        db (Session): Database session
        document_guid (UUID): Test Document Guid
        cognito_user_guid (UUID): The Cognito user Guid
        cognito_user_email (EmailStr): The email of the user
    """
    try:
        test = db.query(Test).filter_by(id=document_guid).first()
        if test is None:
            raise UserAssignmentError("Test not found")
        user = db.query(User).filter_by(email=cognito_user_email).first()
        if user is None:
            logger.warn(
                "Cognito user not in database, attempting to add them for the first time",
                cognito_user_email=cognito_user_email,
                document_guid=document_guid,
            )
            user = User(id=cognito_user_guid, email=cognito_user_email)

        if user in test.users:
            logger.warn(
                "User already assigned to test",
                cognito_user_email=cognito_user_email,
                document_guid=document_guid,
            )
            raise UserAlreadyAssignedError("User already assigned to test")
        test.users.append(user)
        db.commit()
        logger.info(
            "Assigned user to test",
            cognito_user_email=cognito_user_email,
            document_guid=document_guid,
        )
    except SQLAlchemyError as e:
        logger.error(
            "Database error when assigning user to test",
            cognito_user_email=cognito_user_email,
            document_guid=document_guid,
            error=str(e),
        )
        raise UserAssignmentError("Database error when assigning user to test")


def remove_user_from_test(
    db: Session, document_guid: UUID, cognito_user_email: EmailStr
) -> None:
    """Remove a user from a test.

    Args:
        db (Session): Database session
        document_guid (UUID): Test Document Guid
        cognito_user_email (EmailStr): The user to remove from the Test
    """
    try:
        test = db.query(Test).filter_by(id=document_guid).first()
        if test is None:
            logger.error(
                "Test not found",
                cognito_user_email=cognito_user_email,
                document_guid=document_guid,
            )
            raise UserAssignmentError("Test not found")
        user = db.query(User).filter_by(email=cognito_user_email).first()
        if user is None:
            logger.error(
                "User not found in database",
                cognito_user_email=cognito_user_email,
                document_guid=document_guid,
            )
            raise UserAssignmentError("User not found in database")
        test.users.remove(user)
        db.commit()
        logger.info(
            "Removed user from test",
            cognito_user_email=cognito_user_email,
            document_guid=document_guid,
        )
    except (SQLAlchemyError, Exception) as e:
        logger.error(
            "Database error when removing user from test",
            cognito_user_email=cognito_user_email,
            document_guid=document_guid,
            error=str(e),
        )
        raise UserAssignmentError("Database error when removing user from test")


def set_finding_status(
    db: Session,
    cognito_user_guid: UUID,
    document_guid: UUID,
    phase_number: int,
    finding_number: int,
    status: str,
) -> None:
    """Set the status of a Finding.

    Args:
        db (Session): Database session
        cognito_user_guid (UUID): Cognito user Guid
        document_guid (UUID): Test Document Guid
        phase_number (int): Phase number belonging to the Test
        finding_number (int): Finding number belonging to the Phase
        status (str): The new status to set to the Finding
    """
    finding_result: Finding = (
        db.query(Finding)
        .join(Phase, Phase.id == Finding.phase_id)
        .join(Test, Test.id == Phase.test_id)
        .join(Test.users)
        .where(User.id == cognito_user_guid)
        .where(Finding.number == finding_number)
        .where(Phase.number == phase_number)
        .where(Test.id == document_guid)
        .first()
    )

    if finding_result is None:
        raise HTTPException(status_code=404, detail="Item not found")

    finding_result.status = status
    db.commit()


async def delete_test(db: Session, document_guid: UUID) -> None:
    """Delete an existing Test.

    Args:
        db (Session): Database session
        document_guid (UUID): Test Document Guid

    Returns:
        None
    """
    try:
        test = db.query(Test).filter(Test.id == document_guid).first()
        if test:
            db.delete(test)
            db.commit()
        else:
            raise HTTPException(status_code=404, detail="Test not found")
    except SQLAlchemyError as e:
        logger.error("Failed to delete Test", document_guid=document_guid, error=str(e))
        raise HTTPException(status_code=500, detail="Failed to delete Test")


def archive_test(db: Session, document_guid: UUID, cognito_user_guid: UUID) -> None:
    """Archive an existing test.

    Args:
        db (Session): Database session
        document_guid (UUID): Test Document Guid
        cognito_user_guid (UUID): Cognito user Guid

    Returns:
        None
    """
    try:
        db.execute(
            user_tests.update()
            .where(user_tests.c.test_id == document_guid)
            .where(user_tests.c.user_id == cognito_user_guid)
            .values(is_test_archived=True)
        )
        db.commit()
    except SQLAlchemyError as e:
        logger.error(
            "Failed to archive Test for user",
            cognito_user_guid=cognito_user_guid,
            document_guid=document_guid,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail="Failed to archive Test for user")


def unarchive_test(db: Session, document_guid: UUID, cognito_user_guid: UUID) -> None:
    """Unarchive an existing test.

    Args:
        db (Session): Database session
        document_guid (UUID): Test Document Guid
        cognito_user_guid (UUID): Cognito user Guid

    Returns:
        None
    """
    try:
        db.execute(
            user_tests.update()
            .where(user_tests.c.test_id == document_guid)
            .where(user_tests.c.user_id == cognito_user_guid)
            .values(is_test_archived=False)
        )
        db.commit()
    except SQLAlchemyError as e:
        logger.error(
            "Failed to unarchive Test for user",
            cognito_user_guid=cognito_user_guid,
            document_guid=document_guid,
            error=str(e),
        )
        raise HTTPException(status_code=500, detail="Failed to unarchive Test for user")
