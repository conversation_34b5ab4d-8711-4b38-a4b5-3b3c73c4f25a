import structlog
from sqlalchemy import create_engine
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker

from app.config import get_settings

logger = structlog.get_logger()

SQLALCHEMY_DATABASE_URL = (
    f"postgresql+psycopg://{get_settings().db_user}:{get_settings().db_password.get_secret_value()}"
    f"@{get_settings().db_host}:{get_settings().db_port}/{get_settings().db_name}"
)

engine = create_engine(SQLALCHEMY_DATABASE_URL)
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()


def create_database_tables() -> None:
    """Create database tables.

    Normally this process should be taken care of by alembic, which already exists in the project.
    Leaving it here as an alternative for development purposes.

    Returns: None
    """
    logger.info(
        "Creating database tables",
        host=get_settings().db_host,
        port=get_settings().db_port,
        db_name=get_settings().db_name,
    )

    Base.metadata.create_all(bind=engine)
