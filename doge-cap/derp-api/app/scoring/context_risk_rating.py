from collections import OrderedDict

from pydantic import ValidationError

from app.scoring.scoring_system import BaseScoringSystem, MetricGroups


class ContextRiskRatingMalformedError(Exception):
    pass


class ContextRiskRating(BaseScoringSystem):
    METRICS_ABBREVIATIONS = OrderedDict(
        [
            ("R", "Risk"),
            ("I", "Impact"),
            ("L", "Likelihood"),
        ]
    )

    METRICS_VALUE_NAMES = OrderedDict(
        [
            (
                "R",
                OrderedDict(
                    [
                        ("C", "Critical"),
                        ("H", "High"),
                        ("M", "Medium"),
                        ("L", "Low"),
                        ("MI", "Minimal"),
                    ]
                ),
            ),
            (
                "I",
                OrderedDict(
                    [
                        ("C", "Critical"),
                        ("H", "High"),
                        ("M", "Medium"),
                        ("L", "Low"),
                        ("V", "Very Low"),
                    ]
                ),
            ),
            (
                "L",
                OrderedDict(
                    [
                        ("VH", "Very High"),
                        ("H", "High"),
                        ("M", "Medium"),
                        ("L", "Low"),
                        ("VL", "Very Low"),
                    ]
                ),
            ),
        ]
    )

    def __init__(self, vector_string: str) -> None:
        """Parse a Context Risk Rating vector string.

        Args:
            vector_string (str): A Context Risk Rating vector string
        """
        self.vector_string: str = vector_string
        self.metrics: dict[str, str] = {}
        self._parse_vector()

    def _parse_vector(self) -> None:
        """Parses metrics from the Context Risk Rating vector.

        Raises:
            ContextRiskRatingMalformedError: if vector is not in expected format
        """
        if self.vector_string == "":
            raise ContextRiskRatingMalformedError(
                "Malformed ContextRiskRating vector, vector is empty"
            )

        if self.vector_string.endswith("/"):
            raise ContextRiskRatingMalformedError(
                'Malformed ContextRiskRating vector, trailing "/"'
            )

        try:
            fields: list[str] = self.vector_string.split("/")
        except IndexError:
            raise ContextRiskRatingMalformedError(
                'Malformed ContextRiskRating vector "{0}"'.format(self.vector_string)
            )

        for field in fields:
            if field == "":
                raise ContextRiskRatingMalformedError(
                    'Empty field in ContextRiskRating vector "{0}"'.format(
                        self.vector_string
                    )
                )

            try:
                metric, value = field.split(":")
            except ValueError:
                raise ContextRiskRatingMalformedError(
                    'Malformed ContextRiskRating field "{0}"'.format(field)
                )

            try:
                self.metrics[self.METRICS_ABBREVIATIONS[metric]] = (
                    self.METRICS_VALUE_NAMES[metric][value]
                )
            except KeyError as e:
                raise ContextRiskRatingMalformedError(
                    f"Malformed ContextRiskRating abbreviation: {str(e)}"
                )

    def export(self) -> MetricGroups:
        """Export Context Risk Rating metrics.

        Returns:
            MetricGroups: A dictionary of Context Risk Rating metrics
        """
        metrics: list[dict[str, str]] = []
        for metric_attribute, metric_value in self.metrics.items():
            metrics.append(
                {
                    "attribute": metric_attribute,
                    "value": metric_value.lower(),
                }
            )

        try:
            metric_groups = MetricGroups(
                **{
                    "metric_groups": [
                        {"title": "mandatory", "metrics": metrics},
                    ]
                }
            )
            return metric_groups
        except ValidationError as e:
            print(str(e))
