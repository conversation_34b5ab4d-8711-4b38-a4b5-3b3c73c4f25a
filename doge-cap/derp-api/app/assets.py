import base64

import boto3
import structlog
from botocore.exceptions import (
    EndpointConnectionError,
    NoCredentialsError,
    PartialCredentialsError,
)
from bs4 import BeautifulSoup

logger = structlog.get_logger()

s3_client = boto3.client("s3")


def replace_img_src_with_base64_encoded_image(
    html_content: str,
    bucket_name: str,
) -> str:
    """Replace all the values in img src tags with a base64 representation of images from a S3 bucket.

    In case of error, for now log a warning, with a soft failure that returns an empty string.

    Args:
        html_content (str): The HTML content to replace img src tags
        bucket_name (str): The bucket name
    Returns:
        A HTML string containing a base64 encoded image
    """
    # Parse the HTML content using BeautifulSoup
    soup = BeautifulSoup(html_content, "html.parser")

    # Find all img tags in the HTML
    img_tags = soup.find_all("img")

    # Replace src attributes with pre-signed URLs
    for img_tag in img_tags:
        src_attribute: str | None = img_tag.get("src")
        if src_attribute:
            if src_attribute.startswith("/"):
                src_attribute = src_attribute[1:]
            try:
                response = s3_client.get_object(Bucket=bucket_name, Key=src_attribute)
                image_content: bytes = response["Body"].read()
                base64_encoded_image: str = base64.b64encode(image_content).decode(
                    "utf-8"
                )
                image_url: str = f"data:image/jpeg;base64,{base64_encoded_image}"
            except (
                NoCredentialsError,
                PartialCredentialsError,
                EndpointConnectionError,
                Exception,
            ) as e:
                logger.warn(
                    "Could not generate image URL",
                    bucket_name=bucket_name,
                    src_attribute=src_attribute,
                    error=str(e),
                )
                image_url = ""

            # Update the src attribute with the image data URL
            img_tag["src"] = image_url

    # Return the modified HTML content
    return str(soup)
