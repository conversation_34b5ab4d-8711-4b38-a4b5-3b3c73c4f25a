from uuid import UUID

import structlog
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, EmailStr
from sqlalchemy.orm import Session
from starlette.status import (
    HTTP_400_BAD_REQUEST,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from app import cognito, crud
from app.cognito import CognitoError
from app.crud import UserAlreadyAssignedError, UserAssignmentError
from app.dependencies import (
    cognito_jwt_access_token_authoriser_for_test_admin,
    get_db,
)

logger = structlog.get_logger()

router = APIRouter()


@router.get("")
async def get_tests_with_assigned_users(
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    _=Depends(cognito_jwt_access_token_authoriser_for_test_admin),
):
    """Get a list of tests with their assigned users, this functionality is accessible only to a Test Admin.

    Args:
        skip (int): Skip parameter for pagination
        limit (int): Limit parameter for pagination
        db (Session): Database session
        _: Authentication dependency for the endpoint, only allow Test Admins
    """
    results = crud.get_tests_with_assigned_users(db, skip, limit)
    return {"data": results}


@router.get("/{document_guid}")
async def get_test_with_assigned_users(
    document_guid: UUID,
    db: Session = Depends(get_db),
    _=Depends(cognito_jwt_access_token_authoriser_for_test_admin),
):
    """Get a test with its assigned users, this functionality is accessible only to a Test Admin.

    Args:
        document_guid (UUID): Test Document Guid
        db (Session): Database session
        _: Authentication dependency for the endpoint, only allow Test Admins
    """
    results = crud.get_test_with_assigned_users(db, document_guid)
    return {"data": results}


@router.post("/{document_guid}/users/{user_email}")
async def assign_user_to_test(
    document_guid: UUID,
    user_email: EmailStr,
    db: Session = Depends(get_db),
    _=Depends(cognito_jwt_access_token_authoriser_for_test_admin),
):
    """Assign a user to a test, this functionality is accessible only to a Test Admin.

    Args:
        document_guid (UUID): Test ID
        user_email (EmailStr): The user to assign to the Test
        db: Database session
        _: Authentication dependency for the endpoint, only allow Test Admins
    """
    try:
        cognito_user = await cognito.get_user(user_email)
    except CognitoError as e:
        logger.error(
            "Could not assign user to test",
            user_email=user_email,
            document_guid=document_guid,
            error=str(e),
        )
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not assign user to test",
        )

    try:
        crud.assign_user_to_test(
            db, document_guid, cognito_user["sub"], cognito_user["email"]
        )
    except UserAssignmentError:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not assign user to test",
        )
    except UserAlreadyAssignedError:
        raise HTTPException(
            status_code=HTTP_400_BAD_REQUEST,
            detail=f"User {user_email} already assigned to test",
        )
    logger.info(
        "Assigned user to test", user_email=user_email, document_guid=document_guid
    )
    return {
        "message": "Assigned user to test",
        "user": user_email,
        "document_guid": document_guid,
    }


@router.delete("/{document_guid}/users/{user}")
async def remove_user_from_test(
    document_guid: UUID,
    user: EmailStr,
    db: Session = Depends(get_db),
    _=Depends(cognito_jwt_access_token_authoriser_for_test_admin),
):
    """Remove a user from a test, this functionality is accessible only to a Test Admin.

    Args:
        document_guid (UUID): Test Document Guid to remove
        user (EmailStr): The user to remove from the Test
        db (Session): Database session
        _: Authentication dependency for the endpoint, only allow Test Admins
    """
    try:
        cognito_user = await cognito.get_user(user)
    except cognito.CognitoError:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not remove user from test",
        )
    try:
        crud.remove_user_from_test(db, document_guid, cognito_user["email"])
    except UserAssignmentError:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Could not remove user {user} from test",
        )

    logger.info("Removed user from test", user=user, document_guid=document_guid)
    return {
        "message": "Removed user from test",
        "user": user,
        "document_guid": document_guid,
    }


@router.delete("/{document_guid}")
async def remove_test(
    document_guid: UUID,
    db: Session = Depends(get_db),
    _=Depends(cognito_jwt_access_token_authoriser_for_test_admin),
):
    """Remove test.

    Args:
        document_guid (UUID): Test Document Guid to remove
        db (Session): Database session
        _: Authentication dependency for the endpoint, only allow Test Admins
    """
    try:
        await crud.delete_test(db, document_guid)
    except Exception:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Could not delete test {document_guid}",
        )


class SearchUsersRequest(BaseModel):
    search_users_string: str


@router.post("/users/search")
async def search_users(
    search_users_request: SearchUsersRequest,
    _=Depends(cognito_jwt_access_token_authoriser_for_test_admin),
):
    """Search users.

    Args:
        search_users_request (SearchUsersRequest): Request JSON body that contains the search string
        _: Authentication dependency for the endpoint, only allow Test Admins
    """
    try:
        users: list[str] = await cognito.search_users(
            search_users_request.search_users_string
        )
    except CognitoError:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Error searching users",
        )
    return {"data": users}
