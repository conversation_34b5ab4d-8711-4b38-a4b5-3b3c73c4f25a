import structlog
from fastapi import <PERSON><PERSON>out<PERSON>, Depends, HTTPException, Response
from pydantic import BaseModel, EmailStr
from starlette.status import (
    HTTP_204_NO_CONTENT,
    HTTP_400_BAD_REQUEST,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from app import cognito
from app.cognito import CognitoError, UsernameExistsError
from app.dependencies import (
    cognito_jwt_access_token_authoriser_for_user_admin,
)

logger = structlog.get_logger()

router = APIRouter()


class UserInviteRequest(BaseModel):
    email: EmailStr


@router.post("/invite", status_code=204)
async def invite_user(
    user_request: UserInviteRequest,
    _=Depends(cognito_jwt_access_token_authoriser_for_user_admin),
) -> Response:
    """Invite a user to the Portal using Amazon Cognito.

    Args:
        user_request (UserRequest): Request body
        _: Authentication dependency for the endpoint, only allow User Admins

    Returns:
        Response: FastAPI Response
    """
    try:
        await cognito.invite_user(user_request.email)
    except UsernameExistsError as e:
        raise HTTPException(status_code=HTTP_400_BAD_REQUEST, detail=str(e))
    except CognitoError as e:
        raise HTTPException(status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    return Response(status_code=HTTP_204_NO_CONTENT)


class UserRequest(BaseModel):
    username: EmailStr


@router.get("")
async def get_users(
    _=Depends(cognito_jwt_access_token_authoriser_for_user_admin),
):
    """Get all users in the User Pool and the groups they belong to, if they are a User Admin and/or a Test Admin.

    Args:
        _: Authentication dependency for the endpoint, only allow User Admins
    """
    try:
        users = await cognito.get_users()
    except CognitoError as e:
        raise HTTPException(status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    return {"data": users}


@router.post("")
async def get_user(
    user_request: UserRequest,
    _=Depends(cognito_jwt_access_token_authoriser_for_user_admin),
):
    """Get a user from a User Pool and the groups they belong to, if they are a User Admin and/or a Test Admin.

    Args:
        user_request (UserRequest): Request body
        _: Authentication dependency for the endpoint, only allow User Admins
    """
    try:
        user = await cognito.get_user(user_request.username)
    except CognitoError as e:
        raise HTTPException(status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    return {"data": user}


@router.delete(
    "",
    status_code=204,
)
async def delete_user(
    user_request: UserRequest,
    _=Depends(cognito_jwt_access_token_authoriser_for_user_admin),
) -> Response:
    """Delete a user from the User Pool.

    Args:
        user_request (UserRequest): Request body
        _: Authentication dependency for the endpoint, only allow User Admins
    """
    try:
        await cognito.delete_user(user_request.username)
    except CognitoError as e:
        raise HTTPException(status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))
    return Response(status_code=HTTP_204_NO_CONTENT)


class UserToGroupRequest(BaseModel):
    username: EmailStr


@router.post("/groups/user_admin/add_user", tags=["groups"])
async def add_user_to_user_admin_group(
    add_user_to_group_request: UserToGroupRequest,
    _=Depends(cognito_jwt_access_token_authoriser_for_user_admin),
):
    """Add a user from the User Pool to the User Admins Group.

    Args:
        add_user_to_group_request (UserToGroupRequest): Request body
        _: Authentication dependency for the endpoint, only allow User Admins
    """
    try:
        response = await cognito.add_user_to_user_admins(
            add_user_to_group_request.username
        )
    except CognitoError:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not add user to user_admin group",
        )
    return {"response": response}


@router.post("/groups/user_admin/remove_user", tags=["groups"])
async def remove_user_from_user_admin_group(
    remove_user_to_group_request: UserToGroupRequest,
    _=Depends(cognito_jwt_access_token_authoriser_for_user_admin),
):
    """Remove a user from the User Admins Group.

    Args:
        remove_user_to_group_request (UserToGroupRequest): Request body
        _: Authentication dependency for the endpoint, only allow User Admins
    """
    try:
        response = await cognito.remove_user_from_user_admins(
            remove_user_to_group_request.username
        )
    except CognitoError:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not remove user from user_admins group",
        )
    return {"response": response}


@router.post("/groups/test_admin/add_user", tags=["groups"])
async def add_user_to_test_admin_group(
    add_user_to_group_request: UserToGroupRequest,
    _=Depends(cognito_jwt_access_token_authoriser_for_user_admin),
):
    """Add a user to the Test Admins Group.

    Args:
        add_user_to_group_request (UserToGroupRequest): Request body
        _: Authentication dependency for the endpoint, only allow User Admins
    """
    try:
        response = await cognito.add_user_to_test_admins(
            add_user_to_group_request.username
        )
    except CognitoError:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not add user to test_admin group",
        )
    return {"response": response}


@router.post("/groups/test_admin/remove_user", tags=["groups"])
async def remove_user_from_test_admin_group(
    remove_user_to_group_request: UserToGroupRequest,
    _=Depends(cognito_jwt_access_token_authoriser_for_user_admin),
):
    """Remove a user from the Test Admins Group.

    Args:
        remove_user_to_group_request (UserToGroupRequest): Request body
        _: Authentication dependency for the endpoint, only allow User Admins
    """
    try:
        response = await cognito.remove_user_from_test_admins(
            remove_user_to_group_request.username
        )
    except CognitoError:
        raise HTTPException(
            status_code=HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Could not remove user from test_admin group",
        )
    return {"response": response}
