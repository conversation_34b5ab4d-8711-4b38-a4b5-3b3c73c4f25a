import csv
import io
from enum import StrEnum
from uuid import UUID

import structlog
from fastapi import APIRouter, Depends, HTTPException
from pydantic import BaseModel, ValidationError
from sqlalchemy.orm import Session
from starlette.responses import Response, StreamingResponse
from starlette.status import (
    HTTP_204_NO_CONTENT,
    HTTP_404_NOT_FOUND,
    HTTP_500_INTERNAL_SERVER_ERROR,
)

from app import crud
from app.crud import FindingDetails, FindingNotFoundError, TestNotFoundError
from app.dependencies import (
    cognito_jwt_access_token_authoriser,
    get_db,
)

logger = structlog.get_logger()

router = APIRouter()


class DashboardFinding(BaseModel):
    total: int
    critical: int
    high: int
    medium: int
    low: int
    minimal: int


class DashboardTest(BaseModel):
    document_guid: UUID
    engagement_number: int
    name: str
    findings: DashboardFinding
    time_of_last_export: str


class DashboardTests(BaseModel):
    tests: list[DashboardTest]


@router.get("", response_model=DashboardTests)
async def get_tests(
    archived: bool = False,
    export: bool = False,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    user_claims=Depends(cognito_jwt_access_token_authoriser),
):
    """Get a list of all tests that a user has access to, along with the number of Findings in each severity category.

    Args:
        archived (bool): Whether to returned archived or unarchived tests
        export (bool): Return an export or not
        skip (int): Skip parameter for pagination
        limit (int): Limit parameter for pagination
        db (Session): Database session
        user_claims: Authentication dependency for the endpoint, also returns claims from the Cognito JWT
    """
    tests = crud.retrieve_tests(
        db, user_claims["sub"], archived, skip=skip, limit=limit
    )
    if export:
        stream = io.StringIO()
        # Write the data to CSV
        writer = csv.writer(stream)
        header = [
            "test_id",
            "test_name",
            "overall_findings",
            "critical_findings",
            "high_findings",
            "medium_findings",
            "low_findings",
            "minimal_findings",
            "time_of_last_export",
        ]
        writer.writerow(header)
        for test in tests:
            writer.writerow(
                [
                    test["engagement_number"],
                    test["name"],
                    test["findings"]["total"],
                    test["findings"]["critical"],
                    test["findings"]["high"],
                    test["findings"]["medium"],
                    test["findings"]["low"],
                    test["findings"]["minimal"],
                    test["time_of_last_export"],
                ]
            )

        response = StreamingResponse(iter([stream.getvalue()]), media_type="text/csv")
        response.headers["Content-Disposition"] = "attachment; filename=tests.csv"
        return response
    return {"tests": tests}


class FindingStatus(StrEnum):
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    FALSE_POSITIVE = "FALSE_POSITIVE"


class TestFinding(BaseModel):
    number: int
    name: str
    base_severity: str
    status: FindingStatus
    has_description: bool
    has_recommendation: bool
    has_supporting_material: bool
    has_associated_hosts: bool


class Phase(BaseModel):
    number: int
    name: str
    findings: list[TestFinding]


class TestDetails(BaseModel):
    document_guid: UUID
    engagement_number: int
    name: str
    phases: list[Phase]


@router.get("/{document_guid}", response_model=TestDetails)
async def get_test(
    document_guid: UUID,
    export: bool = False,
    skip: int = 0,
    limit: int = 100,
    db: Session = Depends(get_db),
    user_claims=Depends(cognito_jwt_access_token_authoriser),
):
    """Get details of a Test given its ID.

    Args:
        document_guid (UUID): Test document guid
        export (bool): Return an export or not
        skip (int): Skip parameter for pagination
        limit (int): Limit parameter for pagination
        db (Session): Database session
        user_claims: Authentication dependency for the endpoint, also returns claims from the Cognito JWT
    """
    try:
        test_details_result = crud.get_test_details(
            db, document_guid, user_claims["sub"], skip=skip, limit=limit
        )
        try:
            test_details = TestDetails(**test_details_result)
        except ValidationError:
            raise HTTPException(
                status_code=HTTP_500_INTERNAL_SERVER_ERROR, detail="Server error"
            )

        if export:
            stream = io.StringIO()
            # Write the data to CSV
            writer = csv.writer(stream)
            header = [
                "finding_id",
                "finding_name",
                "rating",
                "status",
            ]
            writer.writerow(header)
            for phase in test_details.phases:
                for finding in phase.findings:
                    writer.writerow(
                        [
                            f"{test_details.engagement_number}-{phase.number}-{str(finding.number).rjust(2, '0')}",
                            finding.name,
                            finding.base_severity,
                            finding.status,
                        ]
                    )
            response = StreamingResponse(
                iter([stream.getvalue()]), media_type="text/csv"
            )
            response.headers["Content-Disposition"] = (
                f"attachment; filename=test-{test_details.engagement_number}.csv"
            )
            return response

        return test_details
    except TestNotFoundError:
        raise HTTPException(status_code=HTTP_404_NOT_FOUND, detail="Not Found")


@router.put("/{document_guid}/archive", status_code=204)
async def archive_test(
    document_guid: UUID,
    db: Session = Depends(get_db),
    user_claims=Depends(cognito_jwt_access_token_authoriser),
):
    """Archive an existing test.

    Args:
        document_guid (UUID): Test Document Guid
        db (Session): Database session
        user_claims: Authentication dependency for the endpoint, also returns claims from the Cognito JWT
    """
    crud.archive_test(db, document_guid, user_claims["sub"])
    return Response(status_code=HTTP_204_NO_CONTENT)


@router.put("/{document_guid}/unarchive", status_code=204)
async def unarchive_test(
    document_guid: UUID,
    db: Session = Depends(get_db),
    user_claims=Depends(cognito_jwt_access_token_authoriser),
):
    """Unarchive an existing test.

    Args:
        document_guid (UUID): Test Document Guid
        db (Session): Database session
        user_claims: Authentication dependency for the endpoint, also returns claims from the Cognito JWT
    """
    crud.unarchive_test(db, document_guid, user_claims["sub"])
    return Response(status_code=HTTP_204_NO_CONTENT)


@router.get(
    "/{document_guid}/phases/{phase_number}/findings/{finding_number}",
    response_model=FindingDetails,
)
async def get_finding(
    document_guid: UUID,
    phase_number: int,
    finding_number: int,
    export: bool = False,
    db: Session = Depends(get_db),
    user_claims=Depends(cognito_jwt_access_token_authoriser),
):
    """Get details of a Finding given its ID and its Test ID that it belongs to.

    Args:
        document_guid (UUID): Test Document Guid
        phase_number (int): Phase number belonging to the Test
        finding_number (int): Finding number belonging to the Phase
        export (bool): Return an export or not
        db (Session): Database session
        user_claims: Authentication dependency for the endpoint, also returns claims from the Cognito JWT
    """
    try:
        finding = crud.get_finding(
            db,
            user_claims["sub"],
            document_guid=document_guid,
            phase_number=phase_number,
            finding_number=finding_number,
        )
        return finding
    except FindingNotFoundError:
        raise HTTPException(status_code=HTTP_404_NOT_FOUND, detail="Not Found")


class FindingRequestBody(BaseModel):
    finding_status: FindingStatus


@router.put(
    "/{document_guid}/phases/{phase_number}/findings/{finding_number}/status",
)
async def set_finding_status(
    document_guid: UUID,
    phase_number: int,
    finding_number: int,
    finding_request_body: FindingRequestBody,
    db: Session = Depends(get_db),
    user_claims=Depends(cognito_jwt_access_token_authoriser),
):
    """Set the status of a finding.

    Args:
        document_guid (UUID): Test Document Guid
        phase_number (int): Phase number belonging to the Test
        finding_number (int): Finding number belonging to the Phase
        finding_request_body (FindingRequestBody): New value of the status of the Finding
        db (Session): Database session
        user_claims: Authentication dependency for the endpoint, also returns claims from the Cognito JWT
    """
    try:
        crud.set_finding_status(
            db,
            user_claims["sub"],
            document_guid=document_guid,
            phase_number=phase_number,
            finding_number=finding_number,
            status=finding_request_body.finding_status.value,
        )
    except FindingNotFoundError:
        raise HTTPException(status_code=HTTP_404_NOT_FOUND, detail="Not Found")
