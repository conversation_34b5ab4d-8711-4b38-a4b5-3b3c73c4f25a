import structlog
from fastapi import APIRouter, Depends
from pydantic import BaseModel, EmailStr

from app.dependencies import cognito_jwt_id_token_authoriser

logger = structlog.get_logger()

router = APIRouter()


class Profile(BaseModel):
    given_name: str
    family_name: str
    email: EmailStr
    groups: list[str]


@router.get("/profile", response_model=Profile)
async def get_user_profile(
    user_claims: dict = Depends(cognito_jwt_id_token_authoriser),
) -> dict:
    """Get the profile details of a user.

    Args:
        user_claims (dict): User claims from the incoming JWT.

    Returns:
        dict
    """
    user_groups: list[str] = []
    if "cognito:groups" in user_claims:
        user_groups = user_claims["cognito:groups"]

    return {
        "given_name": user_claims["given_name"],
        "family_name": user_claims["family_name"],
        "email": user_claims["email"],
        "groups": user_groups,
    }
