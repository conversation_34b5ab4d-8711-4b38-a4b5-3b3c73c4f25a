from enum import StrEnum

from sqlalchemy import (
    Boolean,
    Column,
    DateTime,
    Enum,
    ForeignKey,
    Integer,
    String,
    Table,
    Text,
    UniqueConstraint,
)
from sqlalchemy.dialects.postgresql import INET, UUID
from sqlalchemy.orm import relationship

from app.database import Base

user_tests = Table(
    "user_tests",
    Base.metadata,
    Column("test_id", UUID, ForeignKey("tests.id")),
    Column("user_id", UUID, ForeignKey("users.id")),
    Column("is_test_archived", Boolean, default=False, nullable=False),
)


class ExportStatus(StrEnum):
    OK = "OK"
    ERROR = "ERROR"


class Test(Base):
    __tablename__ = "tests"

    id = Column(UUID(as_uuid=True), primary_key=True, unique=True, nullable=False)
    engagement_number = Column(Integer, nullable=False)
    scoring_system_id = Column(
        Integer, ForeignKey("scoring_systems.id"), nullable=False
    )
    scoring_system = relationship("ScoringSystem", back_populates="tests")
    name = Column(String, nullable=False)
    time_of_last_export = Column(DateTime, nullable=False)
    export_status = Column(Enum(ExportStatus), nullable=False)
    export_error = Column(String, nullable=True)
    phases = relationship("Phase", back_populates="test", cascade="all, delete-orphan")
    users = relationship("User", secondary=user_tests, back_populates="tests")


class Phase(Base):
    __tablename__ = "phases"

    id = Column(Integer, primary_key=True)
    guid = Column(UUID(as_uuid=True), nullable=False)
    number = Column(Integer, nullable=False)
    test_id = Column(UUID, ForeignKey("tests.id"), nullable=False)
    test = relationship("Test", back_populates="phases")
    name = Column(String, nullable=True)
    findings = relationship(
        "Finding", back_populates="phase", cascade="all, delete-orphan"
    )
    __table_args__ = (UniqueConstraint("guid", "test_id", name="uc_guid_test_id"),)


class AssociatedHost(Base):
    __tablename__ = "associated_hosts"

    id = Column(Integer, primary_key=True)
    finding_id = Column(UUID, ForeignKey("findings.id"), nullable=False)
    finding = relationship("Finding", back_populates="associated_hosts")
    ip_address = Column(INET, nullable=False)
    network = Column(String, nullable=False)
    port = Column(Integer, nullable=False)
    protocol = Column(String, nullable=False)


class FindingStatus(StrEnum):
    OPEN = "OPEN"
    CLOSED = "CLOSED"
    FALSE_POSITIVE = "FALSE_POSITIVE"


class Finding(Base):
    __tablename__ = "findings"

    id = Column(UUID(as_uuid=True), primary_key=True, unique=True, nullable=False)
    number = Column(Integer, nullable=False)
    phase_id = Column(Integer, ForeignKey("phases.id"), nullable=False)
    phase = relationship("Phase", back_populates="findings")
    name = Column(String, nullable=True)
    base_severity = Column(String, nullable=False)
    severity_additional_context = Column(String, nullable=True)
    vector_string = Column(String, nullable=False)
    status = Column(
        Enum(FindingStatus),
        server_default=FindingStatus.OPEN,
        default=FindingStatus.OPEN,
        nullable=False,
    )
    description = Column(Text, nullable=True)
    recommendation = Column(Text, nullable=True)
    supporting_material = Column(Text, nullable=True)
    associated_hosts = relationship(
        "AssociatedHost", back_populates="finding", cascade="all, delete-orphan"
    )


class User(Base):
    __tablename__ = "users"

    id = Column(UUID(as_uuid=True), primary_key=True, unique=True, nullable=False)
    email = Column(String, unique=True, nullable=False)
    tests = relationship("Test", secondary=user_tests, back_populates="users")


class ScoringSystem(Base):
    __tablename__ = "scoring_systems"

    id = Column(Integer, primary_key=True)
    name = Column(String, unique=True, nullable=False)
    tests = relationship("Test", back_populates="scoring_system")
