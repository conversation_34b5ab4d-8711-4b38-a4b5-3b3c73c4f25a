from functools import lru_cache

from pydantic import AnyHttpUrl, SecretStr
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    # Database
    db_name: str
    db_user: str
    db_password: SecretStr
    db_host: str
    db_port: int

    # AWS
    aws_default_region: str

    # CORS
    cors_origin_url: AnyHttpUrl

    # Cognito
    cognito_user_pool_id: str
    cognito_app_client_id: str

    # S3
    tenant_assets_bucket: str

    model_config = SettingsConfigDict(env_file=".env")


@lru_cache()
def get_settings() -> Settings:
    """Retrieve all settings.

    Returns:
        Settings
    """
    return Settings()
