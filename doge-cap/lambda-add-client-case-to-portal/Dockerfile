FROM public.ecr.aws/lambda/python:3.13

# Install uv.
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

# Copy the application into the container.
COPY pyproject.toml uv.lock lambda_function.py schemas.py ${LAMBDA_TASK_ROOT}/

# Install the application dependencies.
RUN uv export --format=requirements-txt --frozen -o requirements.txt && \
    uv pip install -r requirements.txt --system && \
    rm requirements.txt

# Run the application.
CMD ["lambda_function.lambda_handler"]
