# lambda-add-client-case-to-portal

<!-- TOC -->
* [lambda-add-client-case-to-portal](#lambda-add-client-case-to-portal)
  * [Emulating a SQS event as a `pytest` fixture](#emulating-a-sqs-event-as-a-pytest-fixture)
  * [Testing](#testing)
    * [Without coverage](#without-coverage)
    * [With coverage](#with-coverage)
<!-- TOC -->

## Emulating a SQS event as a `pytest` fixture

The folder `tests/events` contains JSON files that represent Doge exports. Those can be used as `pytest` fixtures that
can be passed to the main Lambda handler of the app found in `lambda_function.py`.

Then creating an event can be as followed:

```python
import json
from pathlib import Path

import pytest


@pytest.fixture
def context_risk_rating_event():
    """Returns a Context Risk Rating event as a fixture."""
    with open(
            Path(Path.cwd() / "tests" / "events" / "context_risk_rating_message.json")
    ) as f:
        event = f.read()
        event_body_data = {"Message": event}
        event_body = {"Records": [{"body": json.dumps(event_body_data)}]}
        yield event_body
```

...which then can be used in a test:

```python
from lambda_function import lambda_handler


def test_send_context_risk_rating_message(context_risk_rating_event):
    """Test storing a Context Risk Rating message to the Portal from an event JSON.

    Args:
        context_risk_rating_event: Fixture emulating an event containing a message with a Context Risk Rating test
    Returns: None
    """
    lambda_handler(context_risk_rating_event, None)
```

## Testing

### Without coverage

```bash
pytest
```

### With coverage

```bash
pytest --cov=lambda_function tests/ 
```
