package db

import (
	"errors"
	"sort"
	"strings"

	"github.com/google/uuid"
)

type TestResponse struct {
	EngagementNumber      int            `json:"engagement_number"`
	DocumentGuid          string         `json:"document_guid"`
	Name                  string         `json:"name"`
	TimeOfLastExport      string         `json:"time_of_last_export"`
	FindingCountsResponse map[string]int `json:"findings"`
}

func GetTests(username string) []TestResponse {

	queries.
		groupedTests := make(map[string]TestResponse)
	for _, testDBResult := range testDBResults {
		testResponse, exists := groupedTests[testDBResult.DocumentGuid]

		if !exists {
			findingCounts := map[string]int{
				"total":    0,
				"critical": 0,
				"high":     0,
				"medium":   0,
				"low":      0,
				"minimal":  0,
			}
			testResponse = TestResponse{
				EngagementNumber:      testDBResult.EngagementNumber,
				DocumentGuid:          testDBResult.DocumentGuid,
				Name:                  testDBResult.Name,
				TimeOfLastExport:      testDBResult.TimeOfLastExport.Format("2006-01-02 15:04:05"),
				FindingCountsResponse: findingCounts}
		}

		if testDBResult.RatingName != nil {
			testResponse.FindingCountsResponse[strings.ToLower(*testDBResult.RatingName)] = testDBResult.Count
			testResponse.FindingCountsResponse["total"] += testDBResult.Count
		}
		groupedTests[testDBResult.DocumentGuid] = testResponse
	}

	testResponses := make([]TestResponse, 0)
	for _, groupedTest := range groupedTests {
		testResponses = append(testResponses, groupedTest)
	}
	return testResponses
}

type FindingResponse struct {
	FindingNumber       int    `json:"finding_number"`
	Name                string `json:"name"`
	SummaryRatingValue  string `json:"summary_rating"`
	SummaryRatingColour string `json:"summary_rating_colour"`
}

type PhaseResponse struct {
	PhaseNumber      int               `json:"phase_number"`
	Name             string            `json:"name"`
	FindingResponses []FindingResponse `json:"findings"`
}

type TestDetailsResponse struct {
	DocumentGuid     string          `json:"document_guid"`
	EngagementNumber int             `json:"engagement_number"`
	Name             string          `json:"name"`
	PhasesResponse   []PhaseResponse `json:"phases"`
}

type PhasesSliceResponse []PhaseResponse

func (s PhasesSliceResponse) Len() int           { return len(s) }
func (s PhasesSliceResponse) Swap(i, j int)      { s[i], s[j] = s[j], s[i] }
func (s PhasesSliceResponse) Less(i, j int) bool { return s[i].PhaseNumber < s[j].PhaseNumber }

func GetTest(username string, documentGuid string) (*TestDetailsResponse, error) {

	groupedPhases := make(map[string]PhaseResponse)

	if len(findingDBResults) > 0 {
		engagementNumber := findingDBResults[0].EngagementNumber
		testName := findingDBResults[0].Name
		for _, findingDBResult := range findingDBResults {
			findingResponse := FindingResponse{
				FindingNumber:       findingDBResult.FindingNumber,
				Name:                findingDBResult.FindingName,
				SummaryRatingValue:  findingDBResult.SummaryRatingValueName,
				SummaryRatingColour: findingDBResult.SummaryRatingValueColour}

			phaseResponse, exists := groupedPhases[findingDBResult.PhaseID]
			if !exists {
				phaseResponse = PhaseResponse{
					Name:             findingDBResult.PhaseName,
					PhaseNumber:      findingDBResult.PhaseNumber,
					FindingResponses: []FindingResponse{},
				}
			}
			phaseResponse.FindingResponses = append(phaseResponse.FindingResponses, findingResponse)
			groupedPhases[findingDBResult.PhaseID] = phaseResponse
		}

		phaseResponses := make([]PhaseResponse, 0)
		for _, groupedPhase := range groupedPhases {
			phaseResponses = append(phaseResponses, groupedPhase)
		}

		sort.Sort(PhasesSliceResponse(phaseResponses))

		testDetailsResponse := TestDetailsResponse{
			DocumentGuid:     documentGuid,
			EngagementNumber: engagementNumber,
			Name:             testName,
			PhasesResponse:   phaseResponses}

		return &testDetailsResponse, nil
	} else {
		return nil, errors.New("no results found")
	}
}

type FindingDetailsResponse struct {
	FindingNumber      int     `json:"finding_number"`
	EngagementNumber   int     `json:"engagement_number"`
	PhaseNumber        int     `json:"phase_number"`
	Name               string  `json:"name"`
	Description        *string `json:"description"`
	Recommendation     *string `json:"recommendation"`
	SupportingMaterial *string `json:"supporting_material"`
}

func GetFindingDetails(username string, documentGuid string, phaseNumber string, findingNumber string) (FindingDetailsResponse, error) {

	findingDetailsResponse := FindingDetailsResponse{
		FindingNumber:      findingDetailsDBResult.FindingNumber,
		EngagementNumber:   findingDetailsDBResult.EngagementNumber,
		PhaseNumber:        findingDetailsDBResult.PhaseNumber,
		Name:               findingDetailsDBResult.Name,
		Description:        findingDetailsDBResult.Description,
		Recommendation:     findingDetailsDBResult.Recommendation,
		SupportingMaterial: findingDetailsDBResult.SupportingMaterial}

	return findingDetailsResponse, nil
}

type TestWithAssignedUsersResponse struct {
	DocumentGuid     string   `json:"document_guid"`
	EngagementNumber int      `json:"engagement_number"`
	Name             string   `json:"name"`
	AssignedUsers    []string `json:"assigned_users"`
	TimeOfLastExport string   `json:"time_of_last_export"`
	ExportStatus     string   `json:"export_status"`
}

func GetTestWithAssignedUsers(documentGuid string) (*TestWithAssignedUsersResponse, error) {

	if result.RowsAffected == 0 {
		return nil, result.Error
	}
	t := TestWithAssignedUsersResponse{
		DocumentGuid:     test.ID.String(),
		EngagementNumber: test.EngagementNumber,
		Name:             test.Name,
		TimeOfLastExport: test.TimeOfLastExport.Format("2006-01-02 15:04:05"),
		AssignedUsers:    make([]string, 0),
		ExportStatus:     test.ExportStatus}
	for _, user := range test.Users {
		t.AssignedUsers = append(t.AssignedUsers, user.Email)
	}

	return &t, nil
}

func GetTestsWithAssignedUsers() []TestWithAssignedUsersResponse {
	var tests []Test

	response := make([]TestWithAssignedUsersResponse, 0)

	for _, test := range tests {
		t := TestWithAssignedUsersResponse{
			DocumentGuid:     test.ID.String(),
			EngagementNumber: test.EngagementNumber,
			Name:             test.Name,
			AssignedUsers:    make([]string, 0)}
		for _, user := range test.Users {
			t.AssignedUsers = append(t.AssignedUsers, user.Email)
		}
		response = append(response, t)
	}
	return response
}

func AssignUserToTest(documentGuid string, username uuid.UUID, userEmail string) error {
	var test Test
	var user User

	// First check if the Test exists in the database

	// Next check if the user to be assigned to the Test, exists in the database

	// If they don't exist in the database, add them for the first time and immediately associate them with the Test

	// If they are an existing user, check if they are already assigned to the Test, if they are, just error

	// Finally, for that existing user, associate them with the Test

	return nil
}

func RemoveUserFromTest(documentGuid string, userEmail string) error {

	return nil
}

func ArchiveTest(documentGuid uuid.UUID, userId uuid.UUID) {

}
