// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.25.0

package db

import (
	"database/sql/driver"
	"fmt"
	"net/netip"

	"github.com/jackc/pgx/v5/pgtype"
)

type Exportstatus string

const (
	ExportstatusOK    Exportstatus = "OK"
	ExportstatusERROR Exportstatus = "ERROR"
)

func (e *Exportstatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = Exportstatus(s)
	case string:
		*e = Exportstatus(s)
	default:
		return fmt.Errorf("unsupported scan type for Exportstatus: %T", src)
	}
	return nil
}

type NullExportstatus struct {
	Exportstatus Exportstatus
	Valid        bool // Valid is true if Exportstatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullExportstatus) Scan(value interface{}) error {
	if value == nil {
		ns.Exportstatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.Exportstatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullExportstatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.Exportstatus), nil
}

type Findingstatus string

const (
	FindingstatusOPEN          Findingstatus = "OPEN"
	FindingstatusCLOSED        Findingstatus = "CLOSED"
	FindingstatusFALSEPOSITIVE Findingstatus = "FALSE_POSITIVE"
)

func (e *Findingstatus) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = Findingstatus(s)
	case string:
		*e = Findingstatus(s)
	default:
		return fmt.Errorf("unsupported scan type for Findingstatus: %T", src)
	}
	return nil
}

type NullFindingstatus struct {
	Findingstatus Findingstatus
	Valid         bool // Valid is true if Findingstatus is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullFindingstatus) Scan(value interface{}) error {
	if value == nil {
		ns.Findingstatus, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.Findingstatus.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullFindingstatus) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.Findingstatus), nil
}

type AssociatedHost struct {
	ID        int32
	FindingID pgtype.UUID
	IpAddress netip.Addr
	Network   string
	Port      int32
	Protocol  string
}

type Finding struct {
	ID                 pgtype.UUID
	Number             int32
	PhaseID            int32
	Name               pgtype.Text
	BaseSeverity       string
	VectorString       string
	Status             Findingstatus
	Description        pgtype.Text
	Recommendation     pgtype.Text
	SupportingMaterial pgtype.Text
}

type Phase struct {
	ID     int32
	Guid   pgtype.UUID
	Number int32
	TestID pgtype.UUID
	Name   pgtype.Text
}

type ScoringSystem struct {
	ID   int32
	Name string
}

type Test struct {
	ID               pgtype.UUID
	EngagementNumber int32
	ScoringSystemID  int32
	Name             string
	TimeOfLastExport pgtype.Timestamp
	ExportStatus     Exportstatus
	ExportError      pgtype.Text
}

type User struct {
	ID    pgtype.UUID
	Email string
}

type UserTest struct {
	TestID         pgtype.UUID
	UserID         pgtype.UUID
	IsTestArchived bool
}
