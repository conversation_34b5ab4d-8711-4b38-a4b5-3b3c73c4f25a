// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.25.0
// source: query.sql

package db

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
)

const archiveTest = `-- name: ArchiveTest :exec
UPDATE user_tests 
SET is_test_archived = true
WHERE user_tests.user_id = $1 AND user_tests.test_id = $2
`

type ArchiveTestParams struct {
	UserID pgtype.UUID
	TestID pgtype.UUID
}

func (q *Queries) ArchiveTest(ctx context.Context, arg ArchiveTestParams) error {
	_, err := q.db.Exec(ctx, archiveTest, arg.UserID, arg.TestID)
	return err
}

const assignTestToUser = `-- name: AssignTestToUser :exec
INSERT INTO user_tests (test_id, user_id) VALUES ($1, $2)
`

type AssignTestToUserParams struct {
	TestID pgtype.UUID
	UserID pgtype.UUID
}

func (q *Queries) AssignTestToUser(ctx context.Context, arg AssignTestToUserParams) error {
	_, err := q.db.Exec(ctx, assignTestToUser, arg.TestID, arg.UserID)
	return err
}

const deleteTest = `-- name: DeleteTest :exec
DELETE FROM tests WHERE tests.id = $1
`

func (q *Queries) DeleteTest(ctx context.Context, id pgtype.UUID) error {
	_, err := q.db.Exec(ctx, deleteTest, id)
	return err
}

const getFinding = `-- name: GetFinding :one
SELECT findings.id, findings.number, findings.phase_id, findings.name, findings.base_severity, findings.vector_string, findings.status, findings.description, findings.recommendation, findings.supporting_material, scoring_systems.id, scoring_systems.name, tests.id, tests.engagement_number, tests.scoring_system_id, tests.name, tests.time_of_last_export, tests.export_status, tests.export_error, phases.id, phases.guid, phases.number, phases.test_id, phases.name
FROM findings
JOIN phases ON phases.id = findings.phase_id
JOIN tests ON tests.id = phases.test_id
JOIN user_tests ON user_tests.test_id = tests.id
JOIN users ON users.id = user_tests.user_id
JOIN scoring_systems ON scoring_systems.id = tests.scoring_system_id
WHERE users.id = $1
AND findings.number = $2
AND phases.number = $3
AND tests.id = $4
`

type GetFindingParams struct {
	ID       pgtype.UUID
	Number   int32
	Number_2 int32
	ID_2     pgtype.UUID
}

type GetFindingRow struct {
	Finding       Finding
	ScoringSystem ScoringSystem
	Test          Test
	Phase         Phase
}

func (q *Queries) GetFinding(ctx context.Context, arg GetFindingParams) (GetFindingRow, error) {
	row := q.db.QueryRow(ctx, getFinding,
		arg.ID,
		arg.Number,
		arg.Number_2,
		arg.ID_2,
	)
	var i GetFindingRow
	err := row.Scan(
		&i.Finding.ID,
		&i.Finding.Number,
		&i.Finding.PhaseID,
		&i.Finding.Name,
		&i.Finding.BaseSeverity,
		&i.Finding.VectorString,
		&i.Finding.Status,
		&i.Finding.Description,
		&i.Finding.Recommendation,
		&i.Finding.SupportingMaterial,
		&i.ScoringSystem.ID,
		&i.ScoringSystem.Name,
		&i.Test.ID,
		&i.Test.EngagementNumber,
		&i.Test.ScoringSystemID,
		&i.Test.Name,
		&i.Test.TimeOfLastExport,
		&i.Test.ExportStatus,
		&i.Test.ExportError,
		&i.Phase.ID,
		&i.Phase.Guid,
		&i.Phase.Number,
		&i.Phase.TestID,
		&i.Phase.Name,
	)
	return i, err
}

const getFindingsCountsPerTestForUser = `-- name: GetFindingsCountsPerTestForUser :many
SELECT tests.id, tests.engagement_number, tests.scoring_system_id, tests.name, tests.time_of_last_export, tests.export_status, tests.export_error, findings.base_severity, COUNT(*)
FROM tests
INNER JOIN user_tests on tests.id = user_tests.test_id
INNER JOIN users ON users.id = user_tests.user_id
FULL JOIN phases ON tests.id = phases.test_id
FULL JOIN findings ON phases.id = findings.phase_id
WHERE users.id = $1
GROUP BY tests.id, findings.base_severity
`

type GetFindingsCountsPerTestForUserRow struct {
	ID               pgtype.UUID
	EngagementNumber int32
	ScoringSystemID  int32
	Name             string
	TimeOfLastExport pgtype.Timestamp
	ExportStatus     Exportstatus
	ExportError      pgtype.Text
	BaseSeverity     pgtype.Text
	Count            int64
}

func (q *Queries) GetFindingsCountsPerTestForUser(ctx context.Context, id pgtype.UUID) ([]GetFindingsCountsPerTestForUserRow, error) {
	rows, err := q.db.Query(ctx, getFindingsCountsPerTestForUser, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetFindingsCountsPerTestForUserRow
	for rows.Next() {
		var i GetFindingsCountsPerTestForUserRow
		if err := rows.Scan(
			&i.ID,
			&i.EngagementNumber,
			&i.ScoringSystemID,
			&i.Name,
			&i.TimeOfLastExport,
			&i.ExportStatus,
			&i.ExportError,
			&i.BaseSeverity,
			&i.Count,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTestDetails = `-- name: GetTestDetails :many
SELECT tests.id, tests.engagement_number, tests.scoring_system_id, tests.name, tests.time_of_last_export, tests.export_status, tests.export_error, findings.id, findings.number, findings.phase_id, findings.name, findings.base_severity, findings.vector_string, findings.status, findings.description, findings.recommendation, findings.supporting_material
FROM tests
JOIN user_tests on tests.id = user_tests.test_id
JOIN users ON users.id = user_tests.user_id
JOIN phases ON tests.id = phases.test_id
JOIN findings ON phases.id = findings.phase_id
WHERE users.id = $1 AND tests.id = $2
`

type GetTestDetailsParams struct {
	ID   pgtype.UUID
	ID_2 pgtype.UUID
}

type GetTestDetailsRow struct {
	Test    Test
	Finding Finding
}

func (q *Queries) GetTestDetails(ctx context.Context, arg GetTestDetailsParams) ([]GetTestDetailsRow, error) {
	rows, err := q.db.Query(ctx, getTestDetails, arg.ID, arg.ID_2)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetTestDetailsRow
	for rows.Next() {
		var i GetTestDetailsRow
		if err := rows.Scan(
			&i.Test.ID,
			&i.Test.EngagementNumber,
			&i.Test.ScoringSystemID,
			&i.Test.Name,
			&i.Test.TimeOfLastExport,
			&i.Test.ExportStatus,
			&i.Test.ExportError,
			&i.Finding.ID,
			&i.Finding.Number,
			&i.Finding.PhaseID,
			&i.Finding.Name,
			&i.Finding.BaseSeverity,
			&i.Finding.VectorString,
			&i.Finding.Status,
			&i.Finding.Description,
			&i.Finding.Recommendation,
			&i.Finding.SupportingMaterial,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTestForID = `-- name: GetTestForID :one
SELECT tests.id, tests.engagement_number, tests.scoring_system_id, tests.name, tests.time_of_last_export, tests.export_status, tests.export_error
FROM tests
WHERE tests.id = $1
`

func (q *Queries) GetTestForID(ctx context.Context, id pgtype.UUID) (Test, error) {
	row := q.db.QueryRow(ctx, getTestForID, id)
	var i Test
	err := row.Scan(
		&i.ID,
		&i.EngagementNumber,
		&i.ScoringSystemID,
		&i.Name,
		&i.TimeOfLastExport,
		&i.ExportStatus,
		&i.ExportError,
	)
	return i, err
}

const getTestWithAssignedUsers = `-- name: GetTestWithAssignedUsers :many
SELECT tests.id, tests.engagement_number, tests.scoring_system_id, tests.name, tests.time_of_last_export, tests.export_status, tests.export_error, users.email
FROM tests
INNER JOIN user_tests ON tests.id = user_tests.test_id
INNER JOIN users ON users.id = user_tests.user_id
WHERE tests.id = $1
`

type GetTestWithAssignedUsersRow struct {
	ID               pgtype.UUID
	EngagementNumber int32
	ScoringSystemID  int32
	Name             string
	TimeOfLastExport pgtype.Timestamp
	ExportStatus     Exportstatus
	ExportError      pgtype.Text
	Email            string
}

func (q *Queries) GetTestWithAssignedUsers(ctx context.Context, id pgtype.UUID) ([]GetTestWithAssignedUsersRow, error) {
	rows, err := q.db.Query(ctx, getTestWithAssignedUsers, id)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetTestWithAssignedUsersRow
	for rows.Next() {
		var i GetTestWithAssignedUsersRow
		if err := rows.Scan(
			&i.ID,
			&i.EngagementNumber,
			&i.ScoringSystemID,
			&i.Name,
			&i.TimeOfLastExport,
			&i.ExportStatus,
			&i.ExportError,
			&i.Email,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getTestsWithAssignedUsers = `-- name: GetTestsWithAssignedUsers :many
SELECT tests.id, tests.engagement_number, tests.scoring_system_id, tests.name, tests.time_of_last_export, tests.export_status, tests.export_error, users.email
FROM tests
INNER JOIN user_tests on tests.id = user_tests.test_id
INNER JOIN users ON users.id = user_tests.user_id
`

type GetTestsWithAssignedUsersRow struct {
	ID               pgtype.UUID
	EngagementNumber int32
	ScoringSystemID  int32
	Name             string
	TimeOfLastExport pgtype.Timestamp
	ExportStatus     Exportstatus
	ExportError      pgtype.Text
	Email            string
}

func (q *Queries) GetTestsWithAssignedUsers(ctx context.Context) ([]GetTestsWithAssignedUsersRow, error) {
	rows, err := q.db.Query(ctx, getTestsWithAssignedUsers)
	if err != nil {
		return nil, err
	}
	defer rows.Close()
	var items []GetTestsWithAssignedUsersRow
	for rows.Next() {
		var i GetTestsWithAssignedUsersRow
		if err := rows.Scan(
			&i.ID,
			&i.EngagementNumber,
			&i.ScoringSystemID,
			&i.Name,
			&i.TimeOfLastExport,
			&i.ExportStatus,
			&i.ExportError,
			&i.Email,
		); err != nil {
			return nil, err
		}
		items = append(items, i)
	}
	if err := rows.Err(); err != nil {
		return nil, err
	}
	return items, nil
}

const getUserForEmail = `-- name: GetUserForEmail :one
SELECT users.id, users.email
FROM users
WHERE users.email = $1
`

func (q *Queries) GetUserForEmail(ctx context.Context, email string) (User, error) {
	row := q.db.QueryRow(ctx, getUserForEmail, email)
	var i User
	err := row.Scan(&i.ID, &i.Email)
	return i, err
}

const removeUserFromTest = `-- name: RemoveUserFromTest :exec
DELETE FROM user_tests WHERE user_tests.user_id = $1 AND user_tests.test_id = $2
`

type RemoveUserFromTestParams struct {
	UserID pgtype.UUID
	TestID pgtype.UUID
}

func (q *Queries) RemoveUserFromTest(ctx context.Context, arg RemoveUserFromTestParams) error {
	_, err := q.db.Exec(ctx, removeUserFromTest, arg.UserID, arg.TestID)
	return err
}

const unarchiveTest = `-- name: UnarchiveTest :exec
UPDATE user_tests 
SET is_test_archived = true
WHERE user_tests.user_id = $1 AND user_tests.test_id = $2
`

type UnarchiveTestParams struct {
	UserID pgtype.UUID
	TestID pgtype.UUID
}

func (q *Queries) UnarchiveTest(ctx context.Context, arg UnarchiveTestParams) error {
	_, err := q.db.Exec(ctx, unarchiveTest, arg.UserID, arg.TestID)
	return err
}
