INSERT INTO public.scoring_systems (name)
VALUES ('ContextRiskRating');

INSERT INTO public.summary_rating_values (scoring_system_id, name, colour)
VALUES (1, 'CRITICAL', '#ffadb5');
INSERT INTO public.summary_rating_values (scoring_system_id, name, colour)
VALUES (1, 'HIGH', '#ffc999');
INSERT INTO public.summary_rating_values (scoring_system_id, name, colour)
VALUES (1, 'MEDIUM', '#fffbd6');
INSERT INTO public.summary_rating_values (scoring_system_id, name, colour)
VALUES (1, 'LOW', '#c1ffb9');
INSERT INTO public.summary_rating_values (scoring_system_id, name, colour)
VALUES (1, 'MINIMAL', '#99b3f9');

INSERT INTO public.detail_rating_attributes (name, order_of_appearance, scoring_system_id)
VALUES ('Risk', 1, 1);
INSERT INTO public.detail_rating_attributes (name, order_of_appearance, scoring_system_id)
VALUES ('Impact', 2, 1);
INSERT INTO public.detail_rating_attributes (name, order_of_appearance, scoring_system_id)
VALUES ('Likelihood', 3, 1);

INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (1, 'CRITICAL', '#ffadb5');
INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (1, 'HIGH', '#ffc999');
INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (1, 'MEDIUM', '#fffbd6');
INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (1, 'LOW', '#c1ffb9');
INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (1, 'MINIMAL', '#99b3f9');
INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (2, 'CRITICAL', '#ffadb5');
INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (2, 'HIGH', '#ffc999');
INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (2, 'MEDIUM', '#fffbd6');
INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (2, 'LOW', '#c1ffb9');
INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (2, 'VERY_LOW', '#99b3f9');
INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (3, 'VERY_HIGH', '#ffadb5');
INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (3, 'HIGH', '#ffc999');
INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (3, 'MEDIUM', '#fffbd6');
INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (3, 'LOW', '#c1ffb9');
INSERT INTO public.detail_rating_values (detail_rating_attribute_id, name, colour)
VALUES (3, 'VERY_LOW', '#99b3f9');

INSERT INTO public.tests (engagement_number, document_guid, scoring_system_id, name)
VALUES (1111, '65a3a20a-1c43-4f79-8362-680ade808fff', 1, 'General Engagement');

INSERT INTO public.phases (phase_number, test_id, name)
VALUES (1, 1, 'phase title 1');
INSERT INTO public.phases (phase_number, test_id, name)
VALUES (2, 1, 'phase title 2');

INSERT INTO public.findings (finding_number, summary_rating_value_id, phase_id, name, description, recommendation,
                             supporting_material)
VALUES (1, 2, 1, 'SQL Injection', '<p>The application is vulnerable to SQL injection (SQLi) in <span class="highlighted"> a number of locations/feature xxxx</span>.</p>
<p>This allows an attacker to manipulate an SQL query within the application by sending additional SQL commands to the application server. An attacker can use it to perform malicious tasks such as to extract, change or delete sensitive information within the database supporting the application, and potentially run system commands on the database host. The vulnerability was found <span class="highlighted"> ***WHERE***</span></p>
<p><span class="highlighted"> BLIND ONLY: </span> The injection point <span class="highlighted"> /s were/was </span> found to be blind. This means that it&nbsp;was not possible to view the data or query response directly. Instead, the attacker must use other techniques to determine the result of the query. <span class="highlighted">It was possible to DESCRIBE TECHNIQUE USED </span> form queries that provided differing responses depending on whether the query was successful or not. Each value could be enumerated using this technique.</p>
<p>Using these vulnerabilities, it was possible to enumerate the database and extract content <span class="highlighted"> ***LIKE WHAT*** </span> . <span class="highlighted"> ***ANY OTHER ACCESS e.g. xp_cmdshell OR HTTP_UTL.request**** </span> .</p>
<p>An example request can be found in the supporting material <span class="highlighted"> ***WHICH*** </span></p>
<p><span class="highlighted"> CVSS: Score should be modified to reflect whether the SQLi is auth or unauth, and whether the underlying database server allows for high/complete CIA. For example, if SQL Server runs as SYSTEM and the application uses the "sa" account then availability could also be affected as an attacker could shut down the server.<br /></span></p>',
        e'<html><body><p><strong>Use Prepared Statements &amp; Validate Input</strong></p><p>Use prepared statements with parameterised queries to prevent SQL injection. With prepared statements, parameter values (\'bind variables\') are substituted for placeholders in query templates after the database has parsed the statement. This means they cannot be used to change the structure of the query. This requires that the initial template is not derived from external input.</p>
<p>In some cases, a dynamic value cannot be used for a parameter. Refactor the code to avoid the need to do this. If this is not possible, correct input validation will provide a defence.</p>
<p>Input validation should always be used in addition to prepared statements. External input should be validated against an allow list of expected values, regardless of whether it comes from a perceived trusted source or not. This is a fundamental secure coding principle that would prevent potentially malicious code being written to the database (for example, via second-order SQLi).</p>
<p>Finally, ensure the database is sufficiently hardened and accessed with a minimal set of privileges in case SQLi is achieved.</p>
<p>Further information can be found below:</p>
<ul>
<li><a href="https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html">https://cheatsheetseries.owasp.org/cheatsheets/SQL_Injection_Prevention_Cheat_Sheet.html</a></li>
<li><a href="https://cheatsheetseries.owasp.org/cheatsheets/Query_Parameterization_Cheat_Sheet.html">https://cheatsheetseries.owasp.org/cheatsheets/Query_Parameterization_Cheat_Sheet.html</a></li>
</ul></body></html>', '');
INSERT INTO public.findings (finding_number, summary_rating_value_id, phase_id, name, description, recommendation,
                             supporting_material)
VALUES (2, 4, 1, 'No Password Expiry', '<p>Database accounts that rely on password authentication require that passwords are kept secret at all times. Because passwords are vulnerable to theft, forgery, and misuse, Oracle database uses a password management policy. DBAs and security officers control this policy through user&nbsp;profiles.</p>
<p>The PASSWORD_LIFE_TIME value of the database profiles specifies the number days a password can be used before is is required to be changed.</p>
<p><span class="highlighted"> All database accounts are assign to the DEFAULT&nbsp;profile which has no password expiry assigned, meaning no passwords will expire. </span></p>
<p><span class="highlighted"> Of the 33 open database accounts only three had changed there password in the last 90 days. </span> <br /><br /></p>', '<html><body><p><strong>Assign Password Expiry To All Accounts</strong></p><p>Ensure that all database accounts are assigned a profile with password expiry that is inline with the organisations password policy.</p>
<p>For example, set  PASSWORD_LIFE_TIME to 60 and PASSWORD_GRACE_TIME to 30.  This will expire the password after 60 day but still allow the user to login for a further 30 with a warning.  After which the user will not be allowed to logon.</p></body></html>',
        '');
INSERT INTO public.findings (finding_number, summary_rating_value_id, phase_id, name, description, recommendation,
                             supporting_material)
VALUES (1, 4, 2, 'Verbose Error Pages', '<p><span class="highlighted">N.B. This issue is for stack traces and other verbose error pages, if your issue is for default error pages - use that issue instead</span></p>
<p>The web application does not consistently implement custom error pages. When an unhandled exception occurs, a verbose error message is returned, revealing sensitive technical information, <span class="highlighted">such as&nbsp;software version numbers and internal paths</span> used by the application.</p>
<p>Visiting the following URL caused a verbose <span class="highlighted">404 "Not Found" error page</span>:</p>
<ul>
<li><span class="highlighted"><a href="https://www.contextis.com">https://www.contextis.com</a></span></li>
</ul>
<p>The error message revealed <span class="highlighted">Microsoft IIS 7.5 and JDK 1.8</span> is in use <span class="highlighted">and that the web application root directory is"C:\\inetpub\\wwwroot".<br /></span></p>', '<html><body><p><strong>Implement Custom Error Pages</strong></p><p>Custom server error pages should be configured. Error pages should disclose as little technical information as possible in order to restrict the amount of internal information being exposed to attackers.</p>
<p>If the technical information is required for error resolution, then the data should be logged internally.</p></body></html>',
        '');


INSERT INTO public.finding_detail_rating_values (finding_id, detail_rating_value_id)
VALUES (1, 2);
INSERT INTO public.finding_detail_rating_values (finding_id, detail_rating_value_id)
VALUES (1, 7);
INSERT INTO public.finding_detail_rating_values (finding_id, detail_rating_value_id)
VALUES (1, 12);
INSERT INTO public.finding_detail_rating_values (finding_id, detail_rating_value_id)
VALUES (2, 4);
INSERT INTO public.finding_detail_rating_values (finding_id, detail_rating_value_id)
VALUES (2, 8);
INSERT INTO public.finding_detail_rating_values (finding_id, detail_rating_value_id)
VALUES (2, 14);
INSERT INTO public.finding_detail_rating_values (finding_id, detail_rating_value_id)
VALUES (3, 4);
INSERT INTO public.finding_detail_rating_values (finding_id, detail_rating_value_id)
VALUES (3, 9);
INSERT INTO public.finding_detail_rating_values (finding_id, detail_rating_value_id)
VALUES (3, 12);


-- get all tests
SELECT tests.engagement_number    as engagement_number,
       tests.document_guid        as document_guid,
       tests.name                 as test_name,
       summary_rating_values.name as summary_rating_value,
       COUNT(*)                   as count
FROM tests
         INNER JOIN user_tests
                    on tests.id = user_tests.test_id
         INNER JOIN users ON users.id = user_tests.user_id
         FULL JOIN phases ON tests.id = phases.test_id
         FULL JOIN findings ON phases.id = findings.phase_id
         FULL JOIN summary_rating_values ON findings.summary_rating_value_id = summary_rating_values.id
WHERE email = '<EMAIL>'
GROUP BY tests.engagement_number, tests.document_guid, tests.name, summary_rating_values.name;

-- get finding details
SELECT findings.id,
       findings.finding_number,
       summary_rating_values.name,
       summary_rating_values.colour,
       tests.document_guid,
       tests.engagement_number,
       findings.name,
       findings.description,
       findings.recommendation,
       findings.supporting_material
FROM "findings"
         JOIN summary_rating_values ON summary_rating_values.id = findings.summary_rating_value_id
         JOIN phases ON phases.id = findings.phase_id
         JOIN tests ON tests.id = phases.test_id
         JOIN user_tests ON user_tests.test_id = tests.id
         JOIN users ON users.id = user_tests.user_id
WHERE users.email = '<EMAIL>'
  AND tests.document_guid = 'guid-100'
  AND findings.finding_number = 1;

--get findings of a test
SELECT findings.finding_number, summary_rating_values.name, findings.name, tests.document_guid
FROM "findings"
         JOIN summary_rating_values ON summary_rating_values.id = findings.summary_rating_value_id
         JOIN phases ON phases.id = findings.phase_id
         JOIN tests ON tests.id = phases.test_id
         JOIN user_tests ON user_tests.test_id = tests.id
         JOIN users ON users.id = user_tests.user_id
WHERE users.email = '<EMAIL>'
  AND tests.document_guid = 'guid-100';

-- -- get detail rating attributes of a finding
-- SELECT detail_rating_attributes.id,
--        detail_rating_attributes.name
-- FROM "findings"
--          JOIN phases ON phases.id = findings.phase_id
--          JOIN tests ON tests.id = phases.test_id
--          JOIN detail_rating_attributes ON detail_rating_attributes.scoring_system_id = tests.scoring_system_id
-- WHERE findings.id = 1;

-- get detail rating values
SELECT detail_rating_attributes.name,
       detail_rating_values.name
FROM findings
         JOIN finding_detail_rating_values ON findings.id = finding_detail_rating_values.finding_id
         JOIN detail_rating_values ON detail_rating_values.id = finding_detail_rating_values.detail_rating_value_id
         JOIN detail_rating_attributes ON detail_rating_attributes.id = detail_rating_values.detail_rating_attribute_id
WHERE findings.id = 1;
