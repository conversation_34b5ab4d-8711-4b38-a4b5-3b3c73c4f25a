package assets

import (
	"bytes"
	"context"
	"encoding/base64"
	"log"
	"strings"

	"github.com/PuerkitoBio/goquery"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/s3"
)

type S3Service struct {
	client *s3.Client
}

func NewS3Service(cfg aws.Config) (*S3Service, error) {
	client := s3.NewFromConfig(cfg)
	return &S3Service{
		client: client,
	}, nil
}

func (c *S3Service) ReplaceImgSrcWithBase64EncodedImage(htmlContent string, bucketName string) string {
	doc, err := goquery.NewDocumentFromReader(strings.NewReader(htmlContent))
	if err != nil {
		log.Println("Error parsing HTML:", err)
		return ""
	}

	doc.Find("img").Each(func(i int, img *goquery.Selection) {
		srcAttribute, exists := img.Attr("src")
		if exists {
			if strings.HasPrefix(srcAttribute, "/") {
				srcAttribute = srcAttribute[1:]
			}

			// Retrieve file from S3
			resp, err := c.client.GetObject(context.TODO(), &s3.GetObjectInput{
				Bucket: &bucketName,
				Key:    &srcAttribute,
			})
			if err != nil {
				log.Println("Error fetching image from S3:", err)
				return
			}
			defer resp.Body.Close()

			imageContent := new(bytes.Buffer)
			_, err = imageContent.ReadFrom(resp.Body)
			if err != nil {
				log.Println("Error reading image content:", err)
				return
			}

			base64EncodedImage := base64.StdEncoding.EncodeToString(imageContent.Bytes())
			imageURL := "data:image/jpeg;base64," + base64EncodedImage

			img.SetAttr("src", imageURL)
		}
	})

	htmlString, err := doc.Html()
	if err != nil {
		log.Println("Error converting document to HTML:", err)
		return ""
	}

	return htmlString
}
