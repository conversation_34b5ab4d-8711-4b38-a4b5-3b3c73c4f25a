package routes

import (
	"derp-api/auth"
	"derp-api/middlewares"
	"net/http"

	"github.com/MicahParks/keyfunc/v3"
	"github.com/gin-gonic/gin"
)

func addAdminUsersRoutes(rg *gin.RouterGroup, jwks keyfunc.Keyfunc, cognitoService auth.CognitoService, awsDefaultRegion string, cognitoUserPoolId string, cognitoAppClientId string) {
	adminUsers := rg.Group("/admin/users")

	adminUsers.GET("", middlewares.CognitoAuthMiddleware("access", awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId, jwks, false, true), func(c *gin.Context) {
		users, err := cognitoService.ListAllUsers()
		if err != nil {
			c.JSON(400, gin.H{
				"message": err.Error(),
			})
		} else {
			c.J<PERSON>(200, gin.H{
				"data": users,
			})
		}
	})

	type UserRequest struct {
		Username string `json:"username"`
	}

	adminUsers.POST("", middlewares.CognitoAuthMiddleware("access", awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId, jwks, false, true), func(c *gin.Context) {
		var json UserRequest

		if err := c.ShouldBindJSON(&json); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		user, err := cognitoService.GetUser(json.Username)

		if err != nil {
			c.JSON(400, gin.H{
				"message": err.Error(),
			})
		} else {
			c.JSON(200, gin.H{
				"data": user,
			})
		}
	})

	adminUsers.POST("/groups/test_admin/add_user", middlewares.CognitoAuthMiddleware("access", awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId, jwks, false, true), func(c *gin.Context) {
		var json UserRequest

		if err := c.ShouldBindJSON(&json); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		err := cognitoService.AddUserToGroup(json.Username, "test_admins")

		if err != nil {
			c.JSON(400, gin.H{
				"message": err.Error(),
			})
		} else {
			c.Status(http.StatusNoContent)
		}
	})

	adminUsers.POST("/groups/test_admin/remove_user", middlewares.CognitoAuthMiddleware("access", awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId, jwks, false, true), func(c *gin.Context) {
		var json UserRequest

		if err := c.ShouldBindJSON(&json); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		err := cognitoService.RemoveUserFromGroup(json.Username, "test_admins")

		if err != nil {
			c.JSON(400, gin.H{
				"message": err.Error(),
			})
		} else {
			c.Status(http.StatusNoContent)
		}
	})

	adminUsers.POST("/groups/user_admin/add_user", middlewares.CognitoAuthMiddleware("access", awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId, jwks, false, true), func(c *gin.Context) {
		var json UserRequest

		if err := c.ShouldBindJSON(&json); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		err := cognitoService.AddUserToGroup(json.Username, "user_admins")

		if err != nil {
			c.JSON(400, gin.H{
				"message": err.Error(),
			})
		} else {
			c.Status(http.StatusNoContent)
		}
	})

	adminUsers.POST("/groups/user_admin/remove_user", middlewares.CognitoAuthMiddleware("access", awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId, jwks, false, true), func(c *gin.Context) {
		var json UserRequest

		if err := c.ShouldBindJSON(&json); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}
		err := cognitoService.RemoveUserFromGroup(json.Username, "user_admins")

		if err != nil {
			c.JSON(400, gin.H{
				"message": err.Error(),
			})
		} else {
			c.Status(http.StatusNoContent)
		}
	})
}
