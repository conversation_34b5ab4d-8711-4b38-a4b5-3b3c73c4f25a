package routes

import (
	"derp-api/db"
	"derp-api/middlewares"

	"github.com/MicahParks/keyfunc/v3"
	"github.com/gin-gonic/gin"
)

func addTestRoutes(rg *gin.RouterGroup, queries *db.Queries, jwks keyfunc.Keyfunc, awsDefaultRegion string, cognitoUserPoolId string, cognitoAppClientId string) {
	tests := rg.Group("/tests")

	tests.GET("", middlewares.CognitoAuthMiddleware("access", awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId, jwks, false, false), func(c *gin.Context) {
		username := c.MustGet("username").(string)
		results := queries.GetFindingsCountsPerTestForUser()
		c.JSON(200, gin.H{
			"tests": results,
		})
	})

	tests.GET("/:documentGuid", middlewares.CognitoAuthMiddleware("access", awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId, jwks, false, false), func(c *gin.Context) {
		documentGuid := c.Param("documentGuid")
		username := c.MustGet("username").(string)
		testDetails, _ := db.GetTest(username, documentGuid)
		if testDetails == nil {
			c.JSON(404, gin.H{"detail": "Not found"})
		} else {
			c.JSON(200, testDetails)
		}
	})

	tests.GET("/:documentGuid/phases/:phaseNumber/findings/:findingNumber", middlewares.CognitoAuthMiddleware("access", awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId, jwks, false, false), func(c *gin.Context) {
		documentGuid := c.Param("documentGuid")
		phaseNumber := c.Param("phaseNumber")
		findingNumber := c.Param("findingNumber")
		username := c.MustGet("username").(string)
		findingDetails, _ := db.GetFindingDetails(username, documentGuid, phaseNumber, findingNumber)
		c.JSON(200, findingDetails)
	})
}
