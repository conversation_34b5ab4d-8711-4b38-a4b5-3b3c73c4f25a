package routes

import (
	"context"
	"derp-api/auth"
	"derp-api/db"
	"fmt"
	"log"
	"log/slog"
	"net/http"
	"os"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/gin-gonic/gin"
	"github.com/jackc/pgx/v5"
	"github.com/joho/godotenv"
	sloggin "github.com/samber/slog-gin"
)

var router = gin.New()

func Run() {
	// Environment variables
	err := godotenv.Load()
	if err != nil {
		log.Fatal("Error loading .env file")
	}
	dbName := os.Getenv("DB_NAME")
	dbUser := os.Getenv("DB_USER")
	dbPassword := os.Getenv("DB_PASSWORD")
	dbHost := os.Getenv("DB_HOST")
	dbPort := os.Getenv("DB_PORT")
	awsDefaultRegion := os.Getenv("AWS_DEFAULT_REGION")
	cognitoUserPoolId := os.Getenv("COGNITO_USER_POOL_ID")
	cognitoAppClientId := os.Getenv("COGNITO_APP_CLIENT_ID")
	// tenantAssetsBucket := os.Getenv("TENANT_ASSETS_BUCKET")

	// corsOriginUrl := os.Getenv("CORS_ORIGIN_URL")

	// Logging
	logger := slog.New(slog.NewJSONHandler(os.Stdout, nil))
	slog.SetDefault(logger)
	router.Use(sloggin.NewWithFilters(logger, sloggin.IgnorePath("/healthcheck")))
	router.Use(gin.Recovery())

	ctx := context.Background()

	conn, err := pgx.Connect(ctx, fmt.Sprintf("user=%s password=%s dbname=%s host=%s port=%s", dbUser, dbPassword, dbName, dbHost, dbPort))
	if err != nil {
		log.Fatal("Error initiating database connection")
	}
	defer conn.Close(ctx)

	queries := db.New(conn)

	jwks := auth.GetJWKS(awsDefaultRegion, cognitoUserPoolId)

	cfg, err := config.LoadDefaultConfig(context.TODO())

	// Cognito
	cognitoService, err := auth.NewCognitoService(cfg, cognitoUserPoolId)
	if err != nil {
		log.Fatal("Failed to start Cognito service")
	}

	// // S3
	// s3Service, err := assets.NewS3Service(cfg)
	// if err != nil {
	// 	log.Fatal("Failed to start S3 service")
	// }

	// Routes
	v1 := router.Group("/v1")
	addTestRoutes(v1, queries, jwks, awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId)
	addUserRoutes(v1, jwks, awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId)
	addAdminTestsRoutes(v1, jwks, *cognitoService, awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId)
	addAdminUsersRoutes(v1, jwks, *cognitoService, awsDefaultRegion, cognitoUserPoolId, cognitoAppClientId)

	router.GET("/healthcheck", func(c *gin.Context) {
		c.String(http.StatusOK, "ok")
	})
	router.Run(":8000")
}
