import UnarchivedTestsTable from "@/app/dashboard/UnarchivedTestsTable";
import { runWithAmplifyServerContext } from "@/app/utils/amplifyServerUtils";
import { fetchAuthSession } from "aws-amplify/auth/server";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { notFound } from "next/navigation";

export const metadata: Metadata = {
  title: "Customer Portal",
};
export const dynamic = "force-dynamic";

async function getUnarchivedTests(): Promise<Test[]> {
  const authSession = await runWithAmplifyServerContext({
    nextServerContext: { cookies },
    operation: (contextSpec) => fetchAuthSession(contextSpec),
  });
  const authToken = authSession.tokens?.accessToken?.toString();
  const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/tests`, {
    cache: "no-store",
    headers: {
      Authorization: `Bearer ${authToken}`,
      Accept: "application/json",
    },
  });

  if (!res.ok) {
    notFound();
  }
  const { tests } = await res.json();
  return tests;
}

export default async function DashboardPage() {
  try {
    const tests: Test[] = await getUnarchivedTests();
    return <UnarchivedTestsTable tests={tests} />;
  } catch (error) {
    return <p>Something went wrong...</p>;
  }
}
