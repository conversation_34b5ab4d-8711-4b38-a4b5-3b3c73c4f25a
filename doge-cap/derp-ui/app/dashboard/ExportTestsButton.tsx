"use client";

import { fetchAuthSession } from "aws-amplify/auth";
import React from "react";

import { downloadBlob } from "../utils/downloader";

export default function ExportTestsButton() {
  async function fetchData() {
    const authToken = (
      await fetchAuthSession()
    ).tokens?.accessToken?.toString();
    try {
      const response = await fetch("/api/tests?export=true", {
        headers: {
          Authorization: `Bearer ${authToken}`,
        },
      });
      downloadBlob(await response.blob(), "tests.csv");
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  }

  return (
    <button
      onClick={fetchData}
      className="w-full space-x-2 rounded-full bg-purple-600 px-4 py-2 font-accenture-main-semi-bold text-white md:w-auto"
    >
      <span>&#43;</span>
      <span>Export</span>
    </button>
  );
}
