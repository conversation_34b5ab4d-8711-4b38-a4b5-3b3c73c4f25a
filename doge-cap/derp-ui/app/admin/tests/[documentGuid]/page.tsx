"use client";

import {
  Combobox,
  ComboboxButton,
  ComboboxInput,
  ComboboxOption,
  ComboboxOptions,
  Transition,
} from "@headlessui/react";
import { CheckIcon, ChevronUpDownIcon } from "@heroicons/react/16/solid";
import { XMarkIcon } from "@heroicons/react/20/solid";
import { fetchAuthSession } from "aws-amplify/auth";
import Link from "next/link";
import { useRouter } from "next/navigation";
import React, { Fragment, useEffect, useState } from "react";
import toast, { Toaster } from "react-hot-toast";

const notify = (title: string, subtitle: string) =>
  toast.custom((t) => (
    <div
      className={`${
        t.visible ? "animate-enter" : "animate-leave"
      } pointer-events-auto flex w-full max-w-md flex-col border-l-8 border-purple-600 bg-neutral-50 p-2 shadow-lg`}
    >
      <div className="flex justify-end">
        <XMarkIcon
          className="-mr-1 ml-2 h-5 w-5"
          aria-hidden="true"
          onClick={() => toast.dismiss(t.id)}
        />
      </div>
      <div>
        <p className="font-accenture-main-semi-bold">{title}</p>
        <p>{subtitle}</p>
      </div>
    </div>
  ));

export default function TestUsersPage({
  params,
}: {
  params: { documentGuid: string };
}) {
  const [testWithAssignedUsers, setTestWithAssignedUsers] =
    useState<TestWithAssignedUsers | null>(null);
  document.title = `Customer Portal`;
  const [newUser, setNewUser] = useState("");
  const [assignedUsers, setAssignedUsers] = useState<string[]>([]);
  const [foundUsers, setFoundUsers] = useState([]);
  const router = useRouter();

  useEffect(() => {
    const fetchData = async (documentGuid: string) => {
      try {
        const authToken = (
          await fetchAuthSession()
        ).tokens?.accessToken?.toString();
        const response = await fetch(`/api/admin/tests/${documentGuid}`, {
          method: "GET",
          credentials: "include",
          headers: {
            Authorization: `Bearer ${authToken}`,
            Accept: "application/json",
          },
          cache: "no-store",
        });
        if (!response.ok) {
          router.push("/not-found");
        } else {
          const content = await response.json();
          setTestWithAssignedUsers(content.data);
          setAssignedUsers(content.data.assigned_users);
        }
      } catch (error) {
        router.push("/not-found");
      }
    };
    fetchData(params.documentGuid);
  }, [params.documentGuid, router]);

  async function assignUserToTest() {
    const authToken = (
      await fetchAuthSession()
    ).tokens?.accessToken?.toString();
    try {
      if (newUser === "") {
        notify("", "No user specified");
      } else {
        const response = await fetch(
          `/api/admin/tests/${params.documentGuid}/users/${newUser}`,
          {
            method: "POST",
            credentials: "include",
            headers: {
              Authorization: `Bearer ${authToken}`,
              Accept: "application/json",
            },
          },
        );

        if (!response.ok) {
          const responseBody = await response.json();
          if (response.status === 400 || response.status === 500) {
            notify("", responseBody.detail);
          }
          if (response.status === 422) {
            notify("", "Invalid input");
          }
        } else {
          setAssignedUsers([...assignedUsers, newUser]);
          notify("", `Assigned user ${newUser}`);
          setNewUser("");
        }
      }
    } catch (error) {
      notify("Something went wrong", "");
    }
  }

  async function searchUsers(searchUsersString: string) {
    const authToken = (
      await fetchAuthSession()
    ).tokens?.accessToken?.toString();
    try {
      const response = await fetch(`/api/admin/tests/users/search`, {
        method: "POST",
        credentials: "include",
        headers: {
          Authorization: `Bearer ${authToken}`,
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({
          search_users_string: decodeURIComponent(searchUsersString),
        }),
      });
      if (!response.ok) {
        const responseBody = await response.json();
      } else {
        const result = await response.json();
        setFoundUsers(result.data);
      }
    } catch (error) {
      console.log("Something went wrong");
    }
  }

  async function removeUserFromTest(userToRemoveFromTest: string) {
    const authToken = (
      await fetchAuthSession()
    ).tokens?.accessToken?.toString();
    try {
      const response = await fetch(
        `/api/admin/tests/${params.documentGuid}/users/${userToRemoveFromTest}`,
        {
          method: "DELETE",
          credentials: "include",
          headers: {
            Authorization: `Bearer ${authToken}`,
            Accept: "application/json",
          },
        },
      );
      if (!response.ok) {
        const responseBody = await response.json();
        notify("", responseBody.detail);
      } else {
        setAssignedUsers(
          assignedUsers.filter((user) => user !== userToRemoveFromTest),
        );
        notify("", `Removed user ${userToRemoveFromTest}`);
      }
    } catch (error) {
      notify("Something went wrong", "");
    }
  }

  return (
    <>
      <Toaster position="top-right" reverseOrder={false} />
      <div className="flex h-full flex-col space-y-6 py-4 md:space-y-10">
        <nav className="flex" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-2 md:space-x-3">
            <li className="inline-flex items-center text-neutral-600">
              <Link href={`/admin/tests`} prefetch={false}>
                Test Admin
              </Link>
            </li>
            <li>&gt;</li>
            <li>
              <div className="flex items-center font-accenture-main-semi-bold text-black">
                User Details
              </div>
            </li>
          </ol>
        </nav>
        <div className="font-accenture-main-semi-bold text-2xl md:text-3xl">
          Add User
        </div>
        <div className="flex flex-col space-y-2">
          <div>
            <label className="font-accenture-main-semi-bold">User email</label>
          </div>
          <div className="flex flex-col space-y-6 md:flex-row md:space-x-4 md:space-y-0">
            {
              // @ts-ignore
              <Combobox value={newUser} onChange={setNewUser}>
                <div className="relative md:w-96">
                  <div className="relative w-full cursor-default overflow-hidden bg-white text-left shadow-md focus:outline-none focus-visible:ring-2 focus-visible:ring-white/75 focus-visible:ring-offset-2 focus-visible:ring-offset-teal-300 sm:text-sm">
                    <ComboboxInput
                      className="w-full rounded-l border-2 border-gray-300 p-2 outline-none hover:border-purple-700 focus:border-purple-700"
                      onChange={(event) => searchUsers(event.target.value)}
                    />
                    <ComboboxButton className="absolute inset-y-0 right-0 flex items-center pr-2">
                      <ChevronUpDownIcon
                        className="h-5 w-5 text-gray-400"
                        aria-hidden="true"
                      />
                    </ComboboxButton>
                  </div>
                  <Transition
                    as={Fragment}
                    leave="transition ease-in duration-100"
                    leaveFrom="opacity-100"
                    leaveTo="opacity-0"
                    afterLeave={() => searchUsers("")}
                  >
                    <ComboboxOptions className="absolute mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black/5 focus:outline-none sm:text-sm">
                      {foundUsers.length === 0 ? (
                        <div className="relative cursor-default select-none px-4 py-2 text-gray-700">
                          Nothing found.
                        </div>
                      ) : (
                        foundUsers.map((foundUser) => (
                          <ComboboxOption
                            key={foundUser}
                            className={({ active }) =>
                              `relative cursor-default select-none py-2 pl-10 pr-4 ${
                                active
                                  ? "bg-accenture-core-purple-1 text-white"
                                  : "text-gray-900"
                              }`
                            }
                            value={foundUser}
                          >
                            {({ selected, active }) => (
                              <>
                                <span
                                  className={`block truncate ${selected ? "font-medium" : "font-normal"}`}
                                >
                                  {foundUser}
                                </span>
                                {selected ? (
                                  <span
                                    className={`absolute inset-y-0 left-0 flex items-center pl-3 ${active ? "text-white" : "text-teal-600"}`}
                                  >
                                    <CheckIcon
                                      className="h-5 w-5"
                                      aria-hidden="true"
                                    />
                                  </span>
                                ) : null}
                              </>
                            )}
                          </ComboboxOption>
                        ))
                      )}
                    </ComboboxOptions>
                  </Transition>
                </div>
              </Combobox>
            }
            <button
              className="text-l rounded-3xl bg-accenture-core-purple-1 px-4 py-2 font-accenture-main-semi-bold text-white"
              onClick={assignUserToTest}
            >
              Add user
            </button>
          </div>
        </div>
        <div className="grid grid-cols-1 divide-y">
          <div className="flex flex-row justify-between space-x-6 py-8">
            <div className="font-accenture-main-semi-bold">Test ID</div>
            <div>{testWithAssignedUsers?.engagement_number}</div>
          </div>
          <div className="flex flex-row justify-between space-x-6 py-8">
            <div className="font-accenture-main-semi-bold">Test Name</div>
            <div>{testWithAssignedUsers?.name}</div>
          </div>
          <div className="flex flex-col justify-between space-y-2 py-8 md:flex-row md:space-x-6 md:space-y-0">
            <div className="font-accenture-main-semi-bold">Assigned Users</div>
            <div className="flex flex-col">
              {assignedUsers.map((assignedUser) => (
                <div
                  key={assignedUser}
                  className="m-1 flex items-center justify-center rounded-full border border-zinc-400 px-3 py-1"
                >
                  <div className="md:font-base max-w-full flex-initial text-base text-sm font-normal leading-none text-neutral-600">
                    {assignedUser}
                  </div>
                  <div
                    onClick={() => removeUserFromTest(assignedUser)}
                    className="flex flex-auto flex-row-reverse"
                  >
                    <div>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="100%"
                        height="100%"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="feather feather-x ml-2 h-4 w-4 cursor-pointer rounded-full hover:text-indigo-400"
                      >
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                      </svg>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>
          <div className="flex justify-end py-8">
            <Link href={"/admin/tests"} prefetch={false}>
              <button className="rounded-3xl border-2 border-purple-600 bg-white px-4 py-2 font-accenture-main-semi-bold text-purple-600">
                Back
              </button>
            </Link>
          </div>
        </div>
      </div>
    </>
  );
}
