"use client";

import DeleteTestErrorMessage from "@/app/admin/tests/DeleteTestErrorMessage";
import { Dialog, Transition } from "@headlessui/react";
import {
  ExclamationTriangleIcon,
  TrashIcon,
  XMarkIcon,
} from "@heroicons/react/20/solid";
import { fetchAuthSession } from "aws-amplify/auth";
import React, { Fragment, useState } from "react";

type Props = {
  deleteTestRow(documentGuid: string): void;
  document_guid: string;
  engagement_number: number;
  name: string;
};

export default function DeleteTestModal({
  deleteTestRow,
  document_guid,
  engagement_number,
  name,
}: Props) {
  function closeModal() {
    setIsOpen(false);
    setShowErrorMessage(false);
  }

  function openModal() {
    setIsOpen(true);
    setShowErrorMessage(false);
  }

  const [isOpen, setIsOpen] = useState(false);
  const [showErrorMessage, setShowErrorMessage] = useState(false);

  async function deleteTest(document_guid: string) {
    const authToken = (
      await fetchAuthSession()
    ).tokens?.accessToken?.toString();
    const res = await fetch(`/api/admin/tests/${document_guid}`, {
      method: "DELETE",
      credentials: "include",
      headers: {
        Authorization: `Bearer ${authToken}`,
      },
    });

    if (!res.ok) {
      setShowErrorMessage(true);
    } else {
      setShowErrorMessage(false);
      deleteTestRow(document_guid);
      closeModal();
    }
  }

  return (
    <>
      <div className="flex justify-center">
        <TrashIcon
          onClick={openModal}
          className="h-8 w-8 cursor-pointer text-red-600"
        />
      </div>
      <>
        <Transition appear show={isOpen} as={Fragment}>
          <Dialog as="div" className="relative z-10" onClose={closeModal}>
            <Transition.Child
              as={Fragment}
              enter="ease-out duration-300"
              enterFrom="opacity-0"
              enterTo="opacity-100"
              leave="ease-in duration-200"
              leaveFrom="opacity-100"
              leaveTo="opacity-0"
            >
              <div className="fixed inset-0 bg-black/25" />
            </Transition.Child>

            <div className="fixed inset-0 overflow-y-auto">
              <div className="flex min-h-full items-center justify-center p-4 text-center">
                <Transition.Child
                  as={Fragment}
                  enter="ease-out duration-300"
                  enterFrom="opacity-0 scale-95"
                  enterTo="opacity-100 scale-100"
                  leave="ease-in duration-200"
                  leaveFrom="opacity-100 scale-100"
                  leaveTo="opacity-0 scale-95"
                >
                  <Dialog.Panel className="w-full max-w-md transform space-y-4 overflow-hidden rounded-2xl bg-white p-6 text-left align-middle shadow-xl transition-all">
                    <Dialog.Title
                      as="h3"
                      className="text-lg font-medium leading-6 text-gray-900"
                    >
                      <div className="flex flex-row justify-between space-x-6">
                        <div>
                          Delete Test &quot;{engagement_number} - {name}&quot;
                        </div>
                        <XMarkIcon
                          onClick={closeModal}
                          className="h-6 w-6 cursor-pointer"
                        />
                      </div>
                    </Dialog.Title>
                    <div className="flex flex-col space-y-6">
                      <div
                        className="relative flex flex-row space-x-2 rounded border border-orange-400 bg-orange-100 px-1 py-1 font-accenture-main text-orange-700"
                        role="alert"
                      >
                        <span>
                          <ExclamationTriangleIcon className="h-6 w-6 text-orange-400" />
                        </span>
                        <span className="block text-sm sm:inline md:text-base">
                          Warning! The Test with all its associated Phases and
                          Findings will be permanently deleted. This action
                          cannot be undone.
                        </span>
                      </div>
                      <div className="flex flex-row justify-end space-x-4">
                        <button
                          className="px-8 py-1 font-accenture-main-semi-bold text-lg text-black"
                          onClick={closeModal}
                        >
                          Cancel
                        </button>
                        <button
                          className="rounded-3xl bg-red-700 px-8 py-1 font-accenture-main-semi-bold text-lg text-white"
                          onClick={() => deleteTest(document_guid)}
                        >
                          Delete
                        </button>
                      </div>
                      {showErrorMessage && (
                        <DeleteTestErrorMessage
                          message={"Failed to delete Test"}
                        />
                      )}
                    </div>
                  </Dialog.Panel>
                </Transition.Child>
              </div>
            </div>
          </Dialog>
        </Transition>
      </>
    </>
  );
}
