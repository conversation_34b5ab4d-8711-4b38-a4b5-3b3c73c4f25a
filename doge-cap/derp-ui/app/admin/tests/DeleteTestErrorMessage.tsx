import { XMarkIcon } from "@heroicons/react/20/solid";
import React from "react";

type Props = {
  message: string;
};

export default function DeleteTestErrorMessage({ message }: Props) {
  return (
    <div
      className="relative flex flex-row space-x-2 rounded border border-red-400 bg-red-100 px-1 py-1 font-accenture-main text-red-700"
      role="alert"
    >
      <span>
        <XMarkIcon className="h-6 w-6 text-red-400" />
      </span>
      <span className="block sm:inline">{message}</span>
    </div>
  );
}
