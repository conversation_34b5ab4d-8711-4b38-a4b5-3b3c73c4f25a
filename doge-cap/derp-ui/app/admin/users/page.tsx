import UsersTable from "@/app/admin/users/UsersTable";
import { runWithAmplifyServerContext } from "@/app/utils/amplifyServerUtils";
import { fetchAuthSession } from "aws-amplify/auth/server";
import { Metadata } from "next";
import { cookies } from "next/headers";
import { notFound } from "next/navigation";

export const metadata: Metadata = {
  title: "Customer Portal",
};
export const dynamic = "force-dynamic";

async function getUsers() {
  const authSession = await runWithAmplifyServerContext({
    nextServerContext: { cookies },
    operation: (contextSpec) => fetchAuthSession(contextSpec),
  });
  const authToken = authSession.tokens?.accessToken?.toString();
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/admin/users`,
    {
      cache: "no-store",
      headers: {
        Authorization: `Bearer ${authToken}`,
        Accept: "application/json",
      },
    },
  );

  if (!res.ok) {
    notFound();
  }
  return res.json();
}

export default async function UsersPage() {
  try {
    const { data } = await getUsers();
    const users: User[] = data;

    return (
      <div className="flex flex-col space-y-6 py-4 md:space-y-10">
        <div className="font-accenture-main-bold text-2xl md:text-3xl">
          User Admin
        </div>
        <UsersTable retrievedUsers={users} />
      </div>
    );
  } catch (error) {
    return <p>Something went wrong...</p>;
  }
}
