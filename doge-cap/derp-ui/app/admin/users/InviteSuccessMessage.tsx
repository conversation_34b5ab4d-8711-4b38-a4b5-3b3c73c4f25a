import { CheckCircleIcon } from "@heroicons/react/20/solid";
import React from "react";

type Props = {
  message: string;
};

export default function InviteSuccessMessage({ message }: Props) {
  return (
    <div
      className="relative flex flex-row space-x-2 rounded border border-green-400 bg-green-100 px-1 py-1 font-accenture-main text-green-700"
      role="alert"
    >
      <span>
        <CheckCircleIcon className="h-6 w-6 text-green-400" />
      </span>
      <span className="block sm:inline">{message}</span>
    </div>
  );
}
