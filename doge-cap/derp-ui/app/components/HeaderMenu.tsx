"use client";

import { Menu, Transition } from "@headlessui/react";
import { ChevronDownIcon } from "@heroicons/react/20/solid";
import Link from "next/link";
import { usePathname } from "next/navigation";

type Props = {
  signOut: any;
  data: any;
};

export default function HeaderMenu({ signOut, data }: Props) {
  const pathname = usePathname();

  return (
    <div className="flex flex-row items-center space-x-6 px-6">
      <div
        className={`hidden md:flex ${
          pathname === "/dashboard"
            ? "border-b-2 border-purple-600 py-3"
            : "py-3"
        }`}
      >
        <Link href="/dashboard" prefetch={false}>
          Dashboard
        </Link>
      </div>
      {data?.groups?.includes("user_admins") && (
        <div
          className={`hidden md:flex ${
            pathname === "/admin/users"
              ? "border-b-2 border-purple-600 py-3"
              : "py-3"
          }`}
        >
          <Link href="/admin/users" prefetch={false}>
            User Admin
          </Link>
        </div>
      )}
      {data?.groups?.includes("test_admins") && (
        <div
          className={`hidden md:flex ${
            pathname === "/admin/tests"
              ? "border-b-2 border-purple-600 py-3"
              : "py-3"
          }`}
        >
          <Link href="/admin/tests" prefetch={false}>
            Test Admin
          </Link>
        </div>
      )}
      <div className="flex flex-col">
        <Menu>
          <Menu.Button>
            <div className="flex flex-row items-center font-accenture-main-semi-bold">
              <div>
                <span>{data?.given_name}</span>
                <span>&nbsp;</span>
                <span>{data?.family_name}</span>
              </div>
              <ChevronDownIcon
                className="-mr-1 ml-2 h-5 w-5 text-gray-500"
                aria-hidden="true"
              />
            </div>
          </Menu.Button>
          <Transition
            enter="transition duration-100 ease-out"
            enterFrom="transform scale-95 opacity-0"
            enterTo="transform scale-100 opacity-100"
            leave="transition duration-75 ease-out"
            leaveFrom="transform scale-100 opacity-100"
            leaveTo="transform scale-95 opacity-0"
          >
            <Menu.Items className="absolute right-0 mt-2 w-56 origin-top-right bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none">
              <div className="px-4 py-4">
                <Menu.Item>
                  {({ active }) => (
                    <Link
                      className={
                        active ? "text-accenture-core-purple-1" : "text-black"
                      }
                      href="/profile"
                      prefetch={false}
                    >
                      My Profile
                    </Link>
                  )}
                </Menu.Item>
              </div>
              <div className="px-4 py-4">
                <Menu.Item>
                  {({ active }) => (
                    <button
                      className={
                        active ? "text-accenture-core-purple-1" : "text-black"
                      }
                      onClick={signOut}
                    >
                      Logout
                    </button>
                  )}
                </Menu.Item>
              </div>
            </Menu.Items>
          </Transition>
        </Menu>
      </div>
    </div>
  );
}
