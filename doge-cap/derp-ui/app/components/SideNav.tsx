import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";

type Props = {
  isSideNavOpen: boolean;
  toggleSideNav: () => void;
  data: any;
};

export default function SideNav({ isSideNavOpen, toggleSideNav, data }: Props) {
  const pathname = usePathname();

  return (
    <div
      className={`fixed inset-0 z-30 transform bg-black bg-opacity-10 shadow-md transition-transform duration-300 ${
        isSideNavOpen ? "translate-x-0" : "-translate-x-full"
      }`}
      onClick={toggleSideNav}
    >
      <div
        className="flex h-full w-64 flex-col bg-white"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex flex-row items-center divide-x">
          <div className="p-4">
            <Image
              alt="Accenture logo"
              src="/images/Acc_Logo_Black_Purple_RGB.svg"
              width="80"
              height="80"
              className="pb-3"
            />
          </div>
          <div className="px-4 text-xs">Accenture Security</div>
        </div>
        <div
          className={`p-4 ${pathname === "/dashboard" ? "bg-purple-100 font-medium" : ""}`}
        >
          <Link href="/dashboard" prefetch={false}>
            Dashboard
          </Link>
        </div>
        {data?.groups?.includes("user_admins") && (
          <div
            className={`p-4 ${
              pathname === "/admin/users" ? "bg-purple-100 font-medium" : ""
            }`}
          >
            <Link href="/admin/users" prefetch={false}>
              User Admin
            </Link>
          </div>
        )}
        {data?.groups?.includes("test_admins") && (
          <div
            className={`p-4 ${
              pathname === "/admin/tests" ? "bg-purple-100 font-medium" : ""
            }`}
          >
            <Link href="/admin/tests" prefetch={false}>
              Test Admin
            </Link>
          </div>
        )}
      </div>
    </div>
  );
}
