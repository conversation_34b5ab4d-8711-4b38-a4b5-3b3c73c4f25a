import HeaderMenu from "@/app/components/HeaderMenu";
import { Bars3Icon } from "@heroicons/react/24/outline";
import Image from "next/image";

type Props = {
  signOut: any;
  toggleSideNav: () => void;
  data: any;
};
export default function Header({ signOut, toggleSideNav, data }: Props) {
  return (
    <div className="sticky top-0 z-20 flex flex-row items-center justify-between border-b-2 border-gray-200 bg-white font-accenture-main">
      <Bars3Icon
        onClick={toggleSideNav}
        className="m-3 h-6 w-6 cursor-pointer text-black md:hidden dark:text-white"
      />
      <div className="hidden flex-row items-center md:flex md:divide-x">
        <div className="px-4">
          <Image
            alt="Accenture logo"
            src="/images/Acc_Logo_Black_Purple_RGB.svg"
            width="100"
            height="100"
            className="pb-3"
          />
        </div>
        <div className="px-4">Accenture Security</div>
      </div>
      <HeaderMenu signOut={signOut} data={data} />
    </div>
  );
}
