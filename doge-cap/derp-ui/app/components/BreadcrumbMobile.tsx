"use client";

import { useEffect, useRef, useState } from "react";

interface BreadcrumbProps {
  items: string[];
}

export default function BreadcrumbMobile({ items }: BreadcrumbProps) {
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const dropdownRef = useRef<HTMLUListElement | null>(null);
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsDropdownOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <>
      <button
        onClick={() => setIsDropdownOpen((prev) => !prev)}
        className="text-neutral-600 md:hidden"
      >
        ...
      </button>
      {isDropdownOpen && (
        <ul
          ref={dropdownRef}
          className="absolute rounded-md border bg-white py-1 shadow-lg"
        >
          {items.map((item, index) => (
            <li key={index} className="px-4 py-2 hover:bg-gray-100">
              {item}
            </li>
          ))}
        </ul>
      )}
    </>
  );
}
