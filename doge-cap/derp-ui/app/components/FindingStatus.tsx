type Props = {
  status: string;
};

export default function FindingStatus({ status }: Props) {
  if (status === "OPEN") {
    return (
      <span
        className={
          "bg-red-100 p-1 font-accenture-main-semi-bold uppercase text-red-400"
        }
      >
        OPEN
      </span>
    );
  } else if (status === "CLOSED") {
    return (
      <span
        className={
          "bg-green-100 p-1 font-accenture-main-semi-bold uppercase text-green-400"
        }
      >
        CLOSED
      </span>
    );
  } else if (status === "FALSE_POSITIVE") {
    return (
      <span
        className={
          "bg-orange-100 p-1 font-accenture-main-semi-bold uppercase text-orange-400"
        }
      >
        FALSE POSITIVE
      </span>
    );
  } else {
    return <></>;
  }
}
