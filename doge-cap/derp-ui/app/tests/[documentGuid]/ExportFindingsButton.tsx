"use client";

import { downloadBlob } from "@/app/utils/downloader";
import { fetchAuthSession } from "aws-amplify/auth";
import React from "react";

type Props = {
  testDocumentGuid: string | null;
  testEngagementNumber: string | null;
};

export default function ExportFindingsButton({
  testDocumentGuid,
  testEngagementNumber,
}: Props) {
  async function fetchData() {
    const authToken = (
      await fetchAuthSession()
    ).tokens?.accessToken?.toString();
    try {
      const response = await fetch(
        `/api/tests/${testDocumentGuid}?export=true`,
        {
          headers: {
            Authorization: `Bearer ${authToken}`,
          },
        },
      );
      downloadBlob(await response.blob(), `test-${testEngagementNumber}.csv`);
    } catch (error) {
      console.error("Error fetching data:", error);
    }
  }

  return (
    <button
      onClick={fetchData}
      className="w-full space-x-2 rounded-full bg-purple-600 px-4 py-2 font-accenture-main-semi-bold text-white md:w-auto md:justify-end"
    >
      <span>&#43;</span>
      <span>Export</span>
    </button>
  );
}
