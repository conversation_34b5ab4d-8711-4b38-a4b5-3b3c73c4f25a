import BreadcrumbMobile from "@/app/components/BreadcrumbMobile";
import FindingStatusSelector from "@/app/components/FindingStatusSelector";
import MetricGroups from "@/app/tests/[documentGuid]/phases/[phase]/findings/[finding]/MetricGroups";
import { Finding } from "@/app/tests/[documentGuid]/schemas";
import { runWithAmplifyServerContext } from "@/app/utils/amplifyServerUtils";
import { fetchAuthSession } from "aws-amplify/auth/server";
import { Metadata } from "next";
import { cookies } from "next/headers";
import Link from "next/link";
import { notFound } from "next/navigation";

export const metadata: Metadata = {
  title: "Customer Portal",
};
export const dynamic = "force-dynamic";

async function getFinding(
  documentGuid: string,
  phaseNumber: string,
  findingNumber: string,
): Promise<Finding> {
  const authSession = await runWithAmplifyServerContext({
    nextServerContext: { cookies },
    operation: (contextSpec) => fetchAuthSession(contextSpec),
  });

  const authToken = authSession.tokens?.accessToken?.toString();
  const res = await fetch(
    `${process.env.NEXT_PUBLIC_API_BASE_URL}/tests/${documentGuid}/phases/${phaseNumber}/findings/${findingNumber}`,
    {
      cache: "no-store",
      headers: {
        Authorization: `Bearer ${authToken}`,
        Accept: "application/json",
      },
    },
  );

  if (!res.ok) {
    notFound();
  }
  return res.json();
}

export default async function FindingPage({
  params,
}: {
  params: { documentGuid: string; phase: string; finding: string };
}) {
  try {
    const finding: Finding = await getFinding(
      params.documentGuid,
      params.phase,
      params.finding,
    );

    return (
      <div className="flex h-full w-full flex-col space-y-4 px-6 pt-4">
        <nav className="flex py-3" aria-label="Breadcrumb">
          <ol className="inline-flex items-center space-x-2 md:space-x-3">
            <li className="inline-flex items-center text-neutral-600">
              <Link href={`/dashboard`} prefetch={false}>
                Dashboard
              </Link>
            </li>
            <li>&gt;</li>
            <li>
              <div className="hidden items-center text-neutral-600 md:flex">
                <Link href={`/tests/${params.documentGuid}`} prefetch={false}>
                  Test Detail
                </Link>
              </div>
              <BreadcrumbMobile items={["Test Detail"]} />
            </li>
            <li>&gt;</li>
            <li>
              <div className="flex items-center font-accenture-main-semi-bold text-black">
                <Link
                  href={`/tests/${params.documentGuid}/phases/${params.phase}/findings/${finding?.number}`}
                  prefetch={false}
                >
                  {finding?.name}
                </Link>
              </div>
            </li>
          </ol>
        </nav>
        <div className="flex flex-col items-center space-y-1 font-accenture-main-bold md:flex-row md:space-y-0">
          <div className="text-xl md:text-3xl">
            <span className="font-accenture-main-semi-bold text-purple-600">
              {finding?.engagement_number}-{finding?.phase_number}-
              {String(finding.number).padStart(2, "0")}
            </span>
            <span>&#58;</span>
            <span>&nbsp;</span>
            <span>{finding?.name}</span>
          </div>
          <div className="place-self-end pl-4 pr-2 md:self-center">
            <span
              className={`base-severity-${finding.scoring.base_severity} p-1 px-2 font-accenture-main-semi-bold text-sm uppercase text-black`}
            >
              {finding?.scoring.base_severity}
            </span>
          </div>
        </div>
        <div className="flex w-full flex-col space-y-6 pb-8">
          <MetricGroups metricGroups={finding.scoring.metric_groups} />
          <div className="flex flex-col space-y-1 md:flex-row md:items-center md:space-x-2 md:space-y-0">
            <div className="font-accenture-main-semi-bold">Vector String:</div>
            <div className="break-words">{finding.scoring.vector_string}</div>
          </div>

          <div className="flex flex-row items-center space-x-2">
            <div className="font-accenture-main-semi-bold">Status:</div>
            <div>
              <FindingStatusSelector
                documentGuid={params.documentGuid}
                phase={params.phase}
                finding={finding?.number}
                status={finding?.status}
              />
            </div>
          </div>

          {finding?.description ? (
            <div id="description">
              <div className="pb-4 font-accenture-main-semi-bold text-xl capitalize">
                Description
              </div>
              <div
                className="prose"
                dangerouslySetInnerHTML={{ __html: finding?.description }}
              ></div>
            </div>
          ) : (
            <></>
          )}
          {finding?.recommendation ? (
            <div id="recommendation">
              <div className="pb-4 font-accenture-main-semi-bold text-xl capitalize">
                Recommendation
              </div>
              <div
                className="prose break-words"
                dangerouslySetInnerHTML={{
                  __html: finding?.recommendation,
                }}
              ></div>
            </div>
          ) : (
            <></>
          )}
          {finding?.associated_hosts.length > 0 ? (
            <div id="associated-hosts">
              <div className="pb-4 font-accenture-main-semi-bold text-xl capitalize">
                Associated Hosts
              </div>
              <table className="grow table-auto overflow-x-auto">
                <thead className="border-b-2 border-neutral-200 font-accenture-main-semi-bold">
                  <tr>
                    <th className="min-w-28">IP Address</th>
                    <th className="min-w-28">Network</th>
                    <th className="min-w-28">Port</th>
                    <th className="min-w-28">Protocol</th>
                  </tr>
                </thead>
                <tbody>
                  {finding?.associated_hosts.map((associatedHost, index) => (
                    <tr
                      key={index}
                      className="text-center odd:bg-white even:bg-neutral-50"
                    >
                      <td className="mx-2 px-6 py-2">
                        {associatedHost.ip_address}
                      </td>
                      <td className="min-w-28">{associatedHost.network}</td>
                      <td className="min-w-28">{associatedHost.port}</td>
                      <td className="min-w-28">{associatedHost.protocol}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          ) : (
            <></>
          )}
          {finding?.supporting_material ? (
            <div id="supporting-material">
              <div className="pb-4 font-accenture-main-semi-bold text-xl capitalize">
                Supporting Material
              </div>
              <div
                className="prose"
                dangerouslySetInnerHTML={{
                  __html: finding?.supporting_material,
                }}
              ></div>
            </div>
          ) : (
            <></>
          )}
        </div>
      </div>
    );
  } catch (error) {
    return <p>Something went wrong...</p>;
  }
}
