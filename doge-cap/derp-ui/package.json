{"name": "derp-ui", "version": "0.1.0", "private": true, "engines": {"node": "^22", "pnpm": "^10"}, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@aws-amplify/adapter-nextjs": "^1.3.0", "@aws-amplify/ui-react": "^6.7.2", "@headlessui/react": "^2.2.0", "@heroicons/react": "^2.2.0", "aws-amplify": "^6.10.3", "next": "14.2.18", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "swr": "^2.2.5"}, "devDependencies": {"@tailwindcss/typography": "^0.5.15", "@trivago/prettier-plugin-sort-imports": "^5.2.0", "@types/node": "^22.10.2", "@types/react": "^18.3.17", "@types/react-dom": "^18.3.5", "autoprefixer": "^10.4.20", "eslint": "^8.57.1", "eslint-config-next": "14.2.18", "postcss": "^8.4.49", "prettier": "^3.4.2", "prettier-plugin-tailwindcss": "^0.6.9", "tailwindcss": "^3.4.17", "typescript": "^5.7.2"}, "prettier": {"plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "importOrder": ["^@core/(.*)$", "^@server/(.*)$", "^@ui/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true}}