# Doge Enterprise Portal monorepo

<!-- TOC -->
- [Doge Enterprise Portal monorepo](#doge-enterprise-portal-monorepo)
  - [Security context](#security-context)
  - [Prerequisites](#prerequisites)
    - [1. Install `awscli`](#1-install-awscli)
    - [2. Add MFA and tag your IAM account](#2-add-mfa-and-tag-your-iam-account)
    - [3. Add keys to your `awscli`](#3-add-keys-to-your-awscli)
    - [4. Use temporary session tokens using your MFA app](#4-use-temporary-session-tokens-using-your-mfa-app)
      - [Zsh script to set temporary credentials using MFA](#zsh-script-to-set-temporary-credentials-using-mfa)
      - [Bash script to set temporary credentials using MFA](#bash-script-to-set-temporary-credentials-using-mfa)
<!-- TOC -->

## Security context

```
Beginning July 27, Accenture Information Security will block usage of non-standard browsers on your workstation. 

Use of these browsers does not align with the Internet Browser Security Standard. See approved browsers. 

If you have a business need to continue using a specific browser, raise an exception on the eGRC portal with these details.
```

This means that we cannot use [`aws-azure-login`](https://github.com/aws-azure-login/aws-azure-login) because it relies
on installing its own Chromium instance. Instead we should follow standard practices and use IAM users with limited
privileges for local development with the `awscli`, as we should have from the very beginning.

## Prerequisites

- [AWS Command Line Interface](https://aws.amazon.com/cli/)

### 1. Install `awscli`

**MacOS (using `brew`)**

```bash
brew install awscli
```

**Ubuntu/Debian/WSL**

```bash
curl "https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip" -o "awscliv2.zip"
unzip awscliv2.zip
sudo ./aws/install
```

### 2. Add MFA and tag your IAM account

Each IAM user should add a MFA device for its login and
have [this policy](https://us-east-1.console.aws.amazon.com/iamv2/home?region=eu-west-2#/policies/details/arn%3Aaws%3Aiam%3A%3A************%3Apolicy%2FMFARequired?section=policy_permissions)
attached where it forbids any access to AWS services unless signed in with MFA.

Each user will also need to have a tag with a key-pair value `Type - HumanAdmin` (take care of the capitalisation. It
should be as shown here).

[Link to an example user with all the above points](https://us-east-1.console.aws.amazon.com/iamv2/home?region=eu-west-2#/users/details/a.panagiotopoulos.cli?section=permissions).

After creating an access key for your account to use with `awscli`, you will be receiving an alert
that `AWS Humanadmin Access keys older than 48 hrs`, [an issue will be created in the issues dashboard](https://isd.accenture.com/issues?issueProgress=OPEN&isBeta=false&isSecurityException=false&isFalsePositive=false&isAllowedList=false&isAstr=null),
and 30-day timer will start for this key to be removed. Current Accenture AWS policies forbid you to have long-lived
access keys, even for development purposes using accounts with limited privileges, which I find absurd.

More details of all the above can be found
in [this policy document](https://ts.accenture.com/sites/ACP_Security_Compliance/Documents_CSPM/Amazon/Wave%204/524%20AWS%20IAM%20Users%20with%20HumanAdmin%20tag%20Access%20Keys_cspm.pdf).

Also posting the content of the MFA policy that needs to be attached to your IAM account here:

```json
{
    "Version": "2012-10-17",
    "Statement": [
        {
            "Sid": "BlockMostAccessUnlessSignedInWithMFA",
            "Effect": "Deny",
            "NotAction": [
                "iam:CreateVirtualMFADevice",
                "iam:EnableMFADevice",
                "iam:ListMFADevices",
                "iam:ListUsers",
                "iam:ListVirtualMFADevices",
                "iam:ResyncMFADevice",
                "sts:GetSessionToken"
            ],
            "Resource": "*",
            "Condition": {
                "BoolIfExists": {
                    "aws:MultiFactorAuthPresent": "false"
                }
            }
        }
    ]
}
```

### 3. Add keys to your `awscli`

```bash
aws configure
```


### 4. Use temporary session tokens using your MFA app

A personal script that I have been using to create sessions in my local terminal for my AWS account instead of manually calling (`aws sts get-session-token`) and setting the environment variables. Feel free to modify it for your needs. There are two variations below, one for `bash` and one for `zsh` with some small differences in syntax between them:
* Use the appropriate MFA serial number.
* The `vared` command is `zsh` specific, use `read` if you use `bash`.
* [An example IAM user with all the above points set up](https://us-east-1.console.aws.amazon.com/iamv2/home?region=eu-west-2#/users/details/a.panagiotopoulos.cli?section=security_credentials).

#### Zsh script to set temporary credentials using MFA

```zsh
#!/bin/zsh

# Set your MFA serial number
MFA_SERIAL_NUMBER="arn:aws:iam::************:mfa/doge-aws-cli"
# AWS profile - replace with yours
AWS_PROFILE="derp"
# AWS region - replace with yours
AWS_DEFAULT_REGION="eu-west-2"

# Prompt the user for the AWS token code
vared -p "Enter your AWS token code: " -c token_code

session_token=$(aws sts get-session-token --serial-number "$MFA_SERIAL_NUMBER" --profile "$AWS_PROFILE" --token-code "$token_code" --output json)

# Extract Access Key, Secret Key, and Session Token
AWS_ACCESS_KEY_ID=$(echo "$session_token" | jq -r .Credentials.AccessKeyId)
AWS_SECRET_ACCESS_KEY=$(echo "$session_token" | jq -r .Credentials.SecretAccessKey)
AWS_SESSION_TOKEN=$(echo "$session_token" | jq -r .Credentials.SessionToken)

# Check if credentials were retrieved successfully
if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ] || [ -z "$AWS_SESSION_TOKEN" ]; then
    echo "Failed to retrieve temporary AWS credentials."
    exit 1
fi

# Use sed to update or add the AWS credentials in ~/.zshrc
# The format accommodates both macOS and GNU sed versions
sed -i '' '/export AWS_ACCESS_KEY_ID=/d' ~/.zshrc
sed -i '' '/export AWS_SECRET_ACCESS_KEY=/d' ~/.zshrc
sed -i '' '/export AWS_SESSION_TOKEN=/d' ~/.zshrc
sed -i '' '/export AWS_DEFAULT_REGION=/d' ~/.zshrc

# Append new values to ~/.zshrc
echo "export AWS_ACCESS_KEY_ID=\"$AWS_ACCESS_KEY_ID\"" >> ~/.zshrc
echo "export AWS_SECRET_ACCESS_KEY=\"$AWS_SECRET_ACCESS_KEY\"" >> ~/.zshrc
echo "export AWS_SESSION_TOKEN=\"$AWS_SESSION_TOKEN\"" >> ~/.zshrc
echo "export AWS_DEFAULT_REGION=\"$AWS_DEFAULT_REGION\"" >> ~/.zshrc

echo "Temporary AWS credentials have been added to ~/.zshrc."
echo "Run 'source ~/.zshrc' to apply them in the current session."
```

#### Bash script to set temporary credentials using MFA

```bash
#!/bin/bash

# Set your MFA serial number
MFA_SERIAL_NUMBER="arn:aws:iam::************:mfa/doge-aws-cli"
# AWS profile - replace with yours
AWS_PROFILE="derp"
# AWS region - replace with yours
AWS_DEFAULT_REGION="eu-west-2"

# Prompt the user for the AWS token code
read -p "Enter your AWS token code: " token_code

session_token=$(aws sts get-session-token --serial-number "$MFA_SERIAL_NUMBER" --profile "$AWS_PROFILE" --token-code "$token_code" --output json)

# Extract Access Key, Secret Key, and Session Token
AWS_ACCESS_KEY_ID=$(echo "$session_token" | jq -r .Credentials.AccessKeyId)
AWS_SECRET_ACCESS_KEY=$(echo "$session_token" | jq -r .Credentials.SecretAccessKey)
AWS_SESSION_TOKEN=$(echo "$session_token" | jq -r .Credentials.SessionToken)

# Check if credentials were retrieved successfully
if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ] || [ -z "$AWS_SESSION_TOKEN" ]; then
    echo "Failed to retrieve temporary AWS credentials."
    exit 1
fi

# Use sed to update or add the AWS credentials in ~/.bashrc
# The format accommodates both macOS and GNU sed versions
sed -i '' '/export AWS_ACCESS_KEY_ID=/d' ~/.bashrc
sed -i '' '/export AWS_SECRET_ACCESS_KEY=/d' ~/.bashrc
sed -i '' '/export AWS_SESSION_TOKEN=/d' ~/.bashrc
sed -i '' '/export AWS_DEFAULT_REGION=/d' ~/.bashrc

# Append new values to ~/.bashrc
echo "export AWS_ACCESS_KEY_ID=\"$AWS_ACCESS_KEY_ID\"" >> ~/.bashrc
echo "export AWS_SECRET_ACCESS_KEY=\"$AWS_SECRET_ACCESS_KEY\"" >> ~/.bashrc
echo "export AWS_SESSION_TOKEN=\"$AWS_SESSION_TOKEN\"" >> ~/.bashrc
echo "export AWS_DEFAULT_REGION=\"$AWS_DEFAULT_REGION\"" >> ~/.bashrc

echo "Temporary AWS credentials have been added to ~/.bashrc."
echo "Run 'source ~/.bashrc' to apply them in the current session."
```
