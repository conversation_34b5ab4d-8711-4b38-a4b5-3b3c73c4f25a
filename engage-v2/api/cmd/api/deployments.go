package main

import (
	"context"
	"net/http"

	"github.com/danielgtaylor/huma/v2"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments"
)

// addDeploymentsRoutes registers all /deployment routes to the application router
func addDeploymentsRoutes(api huma.API, a *application) {
	type GetDeploymentsOutput struct {
		Body struct {
			Deployments []deployments.DeploymentModel `json:"deployments"`
		}
	}

	// Get all Deployments
	huma.Register(api, huma.Operation{
		OperationID:   "get-deployments",
		Method:        http.MethodGet,
		Path:          "/deployments",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Deployments",
		Tags:          []string{"Deployments"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetDeploymentsOutput, error) {
		resp := &GetDeploymentsOutput{}

		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		roles, ok := ctx.Value("roles").([]string)
		if !ok {
			return nil, huma.Error500InternalServerError("Role information is missing or invalid")
		}
		deploymentsResults, err := deployments.GetDeployments(a.queries, roles, userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp.Body.Deployments = deploymentsResults
		return resp, nil
	})

	type GetDeploymentInput struct {
		DeploymentID string `path:"deploymentID" format:"uuid" doc:"Deployment ID" example:"652545de-7daf-465a-a773-d499f200d1b8"`
	}

	type GetDeploymentOutput struct {
		Body struct {
			Deployment deployments.DeploymentModel `json:"deployment"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-deployment",
		Method:        http.MethodGet,
		Path:          "/deployment/{deploymentID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Deployment Details",
		Tags:          []string{"Deployments", "Details"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *GetDeploymentInput) (*GetDeploymentOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		deploymentDetails, err := deployments.GetDeploymentDetails(a.queries, input.DeploymentID, userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Failed to fetch user details", err)
		}

		resp := &GetDeploymentOutput{}
		resp.Body.Deployment = *deploymentDetails

		return resp, nil
	})
}
