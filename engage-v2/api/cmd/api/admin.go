package main

import (
	"context"
	"net/http"

	"github.com/danielgtaylor/huma/v2"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/admin"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/users"
)

// addAdminRoutes registers all /admin routes to the application router
func addAdminRoutes(api huma.API, a *application) {

	// Get Admin Scripts
	type GetAdminScriptsOutput struct {
		Body struct {
			Scripts []admin.AdminScript `json:"scripts"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-admin-scripts",
		Method:        http.MethodGet,
		Path:          "/admin/scripts",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Admin Scripts",
		Tags:          []string{"Admin, Scripts"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetAdminScriptsOutput, error) {
		resp := &GetAdminScriptsOutput{}
		scriptsResult, err := admin.GetScripts(a.queries)

		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong", err)
		}

		resp.Body.Scripts = scriptsResult
		return resp, nil
	})

	type DeleteScriptInput struct {
		ScriptID string `path:"script_id" format:"uuid" doc:"Script ID" example:"abcd1234"`
	}

	huma.Register(api, huma.Operation{
		OperationID:   "delete-admin-script",
		Method:        http.MethodDelete,
		Path:          "/admin/scripts/{script_id}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Delete Admin Script",
		Tags:          []string{"Admin", "Scripts"},
		DefaultStatus: http.StatusNoContent,
	}, func(ctx context.Context, input *DeleteScriptInput) (*struct{}, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		err := users.DeleteScript(a.queries, input.ScriptID, userID)

		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong", err)
		}

		return nil, nil
	})

	// Create admin script
	type CreateAdminScriptInput struct {
		Body struct {
			Name        string            `json:"name" doc:"Name of the script" example:"My Script"`
			Description string            `json:"description" doc:"Description of the script" example:"This is a script description"`
			Content     string            `json:"content" doc:"Content of the script" example:"This is a script content"`
			ScriptType  db.ScriptTypeEnum `json:"script_type" enum:"ADMIN" doc:"Type of the script" example:"ADMIN"`
		}
	}

	type CreateAdminScriptOutput struct {
		Body struct {
			Script admin.AdminScript `json:"script" doc:"Created admin script"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "post-admin-script",
		Method:        http.MethodPost,
		Path:          "/admin/scripts",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Create Admin Script",
		Tags:          []string{"Admin", "Scripts"},
		DefaultStatus: http.StatusCreated,
	}, func(ctx context.Context, input *CreateAdminScriptInput) (*CreateAdminScriptOutput, error) {
		resp := &CreateAdminScriptOutput{}
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		newScript, err := admin.CreateNewAdminScript(a.queries, input.Body.Name, input.Body.Description, input.Body.Content, db.ScriptTypeEnumADMIN, userID)

		if err != nil {
			a.logger.Error("Error creating script in database", "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong", err)
		}

		resp.Body.Script = *newScript
		return resp, nil
	})

	// Edit Admin script
	type EditAdminScriptInput struct {
		ScriptID string `path:"script_id" format:"uuid" doc:"Script ID" example:"abcd1234"`
		Body     struct {
			Name        string            `json:"name" doc:"Updated name of the script" example:"Updated Script"`
			Description string            `json:"description" doc:"Updated description of the script" example:"This is an updated script description"`
			Content     string            `json:"content" doc:"Content of the script" example:"This is a script content"`
			ScriptType  db.ScriptTypeEnum `json:"script_type" enum:"ADMIN" doc:"Type of the script" example:"ADMIN"`
		}
	}

	type EditAdminScriptOutput struct {
		Body struct {
			Script admin.AdminScript `json:"script" doc:"Admin script details"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "edit-admin-script",
		Method:        http.MethodPut,
		Path:          "/admin/scripts/{script_id}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Edit Admin Script",
		Tags:          []string{"Admin", "Scripts"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *EditAdminScriptInput) (*EditAdminScriptOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		updatedScript, err := admin.UpdateAdminScript(a.queries, input.ScriptID, input.Body.Name, input.Body.Description, input.Body.Content, db.ScriptTypeEnumADMIN, userID)
		if err != nil {
			a.logger.Error("Error updating script in database", "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong", err)
		}

		resp := &EditAdminScriptOutput{}
		resp.Body.Script = *updatedScript
		return resp, nil
	})

}
