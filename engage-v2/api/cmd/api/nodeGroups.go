package main

import (
	"context"
	"net/http"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

// addNodeGroupsRoutes registers all /node-groups routes to the application router
func addNodeGroupsRoutes(api huma.API, a *application) {
	// Edit Node Group
	type EditNodeGroupInput struct {
		NodeGroupID string `path:"nodeGroupID" format:"uuid" doc:"Node Group ID" example:"9a7e965f-43ed-434b-bf08-059e8dca0111"`
		Body        struct {
			Name string `json:"name" doc:"Updated Node Group Name"`
		}
	}

	type UpdatedNodeGroup struct {
		ID           string    `json:"node_group_id" format:"uuid" doc:"Node Group ID" example:"9a7e965f-43ed-434b-bf08-059e8dca0111"`
		Name         string    `json:"name" doc:"Updated Node Group Name"`
		IsActive     bool      `json:"is_active"`
		CreatedAt    time.Time `json:"created_at" format:"date-time"`
		UpdatedAt    time.Time `json:"updated_at" format:"date-time"`
		EngagementID string    `json:"engagement_id" format:"uuid" doc:"Engagement ID"`
	}

	type EditNodeGroupOutput struct {
		NodeGroup UpdatedNodeGroup `json:"node_group"`
	}

	huma.Register(api, huma.Operation{
		OperationID:   "edit-node-group",
		Method:        http.MethodPut,
		Path:          "/node-groups/{nodeGroupID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Edit Node Group",
		Tags:          []string{"Node Groups"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *EditNodeGroupInput) (*EditNodeGroupOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp := &EditNodeGroupOutput{}
		nodeIDPgType, err := converters.StringToPgTypeUUID(i.NodeGroupID)
		if err != nil {
			if err != nil {
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			if err != nil {
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		}
		updatedNodeGroup, err := a.queries.UpdateNodeGroup(context.Background(), db.UpdateNodeGroupParams{
			Name: i.Body.Name,
			ID:   *nodeIDPgType,
			ID_2: *userIDPgType,
		})
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		updatedNodeGroupIDString, err := converters.PgTypeUUIDToString(updatedNodeGroup.ID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		updatedNodeGroupEngagementID, err := converters.PgTypeUUIDToString(updatedNodeGroup.EngagementID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		resp.NodeGroup.ID = *updatedNodeGroupIDString
		resp.NodeGroup.Name = updatedNodeGroup.Name
		resp.NodeGroup.IsActive = updatedNodeGroup.IsActive
		resp.NodeGroup.CreatedAt = updatedNodeGroup.CreatedAt.Time
		resp.NodeGroup.UpdatedAt = updatedNodeGroup.UpdatedAt.Time
		resp.NodeGroup.EngagementID = *updatedNodeGroupEngagementID

		return resp, nil
	})
}
