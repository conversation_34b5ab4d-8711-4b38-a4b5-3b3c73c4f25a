package main

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/csv"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/inventory"
)

type InventoryDomainResponse struct {
	ID            string `json:"id,omitempty"`
	URL           string `json:"url"`
	Registrar     string `json:"registrar,omitempty"`
	PurchaseDate  string `json:"purchase_date,omitempty"`
	RenewalDate   string `json:"renewal_date,omitempty"`
	Status        string `json:"status,omitempty"`
	Engagement    string `json:"engagement,omitempty"`
	Client        string `json:"client,omitempty"`
	Age           int    `json:"age,omitempty"`
	CreatedAt     string `json:"created_at,omitempty"`
	LastActivity  string `json:"last_activity,omitempty"`
	ActivityCount int    `json:"activity_count"`
	Message       string `json:"message,omitempty"`
	Type          string `json:"type,omitempty"`
	Username      string `json:"username,omitempty"`
}

// GetInventoryOutput represents the output of the inventory route.
type GetInventoryOutput struct {
	Body struct {
		NodeType   []inventory.NodeType            `json:"node_types"`
		TotalNodes int64                           `json:"total_nodes"`
		NodeGroups []inventory.NodeEngagementGroup `json:"node_groups"`
	}
}

// ImportDomainsOutput represents the output of the domain import route
type ImportDomainsOutput struct {
	Body struct {
		ImportedCount int `json:"importedCount"`
	}
}

func addInventoryRoutes(api huma.API, app *application) {
	huma.Register(api, huma.Operation{
		OperationID:   "get-inventory",
		Method:        http.MethodGet,
		Path:          "/inventory",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Inventory",
		Tags:          []string{"Inventory"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetInventoryOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		resp := &GetInventoryOutput{}
		nodeTypes, totalNodes, err := inventory.GetNodeTypes(app.queries, *userIDPgType)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		nodeGroups, err := inventory.GetEngagementNodesGroups(app.queries, userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong while fetching node groups")
		}
		resp.Body.NodeType = nodeTypes
		resp.Body.TotalNodes = *totalNodes
		resp.Body.NodeGroups = nodeGroups
		return resp, nil
	})

	type GetInventoryCloudInstancesOutput struct {
		Body struct {
			CloudInstances []inventory.InventoryCloudInstance `json:"cloud_instances"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-inventory-cloud-instances",
		Method:        http.MethodGet,
		Path:          "/inventory/cloud-instances",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Engagement Cloud Instances",
		Tags:          []string{"Inventory", "Cloud Instances"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetInventoryCloudInstancesOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		resp := &GetInventoryCloudInstancesOutput{}
		engagementsList, err := app.queries.GetUserEngagements(context.Background(), *userIDPgType)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}

		var engagementIDs []pgtype.UUID

		for _, engagement := range engagementsList {
			engagementIDs = append(engagementIDs, engagement.ID)
		}
		inventoryCloudInstances, err := inventory.GetInventoryCloudInstances(app.queries, engagementIDs, *userIDPgType)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp.Body.CloudInstances = inventoryCloudInstances
		return resp, nil
	})

	type GetInventoryHostsOutput struct {
		Body struct {
			CloudHosts []inventory.CloudHost `json:"hosts"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-inventory-hosts",
		Method:        http.MethodGet,
		Path:          "/inventory/hosts",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Engagement Hosts",
		Tags:          []string{"Inventory", "Hosts"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetInventoryHostsOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		resp := &GetInventoryHostsOutput{}
		engagementsList, err := app.queries.GetUserEngagements(context.Background(), *userIDPgType)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}

		var engagementIDs []pgtype.UUID

		for _, engagement := range engagementsList {
			engagementIDs = append(engagementIDs, engagement.ID)
		}
		inventoryHosts, err := inventory.GetInventoryHosts(app.queries, engagementIDs, *userIDPgType)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp.Body.CloudHosts = inventoryHosts
		return resp, nil
	})

	type GetInventoryEmailAddressesOutput struct {
		Body struct {
			EmailAddresses []inventory.EmailAddress `json:"email_addresses"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-inventory-email-addresses",
		Method:        http.MethodGet,
		Path:          "/inventory/email-addresses",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Engagement Email Addresses",
		Tags:          []string{"Inventory", "Email Addresses"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetInventoryEmailAddressesOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		resp := &GetInventoryEmailAddressesOutput{}
		engagementsList, err := app.queries.GetUserEngagements(context.Background(), *userIDPgType)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		var engagementIDs []pgtype.UUID

		for _, engagement := range engagementsList {
			engagementIDs = append(engagementIDs, engagement.ID)
		}
		inventoryEmailAddresses, err := inventory.GetInventoryEmailAddresses(app.queries, engagementIDs, *userIDPgType)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp.Body.EmailAddresses = inventoryEmailAddresses
		return resp, nil
	})

	type GetInventoryPersonsOutput struct {
		Body struct {
			Persons []inventory.Person `json:"persons"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-inventory-persons",
		Method:        http.MethodGet,
		Path:          "/inventory/persons",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Engagement Persons",
		Tags:          []string{"Inventory", "Persons"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetInventoryPersonsOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		resp := &GetInventoryPersonsOutput{}
		engagementsList, err := app.queries.GetUserEngagements(context.Background(), *userIDPgType)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		var engagementIDs []pgtype.UUID

		for _, engagement := range engagementsList {
			engagementIDs = append(engagementIDs, engagement.ID)
		}
		inventoryPersons, err := inventory.GetInventoryPersons(app.queries, engagementIDs, *userIDPgType)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp.Body.Persons = inventoryPersons
		return resp, nil
	})

	type GetInventoryUrlsOutput struct {
		Body struct {
			Urls []inventory.Url `json:"urls"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-inventory-urls",
		Method:        http.MethodGet,
		Path:          "/inventory/urls",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get Engagement Urls",
		Tags:          []string{"Inventory", "Urls"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetInventoryUrlsOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		resp := &GetInventoryUrlsOutput{}
		engagementsList, err := app.queries.GetUserEngagements(context.Background(), *userIDPgType)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		var engagementIDs []pgtype.UUID

		for _, engagement := range engagementsList {
			engagementIDs = append(engagementIDs, engagement.ID)
		}
		inventoryUrls, err := inventory.GetInventoryUrls(app.queries, engagementIDs, *userIDPgType)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp.Body.Urls = inventoryUrls
		return resp, nil
	})

	// Add a new endpoint for base64-encoded Excel files
	type ImportDomainsBase64Input struct {
		Body struct {
			FileName    string `json:"fileName"`
			FileContent string `json:"fileContent"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "post-inventory-domains-import-base64",
		Method:        http.MethodPost,
		Path:          "/inventory/domains/import",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Import domains from CSV file",
		Tags:          []string{"Inventory", "Domains"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *ImportDomainsBase64Input) (*ImportDomainsOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when processing user ID")
		}

		// Check file name extension
		fileExt := strings.ToLower(strings.Split(i.Body.FileName, ".")[len(strings.Split(i.Body.FileName, "."))-1])
		if fileExt != "csv" {
			return nil, huma.Error400BadRequest("Only csv files (.csv) are supported")
		}

		// Decode base64 content
		fileContent, err := base64.StdEncoding.DecodeString(i.Body.FileContent)
		if err != nil {
			app.logger.Error("Error decoding base64 content", "error", err.Error())
			return nil, huma.Error400BadRequest("Invalid file content: " + err.Error())
		}

		// Process the CSV file
		importCount, err := ImportDomainsFromCSVContent(ctx, app, fileContent, *userIDPgType)
		if err != nil {
			app.logger.Error("Error processing CSV file", "error", err.Error())
			return nil, huma.Error500InternalServerError("Failed to process file: " + err.Error())
		}

		// Return response with import count
		resp := &ImportDomainsOutput{}
		resp.Body.ImportedCount = importCount

		return resp, nil
	})

	// Template download response structure
	type GetInventoryDomainsTemplateOutput struct {
		Body []byte `header:"Content-Type:text/csv,Content-Disposition:attachment; filename=\"domain_import_template.csv\""`
	}

	// Template download endpoint
	huma.Register(api, huma.Operation{
		OperationID:   "get-inventory-domains-template",
		Method:        http.MethodGet,
		Path:          "/inventory/domains/template",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Download Domain Import Template CSV",
		Tags:          []string{"Inventory", "Domains"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetInventoryDomainsTemplateOutput, error) {
		// Build CSV in memory
		var buf bytes.Buffer
		wcsv := csv.NewWriter(&buf)
		rows := [][]string{
			{"domain", "registrar", "purchaseDate", "renewalDate", "status", "engagement", "client", "age"},
		}
		if err := wcsv.WriteAll(rows); err != nil {
			return nil, huma.Error500InternalServerError("CSV write error: " + err.Error())
		}
		wcsv.Flush()
		if err := wcsv.Error(); err != nil {
			return nil, huma.Error500InternalServerError("CSV flush error: " + err.Error())
		}

		// Return response with CSV content
		resp := &GetInventoryDomainsTemplateOutput{}
		resp.Body = buf.Bytes()
		return resp, nil
	})

	type GetInventoryDomainsOutput struct {
		Body struct {
			Domains []InventoryDomainResponse `json:"domains"`
		}
	}

	// Define the InventoryDomainResponse struct for the inventory domains endpoint
	

	huma.Register(api, huma.Operation{
		OperationID:   "get-inventory-domains",
		Method:        http.MethodGet,
		Path:          "/inventory/domains",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get all domains with activity history",
		Tags:          []string{"Inventory", "Domains"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, input *struct{}) (*GetInventoryDomainsOutput, error) {
		// Get all domains with their history from logs_nodes
		domainsHistory, err := app.queries.GetDomainsWithHistory(ctx)
		if err != nil {
			return nil, huma.Error500InternalServerError(fmt.Sprintf("Error fetching domains history: %v", err))
		}

		// Convert directly to response format without aggregation
		domainsList := make([]InventoryDomainResponse, 0, len(domainsHistory))
		for _, record := range domainsHistory {
			// Skip records without domain URL
			if record.DomainUrl.String == "" {
				continue
			}
			
			// Format the activity timestamp
			var lastActivity string
			if record.CreatedAt.Valid {
				lastActivity = record.CreatedAt.Time.Format("02.01.2006 15:04:05")
			}
			
			// Add domain with its activity record
			domainsList = append(domainsList, InventoryDomainResponse{
				URL:          record.DomainUrl.String,
				Registrar:    record.Registrar.String,
				Engagement:   record.Engagement.String,
				Client:       record.Client.String,
				LastActivity: lastActivity,
				Message:      record.Message,
				Type:         string(record.Type),
				Username:     record.UserUsername.String,
			})
		}
		
		resp := &GetInventoryDomainsOutput{}
		resp.Body.Domains = domainsList
		return resp, nil
	})
}

// ImportDomainsFromCSVContent imports domains from CSV file content into the database
func ImportDomainsFromCSVContent(ctx context.Context, a *application, fileContent []byte, userID pgtype.UUID) (int, error) {
	// Log the first few bytes to check for BOM
	if len(fileContent) > 10 {
		a.logger.Debug("CSV file first bytes", "bytes", fmt.Sprintf("%x", fileContent[:10]))
	}

	// Check for BOM and remove if present
	if len(fileContent) >= 3 && fileContent[0] == 0xEF && fileContent[1] == 0xBB && fileContent[2] == 0xBF {
		a.logger.Info("Removing BOM from CSV file")
		fileContent = fileContent[3:]
	}

	// Try to detect the delimiter
	content := string(fileContent)
	firstLine := ""
	if idx := strings.Index(content, "\n"); idx > 0 {
		firstLine = content[:idx]
	} else {
		firstLine = content
	}

	delimiter := ','
	if strings.Count(firstLine, ";") > strings.Count(firstLine, ",") {
		delimiter = ';'
		a.logger.Info("Detected semicolon as CSV delimiter")
	} else {
		a.logger.Info("Using comma as CSV delimiter")
	}

	// Parse CSV content
	reader := csv.NewReader(bytes.NewReader(fileContent))
	reader.Comma = delimiter
	reader.TrimLeadingSpace = true
	reader.LazyQuotes = true    // Allow lazy quotes
	reader.FieldsPerRecord = -1 // Allow variable number of fields

	// Read all rows
	rows, err := reader.ReadAll()
	if err != nil {
		a.logger.Error("Error reading CSV", "error", err.Error())
		return 0, fmt.Errorf("error reading CSV file: %w", err)
	}

	if len(rows) < 2 {
		return 0, fmt.Errorf("CSV file has no data rows")
	}

	// Get header row and normalize column names
	header := rows[0]

	// Debug log the raw header
	a.logger.Info("Raw CSV header", "header", header)

	// Handle potential BOM in the first column name
	if len(header) > 0 && strings.HasPrefix(header[0], "\ufeff") {
		header[0] = strings.TrimPrefix(header[0], "\ufeff")
		a.logger.Info("Removed BOM from first column", "column_name", header[0])
	}

	// Map column indices from header row (case insensitive)
	colMap := make(map[string]int)
	for i, col := range header {
		normalizedCol := strings.ToLower(strings.TrimSpace(col))
		colMap[normalizedCol] = i
		a.logger.Debug("CSV column mapped", "original", col, "normalized", normalizedCol, "index", i)
	}

	// Check required column with better error message
	if _, exists := colMap["domain"]; !exists {
		// Try to find similar column names
		possibleDomainColumns := []string{}
		for col := range colMap {
			if strings.Contains(col, "domain") || strings.Contains(col, "url") ||
				strings.Contains(col, "website") || strings.Contains(col, "site") {
				possibleDomainColumns = append(possibleDomainColumns, col)
			}
		}

		errorMsg := "required column 'domain' not found in CSV"
		if len(possibleDomainColumns) > 0 {
			errorMsg += fmt.Sprintf(". Found similar columns: %s", strings.Join(possibleDomainColumns, ", "))
		}
		errorMsg += fmt.Sprintf(". Available columns: %s", strings.Join(header, ", "))

		a.logger.Error(errorMsg)
		return 0, fmt.Errorf("%s", errorMsg)
	}

	// Begin transaction
	tx, err := a.dbConn.Begin(ctx)
	if err != nil {
		return 0, fmt.Errorf("error starting transaction: %w", err)
	}
	defer func() {
		if rollbackErr := tx.Rollback(ctx); rollbackErr != nil {
			a.logger.Error("Error rolling back transaction", "error", rollbackErr.Error())
		}
	}() // This will be a no-op if tx.Commit() is called

	qtx := a.queries.WithTx(tx)

	// Process data rows
	importCount := 0
	updateCount := 0
	var importErrors []string

	for i := 1; i < len(rows); i++ {
		row := rows[i]

		// Skip if row doesn't have enough columns
		if len(row) <= colMap["domain"] {
			continue
		}

		// Extract domain data
		domain := strings.TrimSpace(row[colMap["domain"]])
		if domain == "" {
			continue // Skip empty domains
		}


		// Extract and prepare metadata
		var registrar, status, engagement, client string
		var purchaseDate, renewalDate pgtype.Date
		var age int32
		var ageValid bool

		// Extract registrar if column exists
		if idx, exists := colMap["registrar"]; exists && idx < len(row) {
			registrar = strings.TrimSpace(row[idx])
		}

		// Extract purchase date if column exists
		if idx, exists := colMap["purchasedate"]; exists && idx < len(row) {
			dateStr := strings.TrimSpace(row[idx])
			if dateStr != "" {
				if parsedDate, err := parseDate(dateStr); err == nil {
					purchaseDate = pgtype.Date{Time: parsedDate, Valid: true}
					a.logger.Debug("Successfully parsed purchase date", "original", dateStr, "parsed", parsedDate.Format("02.01.2006"))
				} else {
					a.logger.Warn("Failed to parse purchase date", "date_string", dateStr, "error", err.Error(), "row", i)
				}
			}
		}

		// Extract renewal date if column exists
		if idx, exists := colMap["renewaldate"]; exists && idx < len(row) {
			dateStr := strings.TrimSpace(row[idx])
			if dateStr != "" {
				if parsedDate, err := parseDate(dateStr); err == nil {
					renewalDate = pgtype.Date{Time: parsedDate, Valid: true}
					a.logger.Debug("Successfully parsed renewal date", "original", dateStr, "parsed", parsedDate.Format("02.01.2006"))
				} else {
					a.logger.Warn("Failed to parse renewal date", "date_string", dateStr, "error", err.Error(), "row", i)
				}
			}
		}

		// Extract status if column exists
		if idx, exists := colMap["status"]; exists && idx < len(row) {
			status = strings.TrimSpace(row[idx])
		}

		// Validate and normalize status to ensure it's a valid enum value
		status = strings.ToUpper(status)
		validStatuses := map[string]bool{
			"UNASSIGNED": true,
			"ASSIGNED":   true,
			"QUARANTINE": true,
			"BURNED":     true,
			"EXPIRED":    true,
		}

		// Set default status to 'UNASSIGNED' if status is empty, not provided, or invalid
		if status == "" || !validStatuses[status] {
			if status != "" {
				a.logger.Warn("Invalid domain status in CSV, using default", "invalid_status", status, "row", i, "domain", domain)
			}
			status = "UNASSIGNED"
		}

		// Extract engagement if column exists
		if idx, exists := colMap["engagement"]; exists && idx < len(row) {
			engagement = strings.TrimSpace(row[idx])
		}

		// Extract client if column exists
		if idx, exists := colMap["client"]; exists && idx < len(row) {
			client = strings.TrimSpace(row[idx])
		}

		// Extract age if column exists
		if idx, exists := colMap["age"]; exists && idx < len(row) {
			ageStr := strings.TrimSpace(row[idx])
			if ageStr != "" {
				if ageVal, err := strconv.Atoi(ageStr); err == nil {
					age = int32(ageVal)
					ageValid = true
				}
			}
		}

		// Check if domain already exists in the database
		existingDomains, err := qtx.GetDomainsByUrl(ctx, domain)
		if err != nil && err.Error() != "no rows in result set" {
			a.logger.Error("Error checking for existing domain", "error", err.Error(), "domain", domain)
			importErrors = append(importErrors, fmt.Sprintf("Row %d (%s): %s", i, domain, err.Error()))
			continue
		}

		if len(existingDomains) > 0 {
			// Domain exists, update it
			updateParams := db.UpdateDomainParams{
				ID:           existingDomains[0].ID,
				Url:          domain,
				Registrar:    pgtype.Text{String: registrar, Valid: registrar != ""},
				PurchaseDate: purchaseDate,
				RenewalDate:  renewalDate,
				Status:       db.NullDomainStatusEnum{DomainStatusEnum: db.DomainStatusEnum(status), Valid: status != ""},
				Engagement:   pgtype.Text{String: engagement, Valid: engagement != ""},
				Client:       pgtype.Text{String: client, Valid: client != ""},
				Age:          pgtype.Int4{Int32: age, Valid: ageValid},
			}

			_, err = qtx.UpdateDomain(ctx, updateParams)
			if err != nil {
				a.logger.Error("Error updating domain entry", "error", err.Error(), "domain", domain, "row", i)
				importErrors = append(importErrors, fmt.Sprintf("Row %d (%s): %s", i, domain, err.Error()))
				continue
			}
			updateCount++
		} else {
			// Domain doesn't exist, insert it
			_, err = qtx.CreateDomain(ctx, db.CreateDomainParams{
				Url:          domain,
				Registrar:    pgtype.Text{String: registrar, Valid: registrar != ""},
				PurchaseDate: purchaseDate,
				RenewalDate:  renewalDate,
				Status:       db.NullDomainStatusEnum{DomainStatusEnum: db.DomainStatusEnum(status), Valid: status != ""},
				Engagement:   pgtype.Text{String: engagement, Valid: engagement != ""},
				Client:       pgtype.Text{String: client, Valid: client != ""},
				Age:          pgtype.Int4{Int32: age, Valid: ageValid},
			})
			if err != nil {
				a.logger.Error("Error creating domain entry", "error", err.Error(), "domain", domain, "row", i)
				importErrors = append(importErrors, fmt.Sprintf("Row %d (%s): %s", i, domain, err.Error()))
				continue
			}
			importCount++
		}
	}

	// If we have errors but also some successful imports/updates, log the errors but continue
	if len(importErrors) > 0 && (importCount > 0 || updateCount > 0) {
		a.logger.Warn("Some domains failed to import", "errors", importErrors, "successful_imports", importCount, "successful_updates", updateCount)
	}

	// If we have no successful imports/updates but have errors, return an error
	if importCount == 0 && updateCount == 0 && len(importErrors) > 0 {
		return 0, fmt.Errorf("failed to import any domains: %s", strings.Join(importErrors[:min(5, len(importErrors))], "; "))
	}

	// Commit transaction
	err = tx.Commit(ctx)
	if err != nil {
		return 0, fmt.Errorf("error committing transaction: %w", err)
	}

	totalCount := importCount + updateCount
	a.logger.Info("Domain import completed", "new_domains", importCount, "updated_domains", updateCount, "total", totalCount)
	return totalCount, nil
}

// Helper function to parse dates in various formats
func parseDate(dateStr string) (time.Time, error) {
	// Try different date formats, prioritizing European format DD.MM.YYYY
	formats := []string{
		"02.01.2006",      // European format DD.MM.YYYY (e.g., 17.06.2025)
		"2006-01-02",      // ISO format YYYY-MM-DD
		"01/02/2006",      // US format MM/DD/YYYY
		"02/01/2006",      // European format DD/MM/YYYY
		"Jan 2, 2006",     // Month name format
		"2 Jan 2006",      // Day Month Year
		"January 2, 2006", // Full month name
		"2006/01/02",      // YYYY/MM/DD
		"02-01-2006",      // DD-MM-YYYY
		"01-02-2006",      // MM-DD-YYYY
	}

	for i, format := range formats {
		if t, err := time.Parse(format, dateStr); err == nil {
			// Successfully parsed - log which format worked
			if i == 0 {
				// European format worked - this is expected
				return t, nil
			} else {
				// Log when non-European format was used
				fmt.Printf("Date '%s' parsed using format '%s' instead of European format\n", dateStr, format)
			}
			return t, nil
		}
	}

	return time.Time{}, fmt.Errorf("could not parse date '%s' with any of the supported formats", dateStr)
}

// Helper function to get minimum of two integers
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

type DomainsListResponse struct {
	Domains []InventoryDomainResponse `json:"domains"`
}

