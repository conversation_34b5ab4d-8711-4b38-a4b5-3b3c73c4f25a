package main

import (
	"encoding/json"
	"testing"

	"github.com/danielgtaylor/huma/v2"
	"github.com/danielgtaylor/huma/v2/humatest"
)

// MockAdminUserAuthMiddleware creates a mock middleware with an admin user
func MockAdminUserAuthMiddleware() func(ctx huma.Context, next func(huma.Context)) {
	return func(ctx huma.Context, next func(huma.Context)) {
		// TODO change the userID when proper authorization is in place
		ctx = huma.WithValue(ctx, "userID", "admin_user_id")
		ctx = huma.WithValue(ctx, "role", "admin")
		next(ctx)
	}
}

// MockStandardUserAuthMiddleware creates a mock middleware with a standard user
func MockStandardUserAuthMiddleware() func(ctx huma.Context, next func(huma.Context)) {
	return func(ctx huma.Context, next func(huma.Context)) {
		// TODO change the userID when proper authorization is in place
		ctx = huma.WithValue(ctx, "userID", "standard_user_id")
		ctx = huma.WithValue(ctx, "role", "standard")
		next(ctx)
	}
}

func TestGetEngagementsAsAdmin(t *testing.T) {
	app, _, _ := InitApplication()

	_, api := humatest.New(t)
	api.UseMiddleware(MockAdminUserAuthMiddleware())
	addEngagementRoutes(api, app)

	resp := api.Get("/engagements")

	// Parse the JSON response
	var responseBody map[string]interface{}
	if err := json.Unmarshal(resp.Body.Bytes(), &responseBody); err != nil {
		t.Fatalf("Failed to parse JSON: %v", err)
	}

	// TODO change expected number of assigned Engagements
	expectedNumberOfEngagements := 3
	// Check that the user see all Engagements they are assigned to
	engagements := responseBody["engagements"].([]interface{})
	if len(engagements) != expectedNumberOfEngagements {
		t.Fatalf("Engagements length is incorrect. Expected 3, got %d", len(engagements))
	}
}
