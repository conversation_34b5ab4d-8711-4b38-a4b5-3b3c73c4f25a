package main

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/activitylogs"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

type ActivityLog struct {
	Message   string               `json:"message"`
	Type      db.LogsNodesTypeEnum `json:"type"`
	Username  string               `json:"username"`
	CreatedAt time.Time            `json:"created_at"`
}

// addNodesRoutes registers all /nodes routes to the application router
func addNodesRoutes(api huma.API, a *application) {

	type EdgeToRemove struct {
		SourceID string `json:"sourceId" format:"uuid"`
		TargetID string `json:"targetId" format:"uuid"`
	}

	type NodeRelationshipInput struct {
		Body struct {
			SourceNodeID  string         `json:"source_node_id" format:"uuid"`
			TargetNodeID  string         `json:"target_node_id" format:"uuid"`
			Descendants   []string       `json:"descendants" format:"uuid"`
			EdgesToRemove []EdgeToRemove `json:"edgesToRemove"`
			Type          string         `json:"type"`
		}
	}

	// Create a relationship between two Nodes
	huma.Register(api, huma.Operation{
		OperationID:   "post-nodes-relationship",
		Method:        http.MethodPost,
		Path:          "/nodes/relationship",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Create a relationship between two Nodes for an Engagement",
		Tags:          []string{"Nodes, Relationships"},
		DefaultStatus: http.StatusCreated,
	}, func(ctx context.Context, i *NodeRelationshipInput) (*struct{}, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}

		sourceNodeIDPgType, err := converters.StringToPgTypeUUID(i.Body.SourceNodeID)
		if err != nil {
			a.logger.Error("Error parsing SourceNodeID", "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		targetNodeIDPgType, err := converters.StringToPgTypeUUID(i.Body.TargetNodeID)
		if err != nil {
			a.logger.Error("Error parsing TargetNodeID", "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		// Check if node_group_id is the same
		result, err := a.queries.CheckNodeGroupId(context.Background(), db.CheckNodeGroupIdParams{
			ID:   *sourceNodeIDPgType,
			ID_2: *targetNodeIDPgType,
		})
		if err != nil {
			a.logger.Error("Error checking NodeGroupId", "error", err.Error())
			return nil, huma.Error500InternalServerError("Failed to check NodeGroupId")
		}

		if result == "Same" {
			a.logger.Warn("Source and Target nodes belong to the same Node Group",
				"source_node_id", i.Body.SourceNodeID,
				"target_node_id", i.Body.TargetNodeID,
			)
			err = a.queries.CreateNodeRelationship(context.Background(), db.CreateNodeRelationshipParams{
				SourceNodeID: *sourceNodeIDPgType,
				TargetNodeID: *targetNodeIDPgType,
			})
			if err != nil {
				a.logger.Error("Error storing Node relationship",
					"source_node_id", i.Body.SourceNodeID,
					"target_node_id", i.Body.TargetNodeID,
					"error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}

			err = a.queries.InsertActivityLog(context.Background(), db.InsertActivityLogParams{
				Message: fmt.Sprintf("Created outbound relationship to %s", i.Body.TargetNodeID),
				Type:    db.LogsNodesTypeEnumOUTBOUNDRELATIONSHIPCREATION,
				UserID:  *userIDPgType,
				NodeID:  *sourceNodeIDPgType,
				CreatedAt: pgtype.Timestamp{
					Time:             time.Now(),
					InfinityModifier: 0,
					Valid:            true,
				},
			})
			if err != nil {
				a.logger.Error("Error storing activity log for creating outbound relationship", "node_source_id", i.Body.SourceNodeID, "node_target_id", i.Body.TargetNodeID, "error", err.Error())
				return nil, huma.Error500InternalServerError("Failed to delete node")
			}

			err = a.queries.InsertActivityLog(context.Background(), db.InsertActivityLogParams{
				Message: fmt.Sprintf("Created inbound relationship from %s", i.Body.SourceNodeID),
				Type:    db.LogsNodesTypeEnumINBOUNDRELATIONSHIPCREATION,
				UserID:  *userIDPgType,
				NodeID:  *targetNodeIDPgType,
				CreatedAt: pgtype.Timestamp{
					Time:             time.Now(),
					InfinityModifier: 0,
					Valid:            true,
				},
			})
			if err != nil {
				a.logger.Error("Error storing activity log for creating inbound relationship", "node_source_id", i.Body.SourceNodeID, "node_target_id", i.Body.TargetNodeID, "error", err.Error())
				return nil, huma.Error500InternalServerError("Failed to delete node")
			}

			a.logger.Info("Created Node relationship successfully",
				"source_node_id", i.Body.SourceNodeID,
				"target_node_id", i.Body.TargetNodeID,
			)
		} else {
			a.logger.Warn("Source and Target nodes do not belong to the same Node Group",
				"source_node_id", i.Body.SourceNodeID,
				"target_node_id", i.Body.TargetNodeID,
			)
			descendantsPgType := make([]pgtype.UUID, 0, len(i.Body.Descendants))
			for _, descendant := range i.Body.Descendants {
				parsedUUID, err := uuid.Parse(descendant)
				if err != nil {
					a.logger.Error("Error parsing Descendant ID", "descendant_id", descendant, "error", err.Error())
					return nil, fmt.Errorf("failed to parse descendant ID %s to pgtype.UUID: %v", descendant, err)
				}

				descendantsPgType = append(descendantsPgType, pgtype.UUID{
					Bytes: [16]byte(parsedUUID),
					Valid: true,
				})
			}

			if len(i.Body.EdgesToRemove) > 0 {

				for _, edge := range i.Body.EdgesToRemove {
					sourceID, err := converters.StringToPgTypeUUID(edge.SourceID)
					if err != nil {
						log.Printf("Invalid source UUID: %v", err)
						continue
					}

					targetID, err := converters.StringToPgTypeUUID(edge.TargetID)
					if err != nil {
						log.Printf("Invalid target UUID: %v", err)
						continue
					}

					err = a.queries.DeleteRelationship(context.Background(), db.DeleteRelationshipParams{
						Column1: *sourceID,
						Column2: *targetID,
					})
					if err != nil {
						a.logger.Error("Error delete Node relationship",
							"source_node_id", i.Body.SourceNodeID,
							"target_node_id", i.Body.TargetNodeID,
							"error", err.Error())
						return nil, huma.Error500InternalServerError("Something went wrong")
					}
				}
			}

			var nodeGroupIdParam *pgtype.UUID
			if i.Body.Type == "inbound" {
				nodeGroupIdParam = targetNodeIDPgType
			} else {
				nodeGroupIdParam = sourceNodeIDPgType
			}

			nodeGroupId, err := a.queries.GetNodeGroupId(context.Background(), *nodeGroupIdParam)

			if err == nil {
				fmt.Println("Success nodeGroupId", nodeGroupId)
			}

			if err != nil {
				a.logger.Error("Error getting Node Group ID", "error", err.Error())
				return nil, fmt.Errorf("failed to update Node Group ID with Descendants: %v", err)
			}

			err = a.queries.UpdateNodeGroupId(context.Background(), db.UpdateNodeGroupIdParams{
				NodeGroupID: nodeGroupId,
				Column2:     descendantsPgType,
			})

			if err != nil {
				a.logger.Error("Error updating Node Group ID in database", "error", err.Error())
				return nil, fmt.Errorf("failed to update Node Group ID in database: %v", err)
			}

			err = a.queries.CreateNodeRelationship(context.Background(), db.CreateNodeRelationshipParams{
				SourceNodeID: *sourceNodeIDPgType,
				TargetNodeID: *targetNodeIDPgType,
			})
			if err != nil {
				a.logger.Error("Error storing Node relationship",
					"source_node_id", i.Body.SourceNodeID,
					"target_node_id", i.Body.TargetNodeID,
					"error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		}

		return nil, nil
	})

	type DeleteNodeInput struct {
		NodeID string `path:"node_id" format:"uuid"`
	}

	huma.Register(api, huma.Operation{
		OperationID:   "delete-node",
		Method:        http.MethodDelete,
		Path:          "/nodes/{node_id}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Delete Node",
		Tags:          []string{"Nodes"},
		DefaultStatus: http.StatusNoContent,
	}, func(ctx context.Context, i *DeleteNodeInput) (*struct{}, error) {
		userID, ok := ctx.Value("userID").(string)
		fmt.Println("User id delete node ", userID)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		nodeIDPgType, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			a.logger.Error("Error parsing NodeID", "error", err.Error())
			return nil, huma.Error500InternalServerError("Invalid node ID")
		}

		err = a.queries.DeleteNode(ctx, *nodeIDPgType)
		if err != nil {
			a.logger.Error("Error deleting Node", "nodeID", i.NodeID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Failed to delete node")
		}

		// Get node type and name before setting domain inactive
		nodeTypeAndName, err := a.queries.GetNodeTypeAndName(ctx, *nodeIDPgType)
		if err != nil {
			a.logger.Error("Error checking Node type and name", "nodeID", i.NodeID, "error", err.Error())
			// Continue execution even if we can't get the node type and name
		} else {
			// If node type is URL, set the domain to inactive
			if nodeTypeAndName.NodeType == db.NodeTypeEnumURL {
				err = a.queries.SetDomainQuarantine(ctx, nodeTypeAndName.Name)
				if err != nil {
					a.logger.Error("Error setting domain inactive", "url", nodeTypeAndName.Name, "error", err.Error())
					// Continue execution even if setting domain inactive fails
				} else {
					a.logger.Info("Set domain to inactive", "url", nodeTypeAndName.Name)
				}
			}
		}

		timestamp := pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		}

		if nodeTypeAndName.NodeType == db.NodeTypeEnumURL {
			err = activitylogs.InsertLog(a.queries, "domain set to  quarantine successfully", db.LogsNodesTypeEnumDOMAINQUARANTINED, *userIDPgType, *nodeIDPgType, timestamp)
			if err != nil {
				a.logger.Error("Error inserting log", "error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		} else {
			err = activitylogs.InsertLog(a.queries, "Deleted successfully", db.LogsNodesTypeEnumNODEUPDATE, *userIDPgType, *nodeIDPgType, timestamp)
			if err != nil {
				a.logger.Error("Error inserting log", "error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		}

		

		return nil, nil
	})

	type DeleteNodeEdge struct {
		SourceID string `path:"sourceID" format:"uuid"`
		TargetID string `path:"targetID" format:"uuid"`
	}

	huma.Register(api, huma.Operation{
		OperationID:   "delete-node-relationship",
		Method:        http.MethodDelete,
		Path:          "/nodes/{sourceID}/relationships/{targetID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Delete a relationship between two Nodes for an Engagement",
		Tags:          []string{"Nodes", "Relationships"},
		DefaultStatus: http.StatusNoContent,
	}, func(ctx context.Context, i *DeleteNodeEdge) (*struct{}, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}

		sourceIDPgType, err := converters.StringToPgTypeUUID(i.SourceID)
		if err != nil {
			a.logger.Error("Error parsing sourceID", "error", err.Error())
			return nil, huma.Error500InternalServerError("Invalid source ID")
		}

		targetIDPgType, err := converters.StringToPgTypeUUID(i.TargetID)
		if err != nil {
			a.logger.Error("Error parsing target ID", "error", err.Error())
			return nil, huma.Error500InternalServerError("Invalid target ID")
		}

		err = a.queries.DeleteNodeRelationship(ctx, db.DeleteNodeRelationshipParams{
			SourceNodeID: *sourceIDPgType,
			TargetNodeID: *targetIDPgType,
		})
		if err != nil {
			a.logger.Error("Error deleting Node", "nodeID", i.SourceID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Failed to delete node")
		}

		err = a.queries.InsertActivityLog(context.Background(), db.InsertActivityLogParams{
			Message: fmt.Sprintf("Deleted outbound relationship to %s", i.TargetID),
			Type:    db.LogsNodesTypeEnumOUTBOUNDRELATIONSHIPCREATION,
			UserID:  *userIDPgType,
			NodeID:  *sourceIDPgType,
			CreatedAt: pgtype.Timestamp{
				Time:             time.Now(),
				InfinityModifier: 0,
				Valid:            true,
			},
		})
		if err != nil {
			a.logger.Error("Error storing activity log for deleting outbound relationship", "node_source_id", i.SourceID, "node_target_id", i.TargetID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Failed to delete node")
		}

		err = a.queries.InsertActivityLog(context.Background(), db.InsertActivityLogParams{
			Message: fmt.Sprintf("Deleted inbound relationship from %s", i.SourceID),
			Type:    db.LogsNodesTypeEnumINBOUNDRELATIONSHIPCREATION,
			UserID:  *userIDPgType,
			NodeID:  *targetIDPgType,
			CreatedAt: pgtype.Timestamp{
				Time:             time.Now(),
				InfinityModifier: 0,
				Valid:            true,
			},
		})
		if err != nil {
			a.logger.Error("Error storing activity log for deleting inbound relationship", "node_source_id", i.SourceID, "node_target_id", i.TargetID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Failed to delete node")
		}

		a.logger.Info("Deleted Node relationship successfully",
			"source_node_id", i.SourceID,
			"target_node_id", i.TargetID,
		)

		return nil, nil
	})
}
