package main

import (
	"context"
	"net/http"
	"net/netip"
	"time"

	"github.com/danielgtaylor/huma/v2"
	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/activitylogs"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

func addNodeTypeHostRoutes(api huma.API, a *application) {
	// Get Node Type Host
	type GetNodeTypeHostInput struct {
		NodeID              string `path:"nodeID" format:"uuid" doc:"Node ID" example:"29ffb3ac-18a8-499e-ba5d-442f89c25175"`
		IncludeActivityLogs bool   `query:"activity_logs" format:"bool" doc:"Whether to include activity logs (optional). Example: true or false." default:"true"`
	}

	type NodeHost struct {
		Name             string       `json:"name"`
		IpAddresses      []netip.Addr `json:"ip_addresses"`
		AlternativeNames []string     `json:"alternative_names"`
		NodeID           string       `json:"node_id" format:"uuid" doc:"Node ID"`
	}

	type GetNodeTypeHostOutput struct {
		Body struct {
			Node         NodeHost      `json:"node"`
			ActivityLogs []ActivityLog `json:"activity_logs,omitempty" doc:"Activity Logs"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "get-nodes-host",
		Method:        http.MethodGet,
		Path:          "/nodes/host/{nodeID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Get a Host Node",
		Tags:          []string{"Nodes, Host"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *GetNodeTypeHostInput) (*GetNodeTypeHostOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		resp := &GetNodeTypeHostOutput{}
		nodeIDPgType, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, err
		}
		node, err := a.queries.GetNodeTypeHost(context.Background(), db.GetNodeTypeHostParams{
			NodeID: *nodeIDPgType,
			ID:     *userIDPgType,
		})
		if err != nil {
			a.logger.Error("Error getting Node Host", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
			return nil, huma.Error404NotFound("Node not found")
		}

		resp.Body.Node.Name = node.Name
		resp.Body.Node.IpAddresses = node.IpAddresses
		resp.Body.Node.AlternativeNames = node.AlternativeNames
		resp.Body.Node.NodeID = i.NodeID

		if i.IncludeActivityLogs {
			activityLogs := make([]ActivityLog, 0)
			activityLogsDB, err := a.queries.GetNodeActivityLogs(context.Background(), *nodeIDPgType)
			if err != nil {
				a.logger.Error("Error getting activity logs", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
				return nil, huma.Error404NotFound("Activity logs not found")
			}
			for _, activityLogDB := range activityLogsDB {
				activityLogs = append(activityLogs, ActivityLog{
					Message:   activityLogDB.Message,
					Type:      activityLogDB.Type,
					Username:  activityLogDB.Username,
					CreatedAt: activityLogDB.CreatedAt.Time,
				})
			}
			resp.Body.ActivityLogs = activityLogs
		}
		return resp, nil
	})

	// Edit Node Type Host
	type EditNodeTypeHostInput struct {
		NodeID string `path:"nodeID" format:"uuid" doc:"Node ID" example:"962d5baa-6d73-4caa-b343-af32a3819805"`
		Body   struct {
			IpAddresses      []netip.Addr `json:"ip_addresses"`
			Name             string       `json:"name"`
			AlternativeNames []string     `json:"alternative_names,omitempty"`
		}
	}

	type EditNodeTypeHostOutput struct {
		NodeID           string       `json:"node_id" format:"uuid" doc:"Node ID" example:"962d5baa-6d73-4caa-b343-af32a3819805"`
		IpAddresses      []netip.Addr `json:"ip_addresses"`
		Name             string       `json:"name"`
		AlternativeNames []string     `json:"alternative_names"`
	}

	huma.Register(api, huma.Operation{
		OperationID:   "edit-nodes-host",
		Method:        http.MethodPut,
		Path:          "/nodes/host/{nodeID}",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Update a Host Node",
		Tags:          []string{"Nodes, Host"},
		DefaultStatus: http.StatusOK,
	}, func(ctx context.Context, i *EditNodeTypeHostInput) (*EditNodeTypeHostOutput, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		nodeIDPgType, err := converters.StringToPgTypeUUID(i.NodeID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		resp := &EditNodeTypeHostOutput{}

		updatedNodeTypeHost, err := a.queries.UpdateNodeTypeHost(context.Background(),
			db.UpdateNodeTypeHostParams{
				NodeID:           *nodeIDPgType,
				Name:             i.Body.Name,
				IpAddresses:      i.Body.IpAddresses,
				AlternativeNames: i.Body.AlternativeNames,
			})
		if err != nil {
			a.logger.Error("Error updating Host Node in database", "user_id", userID, "nodeID", i.NodeID, "error", err)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		// Update the nodes table with the name
		updateNodeNameParams := db.UpdateNodeNameParams{
			Name: i.Body.Name,
			ID:   *nodeIDPgType,
		}

		err = a.queries.UpdateNodeName(ctx, updateNodeNameParams)
		if err != nil {
			a.logger.Error("Error updating Node name in database", "user_id", userID, "nodeID", i.NodeID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		timestamp := pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		}

		err = activitylogs.InsertLog(a.queries, "Updated successfully", db.LogsNodesTypeEnumNODEUPDATE, *userIDPgType, *nodeIDPgType, timestamp)
		if err != nil {
			a.logger.Error("Error inserting log", "user_id", userID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		a.logger.Info("Updated Host Node successfully",
			"user_id", userID,
			"node_id", i.NodeID,
			"ip_addresses", i.Body.IpAddresses,
			"name", i.Body.Name,
			"alternative_names", i.Body.AlternativeNames,
		)

		resp.IpAddresses = updatedNodeTypeHost.IpAddresses
		resp.Name = updatedNodeTypeHost.Name
		resp.AlternativeNames = updatedNodeTypeHost.AlternativeNames
		resp.NodeID = i.NodeID

		return resp, nil
	})

	// Create Node Type Host
	type CreateNodeTypeHostInput struct {
		Body struct {
			EngagementID     string   `json:"engagement_id" format:"uuid" doc:"Engagement ID" example:"b4ccc447-46bc-465d-8526-621f1cab1c8b"`
			NodeGroupID      string   `json:"node_group_id,omitempty" format:"uuid" doc:"Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created" example:"9a7e965f-43ed-434b-bf08-059e8dca0111"`
			Name             string   `json:"name" maxLength:"100" example:"Host A"`
			IPAddresses      []string `json:"ip_addresses,omitempty" format:"ipv4" example:"127.0.0.1, ***********"`
			AlternativeNames []string `json:"alternative_names,omitempty" example:"Host A1, Host A2"`
		}
	}

	huma.Register(api, huma.Operation{
		OperationID:   "post-nodes-host",
		Method:        http.MethodPost,
		Path:          "/nodes/host",
		Security:      []map[string][]string{{"microsoft_entra_auth": {"scope1"}}},
		Summary:       "Create a Host Node for an Engagement",
		Tags:          []string{"Nodes, Host"},
		DefaultStatus: http.StatusCreated,
	}, func(ctx context.Context, i *CreateNodeTypeHostInput) (*struct{}, error) {
		userID, ok := ctx.Value("userID").(string)
		if !ok {
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		userIDPgType, err := converters.StringToPgTypeUUID(userID)
		if err != nil {
			return nil, huma.Error500InternalServerError("Something went wrong when fetching engagements")
		}
		engagementIDPgType, err := converters.StringToPgTypeUUID(i.Body.EngagementID)
		if err != nil {
			a.logger.Error("Error parsing EngagementID", "user_id", userID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		var associatedNodeGroupID pgtype.UUID

		// No Node Group ID was provided to create the Node in, create a new one
		if len(i.Body.NodeGroupID) == 0 {
			associatedNodeGroup, err := a.queries.CreateNodeGroup(context.Background(), db.CreateNodeGroupParams{
				Name:         "New Node Group",
				IsActive:     true,
				EngagementID: *engagementIDPgType,
				CreatedAt: pgtype.Timestamp{
					Time:             time.Now(),
					InfinityModifier: 0,
					Valid:            true,
				},
				UpdatedAt: pgtype.Timestamp{
					Time:             time.Now(),
					InfinityModifier: 0,
					Valid:            true,
				},
			})
			if err != nil {
				a.logger.Error("Error creating new Node Group in database", "error", err.Error(),
					"user_id", userID,
					"engagement_id", i.Body.EngagementID,
					"node_type", "host",
					"node_group", i.Body.NodeGroupID)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			associatedNodeGroupID = associatedNodeGroup.ID
		} else {
			// An existing Node Group ID was provided, check if it is associated with the Engagement
			// and associate it with the new Node
			nodeGroupIDPgType, err := converters.StringToPgTypeUUID(i.Body.NodeGroupID)
			if err != nil {
				a.logger.Error("Error parsing NodeGroupID", "user_id", userID, "error", err.Error())
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
			nodeGroupsDB, err := a.queries.GetEngagementNodeGroup(context.Background(), db.GetEngagementNodeGroupParams{
				ID:           *nodeGroupIDPgType,
				EngagementID: *engagementIDPgType,
			})
			if err != nil {
				a.logger.Error("Error getting Node Group", "user_id", userID, "error", err.Error(), "node_group_id", i.Body.NodeGroupID)
				return nil, huma.Error500InternalServerError("Something went wrong")
			}

			// If the Node Group is associated with the Engagement, use its ID
			// Otherwise return a generic error to the user for security reasons
			if len(nodeGroupsDB) == 1 {
				associatedNodeGroupID = nodeGroupsDB[0].ID
			} else {
				a.logger.Error("Error getting Node Group associated with Engagement while creating a Node",
					"user_id", userID,
					"node_group_id", i.Body.NodeGroupID,
					"engagement_id", i.Body.EngagementID,
					"node_type", "host")
				return nil, huma.Error500InternalServerError("Something went wrong")
			}
		}

		createdNode, err := a.queries.CreateNode(context.Background(), db.CreateNodeParams{
			NodeType:    "HOST",
			Name:        i.Body.Name,
			NodeGroupID: associatedNodeGroupID,
		})
		if err != nil {
			a.logger.Error("Error creating Node in database", "error", err.Error(),
				"user_id", userID,
				"engagement_id", i.Body.EngagementID,
				"node_type", "host",
				"node_group", i.Body.NodeGroupID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		// Convert IP Addresses []string to []netip.Addr
		ipAddresses, err := converters.StringsToNetIpAddresses(i.Body.IPAddresses)
		if err != nil {
			a.logger.Error("Error converting IP addresses", "user_id", userID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		createHostNodeParams := db.CreateHostNodeParams{
			Name:             i.Body.Name,
			IpAddresses:      ipAddresses,
			AlternativeNames: i.Body.AlternativeNames,
			NodeID:           createdNode.ID,
		}

		if len(ipAddresses) == 0 {
			createHostNodeParams.IpAddresses = make([]netip.Addr, 0)
		}

		if len(i.Body.AlternativeNames) == 0 {
			createHostNodeParams.AlternativeNames = make([]string, 0)
		}

		err = a.queries.CreateHostNode(context.Background(), createHostNodeParams)
		if err != nil {
			a.logger.Error("Error creating Node in database", "error", err.Error(),
				"user_id", userID,
				"engagement_id", i.Body.EngagementID,
				"node_type", "host",
				"node_group", i.Body.NodeGroupID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		timestamp := pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		}

		err = activitylogs.InsertLog(a.queries, "Created successfully", db.LogsNodesTypeEnumNODECREATION, *userIDPgType, createdNode.ID, timestamp)
		if err != nil {
			a.logger.Error("Error inserting log", "user_id", userID, "error", err.Error())
			return nil, huma.Error500InternalServerError("Something went wrong")
		}

		createdNodeIDString, err := converters.PgTypeUUIDToString(createdNode.ID)
		if err != nil {
			a.logger.Error("Error converting Node ID to string", "user_id", userID, "error", err.Error(), "node_type", createdNode.NodeType, "node_id", createdNode.ID)
			return nil, huma.Error500InternalServerError("Something went wrong")
		}
		a.logger.Info("Created Host Node successfully",
			"user_id", userID,
			"engagement_id", i.Body.EngagementID,
			"name", i.Body.Name,
			"ip_addresses", ipAddresses,
			"alternative_names", i.Body.AlternativeNames,
			"node_id", createdNodeIDString,
		)
		return nil, nil
	})

}
