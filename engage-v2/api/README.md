# engage-v2-api

<!-- TOC -->
* [engage-v2-api](#engage-v2-api)
  * [Prerequisites](#prerequisites)
    * [1. Install Go 1.23](#1-install-go-123)
      * [MacOS (using brew)](#macos-using-brew)
      * [Ubuntu/WSL (using snap)](#ubuntuwsl-using-snap)
    * [2. Install `sqlc`](#2-install-sqlc)
      * [macOS](#macos)
      * [Ubuntu/WSL (using snap)](#ubuntuwsl-using-snap-1)
    * [3. Install `golangci-lint` 1.61](#3-install-golangci-lint-161)
    * [4. Setup and run supported services (PostgreSQL, RabbitMQ)](#4-setup-and-run-supported-services-postgresql-rabbitmq)
    * [5. Create a `.env` file in the root of the project with the necessary variables](#5-create-a-env-file-in-the-root-of-the-project-with-the-necessary-variables)
    * [6. Install dependencies](#6-install-dependencies)
    * [7. Install AWS CLI](#7-install-aws-cli-required-for-aws-mfa-credentials-script)
  * [Run application](#run-application)
  * [Linting](#linting)
  * [Create executable](#create-executable)
  * [View API documentation](#view-api-documentation)
  * [View code documentation in HTML](#view-code-documentation-in-html)
  * [Regenerate SQL operations using `sqlc`](#regenerate-sql-operations-using-sqlc)
  * [Appendix](#appendix)
    * [Current list of AWS permissions required](#current-list-of-aws-permissions-required)
    * [Zsh script to set temporary credentials using MFA](#zsh-script-to-set-temporary-credentials-using-mfa)
    * [Bash script to set temporary credentials using MFA](#bash-script-to-set-temporary-credentials-using-mfa)
<!-- TOC -->

Engage API based on [Go](https://go.dev/) and using the [Huma](https://huma.rocks/) REST API framework.

## Prerequisites

### 1. Install [Go 1.23](https://go.dev/doc/install)

#### MacOS (using [brew](https://brew.sh/))

```bash
brew install go
```

#### Ubuntu/WSL (using [snap](https://go.dev/wiki/Ubuntu))

```bash
sudo snap install --classic go
```

### 2. Install [`sqlc`](https://docs.sqlc.dev/en/stable/overview/install.html)

#### macOS

```bash
brew install sqlc
```

#### Ubuntu/WSL (using [snap](https://docs.sqlc.dev/en/stable/overview/install.html#ubuntu))

```bash
sudo snap install sqlc
```

### 3. Install `golangci-lint` 1.63.4

It's highly recommended installing a specific version of `golangci-lint` available on
the [releases page](https://github.com/golangci/golangci-lint/releases).

The current version used in the project is 1.63.4.

```bash
# binary will be $(go env GOPATH)/bin/golangci-lint
curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $(go env GOPATH)/bin v1.63.4

golangci-lint --version
```

### 4. Setup and run supported services (PostgreSQL, RabbitMQ)

In order to easily run a PostgreSQL and RabbitMQ server, from the root of the project run:

```bash
docker compose up -d db rabbitmq
```

This will automatically create a database called `engage` with a user `engage` and password `engage`. This is just a
convenience for development purposes.

Then apply the schema from the file `sql/schema.sql`.

If you want to populate the database with some test data, use the file `sql/test_data.sql`.

### 5. Create a `.env` file in the root of the project with the necessary variables

```dotenv
# A random key used for cryptographic signing
SECRET_KEY=Ao2cie|do9euj7ieMuch^ae9ie"g5pie

# Azure tenant
AZURE_TENANT_ID=...
AZURE_APP_ID=...
AZURE_CLIENT_SECRET=...

# Azure User Groups
STANDARD_USERS_GROUP_ID=dc4b22e2-7e41-4807-971c-69aca94187a7
ADMIN_USERS_GROUP_ID=3232a771-2321-4141-9513-dfa9423f94b9

# CORS
CORS_URL=http://localhost:5173

# Database
DB_USER=engage
DB_PASSWORD=engage
DB_HOST=localhost
DB_PORT=5432
DB_NAME=engage

# RabbitMQ
RABBITMQ_USER=guest
RABBITMQ_PASSWORD=guest
RABBITMQ_HOST=localhost
RABBITMQ_PORT=5672

# Terraform - replace with the path of the Terraform executable on your machine
TERRAFORM_EXEC_PATH=/opt/homebrew/bin/terraform

# AWS account creation
AWS_ROOT_REGION=eu-west-2
AWS_ROOT_EMAIL=<EMAIL>
```

### 6. Install dependencies

```bash
go mod download
```

### 7. Install AWS CLI (required for AWS MFA credentials script)

#### MacOS

```bash
brew install awscli
```

#### Ubuntu/WSL

```bash
sudo apt install awscli -y
```

#### Check

```bash
aws --version
```

## Run application

Runs the API on port `8080`:

```bash
go run ./cmd/api/
```

or using `make`

```bash
make run
```

## Linting

```bash
golangci-lint run
```

or

```bash
make lint
```

## Create executable

```bash
go build -o build/engage-api ./cmd/api
```

or using `make`

```bash
make build
```

## View API documentation

API documentation is automatically generated with the help of the web
framework [Huma](https://huma.rocks/features/api-docs/) and is accessible under the `/docs` endpoint.

Assuming the server runs on `localhost` and port `8080`, the URL is:

http://localhost:8080/docs

## View code documentation in HTML

If you haven't already, install `godoc`:

```bash
go install golang.org/x/tools/cmd/godoc@latest
```

Run the Go Documentation server

```bash
godoc -http :8080
```

Documentation specific to Engage can be found in the following URL:

http://localhost:8080/pkg/gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/?m=all

## Regenerate SQL operations using `sqlc`

In order to regenerate SQL operations, whenever changes are made to the database schema in `sql/schema.sql`, execute:

```bash
sqlc regenerate
```

## Appendix

### Current list of AWS permissions required

A sample AWS IAM
user [a.panagiotopoulos](https://us-east-1.console.aws.amazon.com/iam/home?region=eu-west-2#/users/details/a.panagiotopoulos?section=permissions)
has been created, under the
group [engage-devs](https://us-east-1.console.aws.amazon.com/iam/home?region=eu-west-2#/groups/details/engage-devs?section=users),
using
the [engage-devs-policy](https://us-east-1.console.aws.amazon.com/iam/home?region=eu-west-2#/policies/details/arn%3Aaws%3Aiam%3A%3A304359549939%3Apolicy%2Fengage-devs-policy?section=permissions),
with the following permissions:

```json
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Sid": "VisualEditor0",
      "Effect": "Allow",
      "Action": [
        "ec2:DescribeImages",
        "ec2:DescribeRegions",
        "ec2:DescribeInstanceTypes",
        "ec2:RunInstances",
        "ec2:CreateTags",
        "ec2:DescribeInstances",
        "ec2:DescribeTags",
        "ec2:DescribeInstanceAttribute",
        "ec2:DescribeVolumes",
        "ec2:DescribeInstanceCreditSpecifications",
        "ec2:TerminateInstances",
        "ec2:CreateSecurityGroup",
        "ec2:DescribeSecurityGroups",
        "ec2:RevokeSecurityGroupEgress"
      ],
      "Resource": "*"
    }
  ]
}
```

### Zsh script to set temporary credentials using MFA

```zsh
#!/bin/zsh

# Set your MFA serial number - replace with your generator
MFA_SERIAL_NUMBER="arn:aws:iam::058264276150:mfa/engage-unrestricted-mfa"
# AWS profile - replace with yours
AWS_PROFILE="engage"
# AWS region - replace with yours
AWS_DEFAULT_REGION="eu-west-2"

# Prompt the user for the AWS token code
vared -p "Enter your AWS token code: " -c token_code

session_token=$(aws sts get-session-token --serial-number "$MFA_SERIAL_NUMBER" --profile "$AWS_PROFILE" --token-code "$token_code" --output json)

# Extract Access Key, Secret Key, and Session Token
AWS_ACCESS_KEY_ID=$(echo "$session_token" | jq -r .Credentials.AccessKeyId)
AWS_SECRET_ACCESS_KEY=$(echo "$session_token" | jq -r .Credentials.SecretAccessKey)
AWS_SESSION_TOKEN=$(echo "$session_token" | jq -r .Credentials.SessionToken)

# Check if credentials were retrieved successfully
if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ] || [ -z "$AWS_SESSION_TOKEN" ]; then
    echo "Failed to retrieve temporary AWS credentials."
    exit 1
fi

# Use sed to update or add the AWS credentials in ~/.zshrc
# The format accommodates both macOS and GNU sed versions
sed -i '' '/export AWS_ACCESS_KEY_ID=/d' ~/.zshrc
sed -i '' '/export AWS_SECRET_ACCESS_KEY=/d' ~/.zshrc
sed -i '' '/export AWS_SESSION_TOKEN=/d' ~/.zshrc
sed -i '' '/export AWS_DEFAULT_REGION=/d' ~/.zshrc

# Append new values to ~/.zshrc
echo "export AWS_ACCESS_KEY_ID=\"$AWS_ACCESS_KEY_ID\"" >> ~/.zshrc
echo "export AWS_SECRET_ACCESS_KEY=\"$AWS_SECRET_ACCESS_KEY\"" >> ~/.zshrc
echo "export AWS_SESSION_TOKEN=\"$AWS_SESSION_TOKEN\"" >> ~/.zshrc
echo "export AWS_DEFAULT_REGION=\"$AWS_DEFAULT_REGION\"" >> ~/.zshrc

echo "Temporary AWS credentials have been added to ~/.zshrc."
echo "Run 'source ~/.zshrc' to apply them in the current session."
```

### Bash script to set temporary credentials using MFA

```bash
#!/bin/bash

# Set your MFA serial number - replace with your generator
MFA_SERIAL_NUMBER="arn:aws:iam::058264276150:mfa/engage-unrestricted-mfa"
# AWS profile - replace with yours
AWS_PROFILE="engage"
# AWS region - replace with yours
AWS_DEFAULT_REGION="eu-west-2"

# Prompt the user for the AWS token code
read -p "Enter your AWS token code: " token_code

session_token=$(aws sts get-session-token --serial-number "$MFA_SERIAL_NUMBER" --profile "$AWS_PROFILE" --token-code "$token_code" --output json)

# Extract Access Key, Secret Key, and Session Token
AWS_ACCESS_KEY_ID=$(echo "$session_token" | jq -r .Credentials.AccessKeyId)
AWS_SECRET_ACCESS_KEY=$(echo "$session_token" | jq -r .Credentials.SecretAccessKey)
AWS_SESSION_TOKEN=$(echo "$session_token" | jq -r .Credentials.SessionToken)

# Check if credentials were retrieved successfully
if [ -z "$AWS_ACCESS_KEY_ID" ] || [ -z "$AWS_SECRET_ACCESS_KEY" ] || [ -z "$AWS_SESSION_TOKEN" ]; then
    echo "Failed to retrieve temporary AWS credentials."
    exit 1
fi

# Use sed to update or add the AWS credentials in ~/.bashrc
# The format accommodates both macOS and GNU sed versions
sed -i '' '/export AWS_ACCESS_KEY_ID=/d' ~/.bashrc
sed -i '' '/export AWS_SECRET_ACCESS_KEY=/d' ~/.bashrc
sed -i '' '/export AWS_SESSION_TOKEN=/d' ~/.bashrc
sed -i '' '/export AWS_DEFAULT_REGION=/d' ~/.bashrc

# Append new values to ~/.bashrc
echo "export AWS_ACCESS_KEY_ID=\"$AWS_ACCESS_KEY_ID\"" >> ~/.bashrc
echo "export AWS_SECRET_ACCESS_KEY=\"$AWS_SECRET_ACCESS_KEY\"" >> ~/.bashrc
echo "export AWS_SESSION_TOKEN=\"$AWS_SESSION_TOKEN\"" >> ~/.bashrc
echo "export AWS_DEFAULT_REGION=\"$AWS_DEFAULT_REGION\"" >> ~/.bashrc

echo "Temporary AWS credentials have been added to ~/.bashrc."
echo "Run 'source ~/.bashrc' to apply them in the current session."
```
