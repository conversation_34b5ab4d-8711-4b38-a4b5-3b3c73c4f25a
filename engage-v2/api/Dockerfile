# Stage 1: Build the Go application
FROM golang:1.24-alpine AS builder

# Set the working directory inside the container
WORKDIR /app

# Copy the Go modules file
COPY go.mod go.sum ./

# Download Go modules
RUN go mod download

# Copy the rest of the source code
COPY . .

# Build the Go binary
RUN CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/engage-api ./cmd/api

# Stage 2: Create a minimal image to run the Go application
FROM alpine:latest

# Install the psql client for startup checks
RUN apk --update add postgresql-client

# Install Terraform
ARG TERRAFORM_VERSION=1.11.1
RUN apk --no-cache add curl unzip
RUN curl -LO https://releases.hashicorp.com/terraform/${TERRAFORM_VERSION}/terraform_${TERRAFORM_VERSION}_linux_amd64.zip \
    && unzip terraform_${TERRAFORM_VERSION}_linux_amd64.zip -d /usr/local/bin \
    && rm terraform_${TERRAFORM_VERSION}_linux_amd64.zip
RUN terraform --version

ENV TERRAFORM_EXEC_PATH=/usr/local/bin/terraform

# Set the working directory in the final image
WORKDIR /app

# Copy the Go binary from the builder stage
COPY --from=builder /app/build/engage-api .
# Copy the entrypoint script from the builder stage
COPY --from=builder /app/entrypoint.sh .
RUN chmod +x /app/entrypoint.sh
# Copy the SQL Schema incase we need it
COPY --from=builder /app/sql/schema.sql .

# Create Terraform deployments folder
RUN mkdir -p /app/deployments_playground

# Expose the necessary port
EXPOSE 8080

# Run the Go application
ENTRYPOINT ["/app/entrypoint.sh"]
CMD ["./engage-api"]