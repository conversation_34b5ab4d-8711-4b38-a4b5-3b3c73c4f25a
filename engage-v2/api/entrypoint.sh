#!/bin/sh

cd /app 
env > .env

TABLE=engagements
SQL_EXISTS="SELECT * from ${TABLE} limit 1;"

# Wait for Database
until PGPASSWORD="${DB_PASSWORD}" psql  -h ${DB_HOST} -U ${DB_USER} -d postgres -c '\q'; do
  echo "Postgres is unavailable - sleeping"
  sleep 1
done

CHECK_EXISTS=`PGPASSWORD="${DB_PASSWORD}" psql -h ${DB_HOST} -U ${DB_USER} -d ${DB_NAME} -c "${SQL_EXISTS}"`
CHECK_EXISTS_RET=$?

if [ $CHECK_EXISTS_RET -eq 1 ]; then
    echo "Schema not imported... init database"
    echo PGPASSWORD="${DB_PASSWORD}" psql -h ${DB_HOST} -U ${DB_USER} -d ${DB_NAME} -a -f ./schema.sql
    PGPASSWORD="${DB_PASSWORD}" psql -h ${DB_HOST} -U ${DB_USER} -d ${DB_NAME} -a -f ./schema.sql
fi

exec "$@"