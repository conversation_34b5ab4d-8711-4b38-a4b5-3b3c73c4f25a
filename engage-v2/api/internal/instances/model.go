package instances

import (
	"context"
	"fmt"

	"github.com/danielgtaylor/huma/v2"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/aws"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/azure"
)

type GetInstanceTypesOutput struct {
	Body struct {
		InstanceTypes []aws.InstanceType                 `json:"instance_types"`
		Validation    []aws.RegionInstanceTypeValidation `json:"validation"`
		Mappings      []db.InstanceSizeMapping           `json:"mappings"`
	}
}

func HandleCustomInstanceTypes(ctx context.Context, region string, category string, queries *db.Queries) (*GetInstanceTypesOutput, error) {
	_, ok := ctx.Value("userID").(string)
	if !ok {
		return nil, huma.Error500InternalServerError("Something went wrong")
	}

	resp := &GetInstanceTypesOutput{}
	awsEc2Client := aws.NewEc2Client(region)

	// Get all instance types from AWS
	instanceTypesResult, err := awsEc2Client.GetAllCloudInstanceTypes()
	if err != nil {
		return nil, huma.Error500InternalServerError("Failed to get instance types")
	}

	// Get mappings specific to this category
	mappings, err := queries.GetInstanceSizeMappingsForCategory(ctx, db.GetInstanceSizeMappingsForCategoryParams{
		Provider:  "AWS",
		SizeAlias: category,
	})
	if err != nil {
		return nil, huma.Error500InternalServerError("Failed to get instance mappings")
	}

	// Validate configured instance types for this category
	validation, err := awsEc2Client.ValidateInstanceTypesInRegion(queries, category)
	if err != nil {
		return nil, huma.Error500InternalServerError("Failed to validate instance types")
	}

	resp.Body.Mappings = mappings
	resp.Body.InstanceTypes = instanceTypesResult
	resp.Body.Validation = validation
	return resp, nil
}

func FetchAzureVMRegions(ctx context.Context, queries *db.Queries, tenantID string) ([]string, error) {
	tenant, err := queries.GetAzureTenantByTenantID(ctx, tenantID)
	if err != nil {
		return nil, fmt.Errorf("failed to get credentials for tenant: %w", err)
	}
	sm, err := azure.GetAzureSecret(tenant.ID.String())
	if err != nil {
		return nil, fmt.Errorf("failed to get secrets for tenant: %w", err)
	}
	cred, err := azure.GetAzureCredential(tenantID, sm.AppID, sm.AppSecret)
	if err != nil {
		return nil, err
	}

	client, err := azure.NewSubscriptionClient(cred)
	if err != nil {
		return nil, err
	}

	pager := client.NewListLocationsPager(tenant.SubscriptionID.String, nil)
	var regions []string

	for pager.More() {
		page, err := pager.NextPage(ctx)
		if err != nil {
			return nil, err
		}
		for _, loc := range page.Value {
			if loc.Name != nil {
				regions = append(regions, *loc.Name)
			}
		}
	}

	return regions, nil
}

type AzureAMI struct {
	Publisher string `json:"publisher"`
	Offer     string `json:"offer"`
	Sku       string `json:"sku"`
	Version   string `json:"version"`
	Name      string `json:"name"`
}

func GetHardcodedAzureAmis(ctx context.Context) []AzureAMI {
	return []AzureAMI{
		{
			Publisher: "debian",
			Offer:     "debian-11",
			Sku:       "11-gen2",
			Version:   "latest",
			Name:      "debian-11-gen2",
		},
		{
			Publisher: "debian",
			Offer:     "debian-11",
			Sku:       "11-backports",
			Version:   "latest",
			Name:      "debian-11-backports",
		},
		{
			Publisher: "debian",
			Offer:     "debian-12",
			Sku:       "12-gen2",
			Version:   "latest",
			Name:      "debian-12-gen2",
		},
		{
			Publisher: "debian",
			Offer:     "debian-12",
			Sku:       "12",
			Version:   "latest",
			Name:      "debian-12",
		},
	}
}
