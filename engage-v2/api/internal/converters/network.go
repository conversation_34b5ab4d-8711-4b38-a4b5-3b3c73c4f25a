package converters

import (
	"fmt"
	"net/netip"
)

// StringsToNetIpAddresses converts a slice of strings to a slice of
// netip.Addr
func StringsToNetIpAddresses(strings []string) ([]netip.Addr, error) {
	var addresses []netip.Addr

	for _, s := range strings {
		addr, err := netip.ParseAddr(s)
		if err != nil {
			return nil, fmt.Errorf("invalid IP address: %v", s)
		}
		addresses = append(addresses, addr)
	}

	return addresses, nil
}
