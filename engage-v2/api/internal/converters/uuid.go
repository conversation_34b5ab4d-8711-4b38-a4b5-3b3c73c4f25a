package converters

import (
	"encoding/hex"
	"fmt"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
)

// StringToPgTypeUUID converts a string to a pgtype.UUID
func StringToPgTypeUUID(s string) (*pgtype.UUID, error) {
	uuidBytes, err := uuid.Parse(s)
	if err != nil {
		return nil, err
	}

	uuidPgType := pgtype.UUID{Valid: true, Bytes: uuidBytes}

	return &uuidPgType, nil
}

// PgTypeUUIDToString converts a pgtype.UUID to a string
func PgTypeUUIDToString(u pgtype.UUID) (*string, error) {
	// Convert the fixed-size array [16]byte to a byte slice []byte
	uuidSlice := u.Bytes[:]

	// Convert the byte slice to a hex string
	uuidStr := hex.EncodeToString(uuidSlice)

	// Insert dashes to match the UUID format
	formattedUUID := fmt.Sprintf("%s-%s-%s-%s-%s",
		uuidStr[0:8], uuidStr[8:12], uuidStr[12:16], uuidStr[16:20], uuidStr[20:])
	return &formattedUUID, nil
}

func StringToPgTypeText(s string) pgtype.Text {
	if s == "" {
		return pgtype.Text{Valid: false}
	}
	return pgtype.Text{Valid: true, String: s}
}
