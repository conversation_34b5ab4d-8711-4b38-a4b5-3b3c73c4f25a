package accounts

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log/slog"
	"strings"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/charmbracelet/keygen"
	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments/keys"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/azure"
)

type AzureTenantForm struct {
	AzureTenantID       string `json:"azure_tenant_id" doc:"Azure Tenant ID" maxLength:"50"`
	AzureSubscriptionID string `json:"azure_subscription_id,omitempty" doc:"Azure Subscription ID" maxLength:"50"`
	AppID               string `json:"azure_app_id,omitempty" doc:"Azure App ID" maxLength:"50"`
	AppSecret           string `json:"azure_app_secret,omitempty" doc:"Azure App Secret" maxLength:"80"`
}

type TenantError struct {
	TenantID       string `json:"tenant_id,omitempty"`
	SubscriptionID string `json:"subscription_id,omitempty"`
	Message        string `json:"message"`
}

type ValidationErrorResponse struct {
	Error []TenantError `json:"error"`
}

func AddAzureTenantData(
	logger *slog.Logger,
	dbConn *pgxpool.Pool,
	dbQueries *db.Queries,
	engagementID, secretKey, awsRootEmail, awsRootRegion string,
	tenant AzureTenantForm,
) ([]TenantError, error) {
	ctx := context.Background()
	var errorMessages []TenantError

	// Validate engagement ID
	engagementUUID, err := converters.StringToPgTypeUUID(engagementID)
	if err != nil {
		logger.Error("Invalid engagement ID", "error", err)
		return nil, fmt.Errorf("invalid engagement ID: %w", err)
	}

	// DB validation checks
	// ---------------------------------------------
	subscriptionText := converters.StringToPgTypeText(tenant.AzureSubscriptionID)

	// 1. Check if tenant already exists
	dbTenant, err := dbQueries.GetAzureTenantByTenantID(ctx, tenant.AzureTenantID)
	if err != nil && err.Error() != "no rows in result set" {
		logger.Error("DB tenant lookup error", "err", err)
		return nil, fmt.Errorf("error checking tenant existence: %w", err)
	}
	if err == nil && dbTenant.TenantID != "" {
		errorMessages = append(errorMessages, TenantError{
			TenantID: tenant.AzureTenantID,
			Message:  fmt.Sprintf("Tenant %s is already registered", tenant.AzureTenantID),
		})
	}

	// 2. Check if subscription already exists
	subExists, err := dbQueries.GetAzureTenantBySubscriptionID(ctx, subscriptionText)
	isNotFound := err != nil && err.Error() == "no rows in result set"
	if err != nil && !isNotFound {
		logger.Error("DB subscription lookup error", "err", err)
		return nil, fmt.Errorf("error checking subscription existence: %w", err)
	}
	if err == nil && subExists.SubscriptionID.Valid {
		errorMessages = append(errorMessages, TenantError{
			TenantID:       tenant.AzureTenantID,
			SubscriptionID: tenant.AzureSubscriptionID,
			Message:        fmt.Sprintf("Subscription %s is already registered", tenant.AzureSubscriptionID),
		})
	}

	// If either tenant or subscription exists, stop here, return errors to UI
	if len(errorMessages) > 0 {
		logger.Warn("Tenant DB validation failed", "errors", errorMessages)
		return errorMessages, fmt.Errorf("validation failed")
	}
	// ---------------------------------------------

	// Validate credentials in Azure, if Azure validation fails, stop here return errors to UI
	if tenant.AppID != "" && tenant.AppSecret != "" {
		credErr, otherErr := validateAzureCredentials(ctx, tenant.AzureTenantID, tenant.AppID, tenant.AppSecret, tenant.AzureSubscriptionID)
		if credErr != nil {
			errorMessages = append(errorMessages, TenantError{
				TenantID:       tenant.AzureTenantID,
				SubscriptionID: tenant.AzureSubscriptionID,
				Message:        fmt.Sprintf("Invalid Azure credentials for tenant %s", tenant.AzureTenantID),
			})
			return errorMessages, fmt.Errorf("validation failed")
		}
		if otherErr != nil {
			logger.Error("Azure SDK validation failed", "error", otherErr)
			return nil, fmt.Errorf("Azure SDK validation failed: %w", otherErr)
		}
	}

	// Generate SSH keys
	sshPassphrase, err := keys.GeneratePassword(32)
	if err != nil {
		return nil, fmt.Errorf("failed to generate SSH passphrase: %w", err)
	}

	encryptedPassphrase, salt, err := keys.EncryptSecret([]byte(sshPassphrase), secretKey)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt SSH passphrase: %w", err)
	}

	kp, err := keygen.New("path", keygen.WithPassphrase(sshPassphrase), keygen.WithKeyType(keygen.RSA))
	if err != nil {
		return nil, fmt.Errorf("failed to generate SSH keypair: %w", err)
	}

	// Start transaction
	tx, err := dbConn.Begin(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to start transaction: %w", err)
	}
	defer func() {
		if rbErr := tx.Rollback(ctx); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			logger.Error("rollback failed", "err", rbErr)
		}
	}()

	// Save tenant
	statusPending := converters.StringToPgTypeText("PENDING")
	initialCloudStatus := converters.StringToPgTypeText("Checking")
	dbID, err := dbQueries.WithTx(tx).SaveAzureTenantData(ctx, db.SaveAzureTenantDataParams{
		EngagementID:       *engagementUUID,
		TenantID:           tenant.AzureTenantID,
		SubscriptionID:     subscriptionText,
		PolicyID:           string(encryptedPassphrase),
		StatusID:           salt,
		SshKeyPublic:       kp.AuthorizedKey(),
		CreationStatus:     statusPending,
		AccountCloudStatus: initialCloudStatus,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to save tenant %s: %w", tenant.AzureTenantID, err)
	}

	if tenant.AppID != "" && tenant.AppSecret != "" {
		// Save secrets
		sm, err := keys.NewSecretsManager(awsRootRegion)
		if err != nil {
			logger.Error("Error initializing SecretsManager", "error", err)
			_ = dbQueries.WithTx(tx).SetAzureTenantStatusToError(ctx, db.SetAzureTenantStatusToErrorParams{
				ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
				ID:           dbID,
			})
			return nil, fmt.Errorf("failed to initialize secrets manager: %w", err)
		}

		secretData := map[string]string{
			"TenantID":        tenant.AzureTenantID,
			"AppID":           tenant.AppID,
			"AppSecret":       tenant.AppSecret,
			"ssh_private_key": string(kp.RawProtectedPrivateKey()),
		}
		_, err = sm.CreateSecret(dbID.String(), secretData)
		if err != nil {
			logger.Error("Error saving Azure secrets", "err", err)
			_ = dbQueries.WithTx(tx).SetAzureTenantStatusToError(ctx, db.SetAzureTenantStatusToErrorParams{
				ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
				ID:           dbID,
			})
			errorMessages = append(errorMessages, TenantError{
				TenantID:       tenant.AzureTenantID,
				SubscriptionID: tenant.AzureSubscriptionID,
				Message:        fmt.Sprintf("Failed to save secrets for tenant %s", tenant.AzureTenantID),
			})
			return errorMessages, fmt.Errorf("secrets creation failed")
		}

		err = dbQueries.WithTx(tx).UpdateAzureTenantSecretsStatus(ctx, dbID)
		if err != nil {
			logger.Error("Failed to update secrets status", "tenant_id", tenant.AzureTenantID, "error", err)
		}
	}

	// Set status to success
	err = dbQueries.WithTx(tx).SetAzureTenantStatusToSuccess(ctx, dbID)
	if err != nil {
		logger.Error("Failed to update tenant status to success", "tenant_id", tenant.AzureTenantID)
	}

	// Commit transaction
	if err := tx.Commit(ctx); err != nil {
		return nil, fmt.Errorf("failed to commit transaction: %w", err)
	}

	// Immediately check Azure subscription status after successful creation
	if tenant.AppID != "" && tenant.AppSecret != "" && tenant.AzureSubscriptionID != "" {
		go func() {
			// Run status check in background to avoid blocking the response
			err := checkAzureSubscriptionStatusImmediate(ctx, dbQueries, dbID, tenant.AzureTenantID, tenant.AppID, tenant.AppSecret, tenant.AzureSubscriptionID, logger)
			if err != nil {
				logger.Error("Failed immediate Azure status check", "tenant_id", tenant.AzureTenantID, "error", err)
			}
		}()
	}

	return nil, nil
}

// checkAzureSubscriptionStatusImmediate checks Azure subscription status immediately after tenant creation
func checkAzureSubscriptionStatusImmediate(ctx context.Context, queries *db.Queries, tenantDBID pgtype.UUID, tenantID, appID, appSecret, subscriptionID string, logger *slog.Logger) error {
	// Create Azure credential
	cred, err := azure.GetAzureCredential(tenantID, appID, appSecret)
	if err != nil {
		return fmt.Errorf("failed to create Azure credential: %w", err)
	}

	// Create subscriptions client
	client, err := azure.NewSubscriptionClient(cred)
	if err != nil {
		return fmt.Errorf("failed to create Azure subscriptions client: %w", err)
	}

	// Get subscription status
	subResp, err := client.Get(ctx, subscriptionID, nil)
	if err != nil {
		logger.Error("Failed to fetch subscription status immediately", "subscriptionID", subscriptionID, "error", err)
		// Set status to "Unknown" if we can't fetch it
		err := queries.SetAzureSubscriptionStatus(ctx, db.SetAzureSubscriptionStatusParams{
			ID:                 tenantDBID,
			AccountCloudStatus: pgtype.Text{String: "Unknown", Valid: true},
		})
		return err
	}

	status := "Disabled" // default fallback
	if subResp.State != nil {
		status = string(*subResp.State)
	}

	// Update the status in database
	err = queries.SetAzureSubscriptionStatus(ctx, db.SetAzureSubscriptionStatusParams{
		ID:                 tenantDBID,
		AccountCloudStatus: pgtype.Text{String: status, Valid: true},
	})
	if err != nil {
		return fmt.Errorf("failed to update subscription status: %w", err)
	}

	logger.Info("Immediate Azure subscription status check completed", "tenant_id", tenantID, "status", status)
	return nil
}

func validateAzureCredentials(ctx context.Context, tenantID, clientID, clientSecret, subscriptionID string) (credErr error, otherErr error) {
	cred, err := azure.GetAzureCredential(tenantID, clientID, clientSecret)
	if err != nil {
		credErr = fmt.Errorf("failed to create Azure credential: %w", err)
		return
	}

	client, err := azure.NewSubscriptionClient(cred)
	if err != nil {
		otherErr = fmt.Errorf("failed to create Azure subscription client: %w", err)
		return
	}

	_, err = client.Get(ctx, subscriptionID, nil)
	if err != nil {
		// First check if it's an authentication failure
		var authErr *azidentity.AuthenticationFailedError
		if errors.As(err, &authErr) {
			credErr = fmt.Errorf("authentication failed: %w", authErr)
			return
		}

		// Then check for Azure REST API errors
		var respErr *azcore.ResponseError
		if errors.As(err, &respErr) {
			if respErr.StatusCode == 401 || respErr.StatusCode == 403 ||
				strings.Contains(respErr.Error(), "invalid_tenant") ||
				strings.Contains(respErr.Error(), "AADSTS") {
				credErr = fmt.Errorf("authorization failed: %w", err)
			} else {
				otherErr = fmt.Errorf("azure API error: %w", err)
			}
			return
		}

		// Unknown / network / unexpected error
		otherErr = fmt.Errorf("unexpected error during subscription validation: %w", err)
		return
	}

	return nil, nil
}
