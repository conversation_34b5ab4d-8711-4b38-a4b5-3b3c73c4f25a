package accounts

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/iam"
	"github.com/aws/aws-sdk-go-v2/service/organizations"
	"github.com/aws/aws-sdk-go-v2/service/sts"
	"github.com/charmbracelet/keygen"
	"github.com/jackc/pgx/v5/pgtype"
	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments/keys"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/aws"
	scheduler "gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/scheduler"
)

func PublishAWSAccountCreationMessage(channel *amqp.Channel, engagementIDString string, secretKey string, awsRootEmail string, awsRootRegion string, logger *slog.Logger, dbQueries *db.Queries, nickname string, createdBy string) error {
	payload := map[string]string{
		"engagement_id":   engagementIDString,
		"aws_root_email":  awsRootEmail,
		"aws_root_region": awsRootRegion,
		"secret_key":      secretKey,
		"nickname":        nickname,
		"created_by":      createdBy,
	}

	body, err := json.Marshal(payload)
	if err != nil {
		return fmt.Errorf("failed to marshal AWS account creation message: %w", err)
	}

	err = channel.PublishWithContext(context.Background(),
		"",                     // exchange
		"aws-account-create-q", // routing key / queue name
		false, false,
		amqp.Publishing{
			ContentType: "application/json",
			Body:        body,
		},
	)
	if err != nil {
		return fmt.Errorf("Error publishing message: %v\n", err)
	} else {
		fmt.Println("Message successfully published to RabbitMQ")
	}
	return nil
}

func StartAWSAccountWorker(channel *amqp.Channel, logger *slog.Logger, dbQueries *db.Queries, msgs <-chan amqp.Delivery) error {
	logger.Info("Starting AWS Account Worker...")

	// Start a goroutine to process messages concurrently
	go func() {
		for d := range msgs {
			var payload struct {
				EngagementID  string `json:"engagement_id"`
				AWSRootEmail  string `json:"aws_root_email"`
				AWSRootRegion string `json:"aws_root_region"`
				SecretKey     string `json:"secret_key"`
				NickName      string `json:"nickname"`
				CreatedBy     string `json:"created_by"`
			}

			if err := json.Unmarshal(d.Body, &payload); err != nil {
				logger.Error("Failed to unmarshal AWS account creation message", "error", err.Error())
				_ = d.Nack(false, false)
				continue
			}

			logger := logger.With("engagement_id", payload.EngagementID)

			engagementUUID, err := converters.StringToPgTypeUUID(payload.EngagementID)
			if err != nil {
				logger.Error("Error converting created by ID", "error", err.Error())
				_ = d.Nack(false, false)
				continue
			}

			sshPassphrase, err := keys.GeneratePassword(32)
			if err != nil {
				logger.Error("Unable to generate a passphrase for the SSH key-pair of Engagement", "error", err.Error())
				_ = d.Nack(false, false)
				continue
			}

			encryptedSshPassphrase, salt, err := keys.EncryptSecret([]byte(sshPassphrase), payload.SecretKey)
			if err != nil {
				logger.Error("Unable to encrypt SSH key-pair passphrase of Engagement", "error", err.Error())
				_ = d.Nack(false, false)
				continue
			}

			kp, err := keygen.New("path", keygen.WithPassphrase(sshPassphrase), keygen.WithKeyType(keygen.RSA))
			if err != nil {
				logger.Error("Unable to create SSH key-pair of Engagement", "error", err.Error())
				_ = d.Nack(false, false)
				continue
			}

			createdByUUID, err := converters.StringToPgTypeUUID(payload.CreatedBy)

			if err != nil {
				logger.Error("Error converting created by ID", "error", err.Error())
				_ = d.Nack(false, false)
				continue
			}

			dbAccount, err := dbQueries.AddAWSAccount(context.Background(), db.AddAWSAccountParams{
				EngagementID:          *engagementUUID,
				PolicyID:              encryptedSshPassphrase,
				StatusID:              salt,
				SshKeyPublic:          kp.AuthorizedKey(),
				AccountCreationStatus: converters.StringToPgTypeText("PENDING"),
				Nickname:              payload.NickName,
				CreatedBy:             *createdByUUID,
			})

			if err != nil {
				logger.Error("Error saving aws account data to the db", "error", err.Error())
				_ = d.Nack(false, false)
				continue
			}
			dbAccountID, err := converters.PgTypeUUIDToString(dbAccount.ID)

			if err != nil {
				logger.Error("Error converting account id to string", "error", err.Error())
				_ = d.Nack(false, false)
				continue
			}

			accountName := dbAccountID

			sm, err := keys.NewSecretsManager(payload.AWSRootRegion)
			if err != nil {
				logger.Error("Error initializing SecretsManager", "error", err.Error())
				_ = dbQueries.SetAWSAccountStatusToError(context.Background(), db.SetAWSAccountStatusToErrorParams{
					ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
					ID:           dbAccount.ID,
				})
				_ = d.Nack(false, false)
				continue
			}

			logger = logger.With(
				"engagement_id", payload.EngagementID)

			cfg, err := config.LoadDefaultConfig(context.TODO())
			if err != nil {
				logger.Error("Unable to load AWS config", "error", err.Error())
				_ = dbQueries.SetAWSAccountStatusToError(context.Background(), db.SetAWSAccountStatusToErrorParams{
					ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
					ID:           dbAccount.ID,
				})
				_ = d.Nack(false, false)
				continue
			}
			ctx := context.TODO()

			orgClient := organizations.NewFromConfig(cfg)
			stsClient := sts.NewFromConfig(cfg)

			accountEmail, err := aws.AddEmailAlias(payload.AWSRootEmail, *accountName)
			if err != nil {
				logger.Error("Error adding email alias", "error", err.Error())
				_ = dbQueries.SetAWSAccountStatusToError(context.Background(), db.SetAWSAccountStatusToErrorParams{
					ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
					ID:           dbAccount.ID,
				})
				_ = d.Nack(false, false)
				continue
			}

			roleName := "OrganizationAccountAccessRole"
			adminUsername := "AdminUser"
			ouID := ""
			scpID := ""

			logger = logger.With(
				"role_name", roleName,
				"admin_username", adminUsername,
				"account_name", accountName,
				"account_email", accountEmail,
			)

			status, err := dbQueries.GetEngagementStatus(context.Background(), *engagementUUID)
			if err != nil {
				logger.Error("Error getting engagement status", "error", err.Error())
				_ = d.Nack(false, false)
				continue
			}

			// Start a goroutine to create a new AWS Account
			go func() {
				// Step 1: Create a new AWS account
				accountID, err := aws.CreateAWSAccount(ctx, orgClient, dbQueries, *accountName, accountEmail, roleName, ouID, scpID)
				if err != nil {
					logger.Error("Unable to create AWS account",
						"error", err.Error())
					if status != "SUCCESS" {
						_ = dbQueries.SetEngagementStatusToError(context.Background(), db.SetEngagementStatusToErrorParams{
							ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
							ID:           *engagementUUID,
						})
					}
					_ = dbQueries.SetAWSAccountStatusToError(context.Background(), db.SetAWSAccountStatusToErrorParams{
						ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
						ID:           dbAccount.ID,
					})
					return
				}

				params := db.SetAWSAccountIdParams{
					CloudAccountID: converters.StringToPgTypeText(accountID),
					ID:             dbAccount.ID,
				}

				err = dbQueries.SetAWSAccountId(ctx, params)

				if err != nil {
					logger.Error("failed to update DB with AWS account id",
						"error", err.Error())
					if status != "SUCCESS" {
						_ = dbQueries.SetEngagementStatusToError(context.Background(), db.SetEngagementStatusToErrorParams{
							ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
							ID:           *engagementUUID,
						})
					}
					_ = dbQueries.SetAWSAccountStatusToError(context.Background(), db.SetAWSAccountStatusToErrorParams{
						ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
						ID:           dbAccount.ID,
					})
					return
				}

				logger = logger.With(
					"account_id", accountID,
				)
				// Step 2: Assume a role in the new account
				roleOutput, err := aws.AssumeRole(ctx, stsClient, accountID, roleName)
				if err != nil {
					logger.Error("Unable to assume role",
						"error", err.Error())
					if status != "SUCCESS" {
						_ = dbQueries.SetEngagementStatusToError(context.Background(), db.SetEngagementStatusToErrorParams{
							ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
							ID:           *engagementUUID,
						})
					}
					_ = dbQueries.SetAWSAccountStatusToError(context.Background(), db.SetAWSAccountStatusToErrorParams{
						ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
						ID:           dbAccount.ID,
					})
					return
				}

				// Step 3: Create an admin IAM user in the new account
				iamClient := iam.NewFromConfig(cfg, func(o *iam.Options) {
					o.Credentials = credentials.NewStaticCredentialsProvider(
						*roleOutput.Credentials.AccessKeyId,     // Temporary Access Key ID
						*roleOutput.Credentials.SecretAccessKey, // Temporary Secret Access Key
						*roleOutput.Credentials.SessionToken,    // Temporary Session Token
					)
				})

				adminUserData, err := aws.CreateIAMAdmin(ctx, iamClient, adminUsername)
				if err != nil {
					logger.Error("Unable to create admin user",
						"error", err.Error())
					if status != "SUCCESS" {
						_ = dbQueries.SetEngagementStatusToError(context.Background(), db.SetEngagementStatusToErrorParams{
							ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
							ID:           *engagementUUID,
						})
					}
					_ = dbQueries.SetAWSAccountStatusToError(context.Background(), db.SetAWSAccountStatusToErrorParams{
						ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
						ID:           dbAccount.ID,
					})
					return
				}

				logger.Info("Created admin IAM user")

				// Prepare secret data
				secretData := map[string]string{
					"ssh_private_key":   string(kp.RawProtectedPrivateKey()),
					"admin_username":    adminUserData["admin_username"],
					"admin_password":    adminUserData["admin_password"],
					"access_key_id":     adminUserData["access_key_id"],
					"access_key_secret": adminUserData["access_key_secret"],
				}

				_, err = sm.CreateSecret(*accountName, secretData)
				if err != nil {
					logger.Error("Error creating secret in Secrets Manager",
						"error", err.Error())
					if status != "SUCCESS" {
						_ = dbQueries.SetEngagementStatusToError(context.Background(), db.SetEngagementStatusToErrorParams{
							ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
							ID:           *engagementUUID,
						})
					}
					_ = dbQueries.SetAWSAccountStatusToError(context.Background(), db.SetAWSAccountStatusToErrorParams{
						ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
						ID:           dbAccount.ID,
					})
					return
				}

				logger.Info("Stored admin credentials to Secrets Manager")

				err = dbQueries.SetEngagementStatusToSuccess(context.Background(), *engagementUUID)
				if err != nil {
					logger.Error("Error updating Engagement status")
					if status != "SUCCESS" {
						_ = dbQueries.SetEngagementStatusToError(context.Background(), db.SetEngagementStatusToErrorParams{
							ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
							ID:           *engagementUUID,
						})
					}
					return
				}
				err = dbQueries.SetAWSAccountStatusToSuccess(context.Background(), dbAccount.ID)
				if err != nil {
					logger.Error("Error updating AWS account status", "err", err.Error())
				}
				logger.Info("AWS account creation complete")
				// trigger account sttaus sync to pull the status in cloud for the new account
				err = scheduler.PublishAccountSyncTask(context.Background(), channel, "aws-sync-queue")
				if err != nil {
					logger.Error("Failed to publish AWS sync task after new account added", "error", err)
				} else {
					logger.Info("Published AWS sync task after new account added")
				}
			}()

			_ = d.Ack(false)
		}
	}()
	return nil
}
