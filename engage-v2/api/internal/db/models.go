// Code generated by sqlc. DO NOT EDIT.
// versions:
//   sqlc v1.27.0

package db

import (
	"database/sql/driver"
	"fmt"
	"net/netip"

	"github.com/jackc/pgx/v5/pgtype"
)

type CiStateEnum string

const (
	CiStateEnumPending      CiStateEnum = "pending"
	CiStateEnumRunning      CiStateEnum = "running"
	CiStateEnumStopping     CiStateEnum = "stopping"
	CiStateEnumStopped      CiStateEnum = "stopped"
	CiStateEnumShuttingDown CiStateEnum = "shutting-down"
	CiStateEnumTerminated   CiStateEnum = "terminated"
	CiStateEnumError        CiStateEnum = "error"
	CiStateEnumNew          CiStateEnum = "new"
)

func (e *CiStateEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = CiStateEnum(s)
	case string:
		*e = CiStateEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for CiStateEnum: %T", src)
	}
	return nil
}

type NullCiStateEnum struct {
	CiStateEnum CiStateEnum `json:"ci_state_enum"`
	Valid       bool        `json:"valid"` // Valid is true if CiStateEnum is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullCiStateEnum) Scan(value interface{}) error {
	if value == nil {
		ns.CiStateEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.CiStateEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullCiStateEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.CiStateEnum), nil
}

type DeploymentStatusEnum string

const (
	DeploymentStatusEnumPENDING    DeploymentStatusEnum = "PENDING"
	DeploymentStatusEnumINPROGRESS DeploymentStatusEnum = "IN-PROGRESS"
	DeploymentStatusEnumSUCCESS    DeploymentStatusEnum = "SUCCESS"
	DeploymentStatusEnumWARNING    DeploymentStatusEnum = "WARNING"
	DeploymentStatusEnumERROR      DeploymentStatusEnum = "ERROR"
)

func (e *DeploymentStatusEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = DeploymentStatusEnum(s)
	case string:
		*e = DeploymentStatusEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for DeploymentStatusEnum: %T", src)
	}
	return nil
}

type NullDeploymentStatusEnum struct {
	DeploymentStatusEnum DeploymentStatusEnum `json:"deployment_status_enum"`
	Valid                bool                 `json:"valid"` // Valid is true if DeploymentStatusEnum is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullDeploymentStatusEnum) Scan(value interface{}) error {
	if value == nil {
		ns.DeploymentStatusEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.DeploymentStatusEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullDeploymentStatusEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.DeploymentStatusEnum), nil
}

type DomainStatusEnum string

const (
	DomainStatusEnumUNASSIGNED DomainStatusEnum = "UNASSIGNED"
	DomainStatusEnumASSIGNED   DomainStatusEnum = "ASSIGNED"
	DomainStatusEnumQUARANTINE DomainStatusEnum = "QUARANTINE"
	DomainStatusEnumBURNED     DomainStatusEnum = "BURNED"
	DomainStatusEnumEXPIRED    DomainStatusEnum = "EXPIRED"
)

func (e *DomainStatusEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = DomainStatusEnum(s)
	case string:
		*e = DomainStatusEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for DomainStatusEnum: %T", src)
	}
	return nil
}

type NullDomainStatusEnum struct {
	DomainStatusEnum DomainStatusEnum `json:"domain_status_enum"`
	Valid            bool             `json:"valid"` // Valid is true if DomainStatusEnum is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullDomainStatusEnum) Scan(value interface{}) error {
	if value == nil {
		ns.DomainStatusEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.DomainStatusEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullDomainStatusEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.DomainStatusEnum), nil
}

type EngagementStatusEnum string

const (
	EngagementStatusEnumPENDING EngagementStatusEnum = "PENDING"
	EngagementStatusEnumSUCCESS EngagementStatusEnum = "SUCCESS"
	EngagementStatusEnumERROR   EngagementStatusEnum = "ERROR"
)

func (e *EngagementStatusEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = EngagementStatusEnum(s)
	case string:
		*e = EngagementStatusEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for EngagementStatusEnum: %T", src)
	}
	return nil
}

type NullEngagementStatusEnum struct {
	EngagementStatusEnum EngagementStatusEnum `json:"engagement_status_enum"`
	Valid                bool                 `json:"valid"` // Valid is true if EngagementStatusEnum is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullEngagementStatusEnum) Scan(value interface{}) error {
	if value == nil {
		ns.EngagementStatusEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.EngagementStatusEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullEngagementStatusEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.EngagementStatusEnum), nil
}

type LogsAssignmentsStatusEnum string

const (
	LogsAssignmentsStatusEnumSUCCESS LogsAssignmentsStatusEnum = "SUCCESS"
	LogsAssignmentsStatusEnumERROR   LogsAssignmentsStatusEnum = "ERROR"
)

func (e *LogsAssignmentsStatusEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = LogsAssignmentsStatusEnum(s)
	case string:
		*e = LogsAssignmentsStatusEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for LogsAssignmentsStatusEnum: %T", src)
	}
	return nil
}

type NullLogsAssignmentsStatusEnum struct {
	LogsAssignmentsStatusEnum LogsAssignmentsStatusEnum `json:"logs_assignments_status_enum"`
	Valid                     bool                      `json:"valid"` // Valid is true if LogsAssignmentsStatusEnum is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullLogsAssignmentsStatusEnum) Scan(value interface{}) error {
	if value == nil {
		ns.LogsAssignmentsStatusEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.LogsAssignmentsStatusEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullLogsAssignmentsStatusEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.LogsAssignmentsStatusEnum), nil
}

type LogsAssignmentsTypeEnum string

const (
	LogsAssignmentsTypeEnumSSHCONNECTION  LogsAssignmentsTypeEnum = "SSH_CONNECTION"
	LogsAssignmentsTypeEnumUSERASSIGNMENT LogsAssignmentsTypeEnum = "USER_ASSIGNMENT"
	LogsAssignmentsTypeEnumUSERREMOVAL    LogsAssignmentsTypeEnum = "USER_REMOVAL"
)

func (e *LogsAssignmentsTypeEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = LogsAssignmentsTypeEnum(s)
	case string:
		*e = LogsAssignmentsTypeEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for LogsAssignmentsTypeEnum: %T", src)
	}
	return nil
}

type NullLogsAssignmentsTypeEnum struct {
	LogsAssignmentsTypeEnum LogsAssignmentsTypeEnum `json:"logs_assignments_type_enum"`
	Valid                   bool                    `json:"valid"` // Valid is true if LogsAssignmentsTypeEnum is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullLogsAssignmentsTypeEnum) Scan(value interface{}) error {
	if value == nil {
		ns.LogsAssignmentsTypeEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.LogsAssignmentsTypeEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullLogsAssignmentsTypeEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.LogsAssignmentsTypeEnum), nil
}

type LogsNodesTypeEnum string

const (
	LogsNodesTypeEnumNODECREATION                 LogsNodesTypeEnum = "NODE_CREATION"
	LogsNodesTypeEnumNODEUPDATE                   LogsNodesTypeEnum = "NODE_UPDATE"
	LogsNodesTypeEnumNODEDELETION                 LogsNodesTypeEnum = "NODE_DELETION"
	LogsNodesTypeEnumINBOUNDRELATIONSHIPCREATION  LogsNodesTypeEnum = "INBOUND_RELATIONSHIP_CREATION"
	LogsNodesTypeEnumINBOUNDRELATIONSHIPDELETION  LogsNodesTypeEnum = "INBOUND_RELATIONSHIP_DELETION"
	LogsNodesTypeEnumOUTBOUNDRELATIONSHIPCREATION LogsNodesTypeEnum = "OUTBOUND_RELATIONSHIP_CREATION"
	LogsNodesTypeEnumOUTBOUNDRELATIONSHIPDELETION LogsNodesTypeEnum = "OUTBOUND_RELATIONSHIP_DELETION"
	LogsNodesTypeEnumDOMAINASSIGNED               LogsNodesTypeEnum = "DOMAIN_ASSIGNED"
	LogsNodesTypeEnumDOMAINBURNED                 LogsNodesTypeEnum = "DOMAIN_BURNED"
	LogsNodesTypeEnumDOMAINUNASSIGNED             LogsNodesTypeEnum = "DOMAIN_UNASSIGNED"
	LogsNodesTypeEnumDOMAINQUARANTINED            LogsNodesTypeEnum = "DOMAIN_QUARANTINED"
	LogsNodesTypeEnumDOMAINEXPIRED                LogsNodesTypeEnum = "DOMAIN_EXPIRED"
)

func (e *LogsNodesTypeEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = LogsNodesTypeEnum(s)
	case string:
		*e = LogsNodesTypeEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for LogsNodesTypeEnum: %T", src)
	}
	return nil
}

type NullLogsNodesTypeEnum struct {
	LogsNodesTypeEnum LogsNodesTypeEnum `json:"logs_nodes_type_enum"`
	Valid             bool              `json:"valid"` // Valid is true if LogsNodesTypeEnum is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullLogsNodesTypeEnum) Scan(value interface{}) error {
	if value == nil {
		ns.LogsNodesTypeEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.LogsNodesTypeEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullLogsNodesTypeEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.LogsNodesTypeEnum), nil
}

type NodeTypeEnum string

const (
	NodeTypeEnumCLOUDINSTANCE NodeTypeEnum = "CLOUD_INSTANCE"
	NodeTypeEnumEMAILADDRESS  NodeTypeEnum = "EMAIL_ADDRESS"
	NodeTypeEnumHOST          NodeTypeEnum = "HOST"
	NodeTypeEnumPERSON        NodeTypeEnum = "PERSON"
	NodeTypeEnumURL           NodeTypeEnum = "URL"
)

func (e *NodeTypeEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = NodeTypeEnum(s)
	case string:
		*e = NodeTypeEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for NodeTypeEnum: %T", src)
	}
	return nil
}

type NullNodeTypeEnum struct {
	NodeTypeEnum NodeTypeEnum `json:"node_type_enum"`
	Valid        bool         `json:"valid"` // Valid is true if NodeTypeEnum is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullNodeTypeEnum) Scan(value interface{}) error {
	if value == nil {
		ns.NodeTypeEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.NodeTypeEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullNodeTypeEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.NodeTypeEnum), nil
}

type ProviderEnum string

const (
	ProviderEnumAWS   ProviderEnum = "AWS"
	ProviderEnumAZURE ProviderEnum = "AZURE"
	ProviderEnumGCP   ProviderEnum = "GCP"
)

func (e *ProviderEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ProviderEnum(s)
	case string:
		*e = ProviderEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for ProviderEnum: %T", src)
	}
	return nil
}

type NullProviderEnum struct {
	ProviderEnum ProviderEnum `json:"provider_enum"`
	Valid        bool         `json:"valid"` // Valid is true if ProviderEnum is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullProviderEnum) Scan(value interface{}) error {
	if value == nil {
		ns.ProviderEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ProviderEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullProviderEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ProviderEnum), nil
}

type ScriptTypeEnum string

const (
	ScriptTypeEnumADMIN    ScriptTypeEnum = "ADMIN"
	ScriptTypeEnumSTANDARD ScriptTypeEnum = "STANDARD"
)

func (e *ScriptTypeEnum) Scan(src interface{}) error {
	switch s := src.(type) {
	case []byte:
		*e = ScriptTypeEnum(s)
	case string:
		*e = ScriptTypeEnum(s)
	default:
		return fmt.Errorf("unsupported scan type for ScriptTypeEnum: %T", src)
	}
	return nil
}

type NullScriptTypeEnum struct {
	ScriptTypeEnum ScriptTypeEnum `json:"script_type_enum"`
	Valid          bool           `json:"valid"` // Valid is true if ScriptTypeEnum is not NULL
}

// Scan implements the Scanner interface.
func (ns *NullScriptTypeEnum) Scan(value interface{}) error {
	if value == nil {
		ns.ScriptTypeEnum, ns.Valid = "", false
		return nil
	}
	ns.Valid = true
	return ns.ScriptTypeEnum.Scan(value)
}

// Value implements the driver Valuer interface.
func (ns NullScriptTypeEnum) Value() (driver.Value, error) {
	if !ns.Valid {
		return nil, nil
	}
	return string(ns.ScriptTypeEnum), nil
}

type AwsAccount struct {
	ID                    pgtype.UUID      `json:"id"`
	EngagementID          pgtype.UUID      `json:"engagement_id"`
	CloudAccountID        pgtype.Text      `json:"cloud_account_id"`
	PolicyID              string           `json:"policy_id"`
	StatusID              string           `json:"status_id"`
	SshKeyPublic          string           `json:"ssh_key_public"`
	CreatedAt             pgtype.Timestamp `json:"created_at"`
	ErrorMessage          pgtype.Text      `json:"error_message"`
	AccountCreationStatus pgtype.Text      `json:"account_creation_status"`
	AccountCloudStatus    pgtype.Text      `json:"account_cloud_status"`
	Nickname              string           `json:"nickname"`
	CreatedBy             pgtype.UUID      `json:"created_by"`
}

type AzureTenant struct {
	ID                 pgtype.UUID      `json:"id"`
	EngagementID       pgtype.UUID      `json:"engagement_id"`
	TenantID           string           `json:"tenant_id"`
	SubscriptionID     pgtype.Text      `json:"subscription_id"`
	ErrorMessage       pgtype.Text      `json:"error_message"`
	CreatedAt          pgtype.Timestamp `json:"created_at"`
	CreationStatus     pgtype.Text      `json:"creation_status"`
	AccountCloudStatus pgtype.Text      `json:"account_cloud_status"`
	PolicyID           string           `json:"policy_id"`
	StatusID           string           `json:"status_id"`
	SshKeyPublic       string           `json:"ssh_key_public"`
	SecretsSaved       bool             `json:"secrets_saved"`
}

type Client struct {
	ID   pgtype.UUID `json:"id"`
	Name string      `json:"name"`
}

type Deployment struct {
	ID              pgtype.UUID          `json:"id"`
	TerraformModule string               `json:"terraform_module"`
	Status          DeploymentStatusEnum `json:"status"`
	CreatedAt       pgtype.Timestamp     `json:"created_at"`
	ErrorMessage    pgtype.Text          `json:"error_message"`
	UserID          pgtype.UUID          `json:"user_id"`
	NodeID          pgtype.UUID          `json:"node_id"`
	EngagementID    pgtype.UUID          `json:"engagement_id"`
}

type Domain struct {
	ID           pgtype.UUID          `json:"id"`
	Url          string               `json:"url"`
	Registrar    pgtype.Text          `json:"registrar"`
	PurchaseDate pgtype.Date          `json:"purchase_date"`
	RenewalDate  pgtype.Date          `json:"renewal_date"`
	Status       NullDomainStatusEnum `json:"status"`
	Engagement   pgtype.Text          `json:"engagement"`
	Client       pgtype.Text          `json:"client"`
	Age          pgtype.Int4          `json:"age"`
	CreatedAt    pgtype.Timestamp     `json:"created_at"`
}

type Engagement struct {
	ID           pgtype.UUID          `json:"id"`
	Title        string               `json:"title"`
	WbsCode      string               `json:"wbs_code"`
	IsActive     bool                 `json:"is_active"`
	ClientID     pgtype.UUID          `json:"client_id"`
	CreatedAt    pgtype.Timestamp     `json:"created_at"`
	UpdatedAt    pgtype.Timestamp     `json:"updated_at"`
	Status       EngagementStatusEnum `json:"status"`
	ErrorMessage pgtype.Text          `json:"error_message"`
}

type EngagementsUser struct {
	EngagementID pgtype.UUID `json:"engagement_id"`
	UserID       pgtype.UUID `json:"user_id"`
}

type InstanceSizeMapping struct {
	ID           int64        `json:"id"`
	Provider     ProviderEnum `json:"provider"`
	SizeAlias    string       `json:"size_alias"`
	Priority     int32        `json:"priority"`
	InstanceType string       `json:"instance_type"`
}

type LogsAssignment struct {
	ID                     pgtype.UUID               `json:"id"`
	Message                string                    `json:"message"`
	Type                   LogsAssignmentsTypeEnum   `json:"type"`
	Status                 LogsAssignmentsStatusEnum `json:"status"`
	UserID                 pgtype.UUID               `json:"user_id"`
	UserCustomUsernameUsed pgtype.Text               `json:"user_custom_username_used"`
	NodeID                 pgtype.UUID               `json:"node_id"`
	CreatedAt              pgtype.Timestamp          `json:"created_at"`
}

type LogsNode struct {
	ID        int64             `json:"id"`
	Message   string            `json:"message"`
	Type      LogsNodesTypeEnum `json:"type"`
	UserID    pgtype.UUID       `json:"user_id"`
	NodeID    pgtype.UUID       `json:"node_id"`
	CreatedAt pgtype.Timestamp  `json:"created_at"`
}

type Node struct {
	ID          pgtype.UUID  `json:"id"`
	NodeType    NodeTypeEnum `json:"node_type"`
	Name        string       `json:"name"`
	NodeGroupID pgtype.UUID  `json:"node_group_id"`
	IsDeleted   bool         `json:"is_deleted"`
}

type NodeGroup struct {
	ID           pgtype.UUID      `json:"id"`
	Name         string           `json:"name"`
	IsActive     bool             `json:"is_active"`
	CreatedAt    pgtype.Timestamp `json:"created_at"`
	UpdatedAt    pgtype.Timestamp `json:"updated_at"`
	EngagementID pgtype.UUID      `json:"engagement_id"`
}

type NodeRelationship struct {
	ID           int64       `json:"id"`
	SourceNodeID pgtype.UUID `json:"source_node_id"`
	TargetNodeID pgtype.UUID `json:"target_node_id"`
}

type NodeTypeCloudInstance struct {
	Provider               ProviderEnum    `json:"provider"`
	Region                 string          `json:"region"`
	OperatingSystemImageID string          `json:"operating_system_image_id"`
	InstanceType           string          `json:"instance_type"`
	Name                   string          `json:"name"`
	OpenPorts              []int32         `json:"open_ports"`
	PublicIpv4Address      *netip.Addr     `json:"public_ipv4_address"`
	NodeID                 pgtype.UUID     `json:"node_id"`
	CloudInstanceState     NullCiStateEnum `json:"cloud_instance_state"`
	CloudInstanceID        pgtype.Text     `json:"cloud_instance_id"`
	AwsAccountID           pgtype.UUID     `json:"aws_account_id"`
	AzureTenantID          pgtype.UUID     `json:"azure_tenant_id"`
}

type NodeTypeEmailAddress struct {
	EmailAddress string      `json:"email_address"`
	NodeID       pgtype.UUID `json:"node_id"`
}

type NodeTypeHost struct {
	Name             string       `json:"name"`
	IpAddresses      []netip.Addr `json:"ip_addresses"`
	AlternativeNames []string     `json:"alternative_names"`
	NodeID           pgtype.UUID  `json:"node_id"`
}

type NodeTypePerson struct {
	FirstName string      `json:"first_name"`
	LastName  pgtype.Text `json:"last_name"`
	Email     pgtype.Text `json:"email"`
	Company   pgtype.Text `json:"company"`
	Title     pgtype.Text `json:"title"`
	NodeID    pgtype.UUID `json:"node_id"`
}

type NodeTypeUrl struct {
	Url    string      `json:"url"`
	NodeID pgtype.UUID `json:"node_id"`
}

type PrioritizedRegion struct {
	ID   int32       `json:"id"`
	Name pgtype.Text `json:"name"`
}

type Script struct {
	ID          pgtype.UUID      `json:"id"`
	Name        string           `json:"name"`
	Description string           `json:"description"`
	Content     string           `json:"content"`
	ScriptType  ScriptTypeEnum   `json:"script_type"`
	CreatedAt   pgtype.Timestamp `json:"created_at"`
	UpdatedAt   pgtype.Timestamp `json:"updated_at"`
	UserID      pgtype.UUID      `json:"user_id"`
}

type TerraformTemplate struct {
	ID      pgtype.UUID `json:"id"`
	Name    string      `json:"name"`
	Content string      `json:"content"`
}

type User struct {
	ID                 pgtype.UUID      `json:"id"`
	Username           string           `json:"username"`
	CustomUsername     pgtype.Text      `json:"custom_username"`
	FullName           pgtype.Text      `json:"full_name"`
	AppRole            pgtype.Text      `json:"app_role"`
	SshKey             pgtype.Text      `json:"ssh_key"`
	SshKeyLabel        pgtype.Text      `json:"ssh_key_label"`
	SshKeyCreationDate pgtype.Timestamp `json:"ssh_key_creation_date"`
	IsInactive         pgtype.Bool      `json:"is_inactive"`
}
