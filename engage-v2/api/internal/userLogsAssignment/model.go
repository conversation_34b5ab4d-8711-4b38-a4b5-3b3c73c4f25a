package logsAssignments

import (
	"context"
	"fmt"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

type UserLogsAssignment struct {
	ID                     string                       `json:"id" format:"uuid"`
	Message                string                       `json:"message"`
	Type                   db.LogsAssignmentsTypeEnum   `json:"type"`
	Status                 db.LogsAssignmentsStatusEnum `json:"status"`
	UserID                 string                       `json:"user_id" format:"uuid"`
	UserCustomUsernameUsed string                       `json:"user_custom_username_used"`
	NodeID                 string                       `json:"node_id"`
	Name                   string                       `json:"name"`
	CreatedAt              time.Time                    `json:"created_at"`
}

// GetAssigmentLogs retrieves all Assignment Logs at their associated Engagement
func GetAssigmentLogs(queries *db.Queries, engagementID pgtype.UUID) ([]UserLogsAssignment, error) {

	assigmentLogsDB, err := queries.GetAssignmentLogs(context.Background(), engagementID)
	if err != nil {
		return nil, fmt.Errorf("failed to fetch assignment logs by engagement IDs: %w", err)
	}

	var assignmentLogs []UserLogsAssignment
	for _, dbAssignmentLog := range assigmentLogsDB {
		assigmentLogIDString, _ := converters.PgTypeUUIDToString(dbAssignmentLog.ID)
		deploymentNodeIDString, _ := converters.PgTypeUUIDToString(dbAssignmentLog.NodeID)
		userIDString, _ := converters.PgTypeUUIDToString(dbAssignmentLog.UserID)

		assignmentLog := UserLogsAssignment{
			ID:                     *assigmentLogIDString,
			Status:                 dbAssignmentLog.Status,
			CreatedAt:              dbAssignmentLog.CreatedAt.Time,
			NodeID:                 *deploymentNodeIDString,
			Message:                dbAssignmentLog.Message,
			Name:                   dbAssignmentLog.Name,
			UserID:                 *userIDString,
			UserCustomUsernameUsed: dbAssignmentLog.UserCustomUsernameUsed.String,
			Type:                   dbAssignmentLog.Type,
		}
		assignmentLogs = append(assignmentLogs, assignmentLog)
	}

	return assignmentLogs, nil

}

// GetAllAssignmentsLogs retrieves all Assignment Logs
func GetAllAssignmentsLogs(queries *db.Queries) ([]UserLogsAssignment, error) {

	assigmentLogsDB, err := queries.GetAllAssignmentsLogs(context.Background())
	if err != nil {
		return nil, fmt.Errorf("failed to fetch assignment logs: %w", err)
	}

	var assignmentLogs []UserLogsAssignment
	for _, dbAssignmentLog := range assigmentLogsDB {
		assigmentLogIDString, _ := converters.PgTypeUUIDToString(dbAssignmentLog.ID)
		deploymentNodeIDString, _ := converters.PgTypeUUIDToString(dbAssignmentLog.NodeID)
		userIDString, _ := converters.PgTypeUUIDToString(dbAssignmentLog.UserID)

		assignmentLog := UserLogsAssignment{
			ID:                     *assigmentLogIDString,
			Status:                 dbAssignmentLog.Status,
			CreatedAt:              dbAssignmentLog.CreatedAt.Time,
			NodeID:                 *deploymentNodeIDString,
			Message:                dbAssignmentLog.Message,
			Name:                   dbAssignmentLog.Name,
			UserID:                 *userIDString,
			UserCustomUsernameUsed: dbAssignmentLog.UserCustomUsernameUsed.String,
			Type:                   dbAssignmentLog.Type,
		}
		assignmentLogs = append(assignmentLogs, assignmentLog)
	}

	return assignmentLogs, nil

}
