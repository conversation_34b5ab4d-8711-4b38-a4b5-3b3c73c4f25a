package activitylogs

import (
	"context"

	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

func InsertLog(queries *db.Queries, message string, logType db.LogsNodesTypeEnum, userId pgtype.UUID, nodeId pgtype.UUID, timestamp pgtype.Timestamp) error {
	err := queries.InsertActivityLog(context.Background(),
		db.InsertActivityLogParams{Message: message,
			UserID:    userId,
			NodeID:    nodeId,
			CreatedAt: timestamp,
			Type:      logType})
	if err != nil {
		return err
	}
	return nil
}
