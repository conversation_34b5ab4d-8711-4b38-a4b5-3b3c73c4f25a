package keys

import (
	"context"
	"encoding/json"
	"fmt"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
)

// SecretsManager struct to represent a class-like abstraction for Secrets Manager operations
type SecretsManager struct {
	client *secretsmanager.Client
}

// NewSecretsManager constructor to create a new SecretsManager instance
func NewSecretsManager(region string) (*SecretsManager, error) {
	// Load AWS configuration with custom credentials
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithRegion(region))
	if err != nil {
		return nil, fmt.Errorf("unable to load SDK config, %v", err)
	}

	// Create a SecretsManager client
	client := secretsmanager.NewFromConfig(cfg)

	return &SecretsManager{client: client}, nil
}

// CreateSecret method to create a new secret in AWS Secrets Manager with multiple keys/values
func (sm *SecretsManager) CreateSecret(secretName string, secretData map[string]string) (string, error) {
	// Convert the map to a JSON string
	secretString, err := json.Marshal(secretData)
	if err != nil {
		return "", fmt.Errorf("unable to marshal secret data to JSON: %v", err)
	}

	// Define the input to create the secret
	input := &secretsmanager.CreateSecretInput{
		Name:         aws.String(secretName),
		SecretString: aws.String(string(secretString)),
	}

	// Call the CreateSecret API
	result, err := sm.client.CreateSecret(context.TODO(), input)
	if err != nil {
		return "", fmt.Errorf("unable to create secret: %v", err)
	}

	// Return the ARN of the created secret
	return *result.ARN, nil
}

// GetSecret method to retrieve a secret from AWS Secrets Manager
func (sm *SecretsManager) GetSecret(secretName string) (map[string]string, error) {
	// Define the input to retrieve the secret
	input := &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(secretName),
	}

	// Call the GetSecretValue API
	result, err := sm.client.GetSecretValue(context.TODO(), input)
	if err != nil {
		return nil, fmt.Errorf("unable to retrieve secret: %v", err)
	}

	// Unmarshal the secret string into a map
	var secretData map[string]string
	if err := json.Unmarshal([]byte(*result.SecretString), &secretData); err != nil {
		return nil, fmt.Errorf("unable to unmarshal secret string: %v", err)
	}

	// Return the secret data as a map
	return secretData, nil
}

// DeleteSecret method to delete a secret from AWS Secrets Manager
func (sm *SecretsManager) DeleteSecret(secretName string) (string, error) {
	// Define the input to delete the secret
	input := &secretsmanager.DeleteSecretInput{
		SecretId: aws.String(secretName),
	}

	// Call the DeleteSecret API
	result, err := sm.client.DeleteSecret(context.TODO(), input)
	if err != nil {
		return "", fmt.Errorf("unable to delete secret: %v", err)
	}

	// Return the ARN of the deleted secret
	return *result.ARN, nil
}
