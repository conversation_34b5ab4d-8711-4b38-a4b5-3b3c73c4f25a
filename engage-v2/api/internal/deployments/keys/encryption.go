package keys

import (
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"fmt"
	"golang.org/x/crypto/argon2"
)

// EncryptSecret encrypts the secret key with a derived key using AES-GCM.
func EncryptSecret(secretKey []byte, masterPassword string) (string, string, error) {
	// Generate a random salt
	salt := make([]byte, 16)
	if _, err := rand.Read(salt); err != nil {
		return "", "", err
	}

	// Derive a key using Argon2
	derivedKey := argon2.IDKey([]byte(masterPassword), salt, 1, 64*1024, 4, 32)

	// Create AES cipher block
	block, err := aes.NewCipher(derivedKey)
	if err != nil {
		return "", "", err
	}

	// Use AES-GCM for encryption
	aesGCM, err := cipher.NewGCM(block)
	if err != nil {
		return "", "", err
	}

	// Generate a random nonce
	nonce := make([]byte, aesGCM.NonceSize())
	if _, err := rand.Read(nonce); err != nil {
		return "", "", err
	}

	// Encrypt the secret key
	ciphertext := aesGCM.Seal(nil, nonce, secretKey, nil)

	// Combine nonce and ciphertext for storage
	finalCiphertext := append(nonce, ciphertext...)

	// Return the base64-encoded ciphertext and salt
	return base64.StdEncoding.EncodeToString(finalCiphertext), base64.StdEncoding.EncodeToString(salt), nil
}

// DecryptSecret decrypts the secret key using the master password and salt.
func DecryptSecret(encryptedSecret, saltBase64, masterPassword string) ([]byte, error) {
	// Decode the base64-encoded salt and encrypted secret
	salt, err := base64.StdEncoding.DecodeString(saltBase64)
	if err != nil {
		return nil, err
	}

	// Decode the encrypted secret
	ciphertext, err := base64.StdEncoding.DecodeString(encryptedSecret)
	if err != nil {
		return nil, err
	}

	// Derive the key using Argon2
	derivedKey := argon2.IDKey([]byte(masterPassword), salt, 1, 64*1024, 4, 32)

	// Create AES cipher block
	block, err := aes.NewCipher(derivedKey)
	if err != nil {
		return nil, err
	}

	// Use AES-GCM for decryption
	aesGCM, err := cipher.NewGCM(block)
	if err != nil {
		return nil, err
	}

	// Split the nonce and ciphertext
	nonceSize := aesGCM.NonceSize()
	if len(ciphertext) < nonceSize {
		return nil, fmt.Errorf("ciphertext too short")
	}
	nonce, ciphertext := ciphertext[:nonceSize], ciphertext[nonceSize:]

	// Decrypt the secret key
	secretKey, err := aesGCM.Open(nil, nonce, ciphertext, nil)
	if err != nil {
		return nil, err
	}

	return secretKey, nil
}
