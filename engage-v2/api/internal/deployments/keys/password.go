package keys

import (
	"crypto/rand"
	"fmt"
	"math/big"
)

const (
	lowercase = "abcdefghijklmnopqrstuvwxyz"
	uppercase = "ABCDEFGHIJKLMNOPQRSTUVWXYZ"
	numbers   = "0123456789"
	specials  = "!@#$%^&*()-_=+[]{}|;:<>,.?/"
	allChars  = lowercase + uppercase + numbers + specials
)

// GeneratePassword ensures the password includes at least one uppercase, one number, and one special character.
func GeneratePassword(length int) (string, error) {
	if length < 4 {
		return "", fmt.Errorf("password length must be at least 4 to include all required character types")
	}

	// Choose one character from each required set.
	password := []byte{
		chooseRandom<PERSON><PERSON>(uppercase),
		chooseRand<PERSON><PERSON><PERSON>(numbers),
		chooseRandom<PERSON>har(specials),
		chooseRandom<PERSON><PERSON>(lowercase),
	}

	// Fill the remaining characters randomly from all character sets.
	for i := 4; i < length; i++ {
		password = append(password, chooseRandom<PERSON>har(allChars))
	}

	// Shuffle the password to avoid predictable patterns.
	shuffle(password)

	return string(password), nil
}

// chooseRand<PERSON><PERSON><PERSON> selects a random character from a given set.
func chooseRandom<PERSON>har(charset string) byte {
	randomIndex, _ := rand.Int(rand.Reader, big.NewInt(int64(len(charset))))
	return charset[randomIndex.Int64()]
}

// shuffle randomly shuffles the characters in a byte slice.
func shuffle(chars []byte) {
	for i := len(chars) - 1; i > 0; i-- {
		j, _ := rand.Int(rand.Reader, big.NewInt(int64(i+1)))
		chars[i], chars[j.Int64()] = chars[j.Int64()], chars[i]
	}
}
