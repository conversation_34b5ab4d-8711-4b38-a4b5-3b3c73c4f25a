package keys

import (
	"bytes"
	"fmt"
	"math/rand"
	"time"

	"golang.org/x/crypto/ssh"
)

// SSHClient wraps SSH connection parameters and provides a method to connect with exponential backoff and jitter
type SSHClient struct {
	Address    string
	User       string
	PrivateKey string
	Passphrase string
	MaxRetries int
	BaseDelay  time.Duration
	client     *ssh.Client
}

// NewSSHClient initializes an SSHClient instance
func NewSSHClient(address, user, privateKey string, passphrase string, maxRetries int, baseDelay time.Duration) *SSHClient {

	return &SSHClient{
		Address:    address,
		User:       user,
		PrivateKey: privateKey,
		Passphrase: passphrase,
		MaxRetries: maxRetries,
		BaseDelay:  baseDelay,
	}
}

// Connect tries to establish an SSH connection with exponential backoff and jitter
func (s *SSHClient) Connect() error {
	var err error

	for i := 0; i < s.MaxRetries; i++ {
		s.client, err = s.sshDial()
		if err == nil {
			return nil
		}

		// Exponential backoff
		backoff := s.BaseDelay * (1 << i)

		// Jitter up to half of the backoff duration
		jitter := time.Duration(rand.Int63n(int64(backoff) / 2))
		sleepDuration := backoff + jitter

		fmt.Printf("SSH connection failed: %s. Retrying in %s...\n", err, sleepDuration)
		time.Sleep(sleepDuration)
	}
	return fmt.Errorf("failed to connect to SSH after %d retries: %v", s.MaxRetries, err)
}

// Close closes the SSH client connection
func (s *SSHClient) Close() error {
	if s.client != nil {
		return s.client.Close()
	}
	return nil
}

// sshDial dials the SSH server using the client's configuration
func (s *SSHClient) sshDial() (*ssh.Client, error) {
	// Parse the private key with passphrase
	signer, err := ssh.ParsePrivateKeyWithPassphrase([]byte(s.PrivateKey), []byte(s.Passphrase))
	if err != nil {
		return nil, fmt.Errorf("unable to parse private key: %v", err)
	}

	config := &ssh.ClientConfig{
		User: s.User,
		Auth: []ssh.AuthMethod{
			ssh.PublicKeys(signer),
		},
		HostKeyCallback: ssh.InsecureIgnoreHostKey(),
	}

	client, err := ssh.Dial("tcp", s.Address, config)
	if err != nil {
		return nil, fmt.Errorf("failed to dial: %v", err)
	}

	return client, nil
}

// AddUser creates a unix user to a remote server through SSH and adds their public SSH key
func (s *SSHClient) AddUser(username string, publicSshKey string, sudoersGroupName string) error {
	// Check if user exists
	_, err := s.runCommand(fmt.Sprintf("id %s", username))
	if err != nil { // User doesn't exist, create user directories from scratch
		// Add user
		_, err = s.runCommand(fmt.Sprintf("sudo /usr/sbin/useradd -s /bin/bash  %s --create-home", username))
		if err != nil {
			return fmt.Errorf("adding user %s failed: %s", username, err)
		}

		// in-group admin
		_, err = s.runCommand(fmt.Sprintf("echo '%s ALL=(ALL) NOPASSWD:ALL' | sudo tee -a /etc/sudoers.d/%s", username, sudoersGroupName))
		if err != nil {
			return fmt.Errorf("adding user %s to sudoers failed: %s", username, err)
		}
		_, err = s.runCommand(fmt.Sprintf("sudo chmod 400 /etc/sudoers.d/%s", sudoersGroupName))
		if err != nil {
			return fmt.Errorf("changing sudoers group %s permissions to 400 failed: %s", sudoersGroupName, err)
		}

		// push auth key set
		_, err = s.runCommand(fmt.Sprintf("sudo mkdir /home/<USER>/.ssh", username))
		if err != nil {
			return fmt.Errorf("creating user %s SSH directory failed: %s", username, err)
		}
		_, err = s.runCommand(fmt.Sprintf("echo -e '%s' | sudo tee -a /home/<USER>/.ssh/authorized_keys", publicSshKey, username))
		if err != nil {
			return fmt.Errorf("adding user's %s public SSH key %s failed: %s", username, publicSshKey, err)
		}
		_, err = s.runCommand(fmt.Sprintf("sudo chown -R %s:%s /home/<USER>/.ssh", username, username, username))
		if err != nil {
			return fmt.Errorf("changing ownership of SSH directory for user %s failed: %s", username, err)
		}
		_, err = s.runCommand(fmt.Sprintf("sudo chmod 400 /home/<USER>/.ssh/authorized_keys", username))
		if err != nil {
			return fmt.Errorf("changing permissions of `authorized_keys` file of user %s failed: %s", username, err)
		}
	} else { // User exists, just add their new public SSH key
		_, err = s.runCommand(fmt.Sprintf("echo -e '%s' | sudo tee -a /home/<USER>/.ssh/authorized_keys", publicSshKey, username))
		if err != nil {
			return fmt.Errorf("adding user's %s public SSH key %s failed: %s", username, publicSshKey, err)
		}
	}
	return nil
}

// RemoveUserSshKey removes a user's SSH key from the remote server but leaves their home directory otherwise intact
func (s *SSHClient) RemoveUserSshKey(username string) error {
	_, err := s.runCommand(fmt.Sprintf("sudo rm /home/<USER>/.ssh/authorized_keys", username))
	if err != nil {
		return fmt.Errorf("removing user's ssh key %s failed: %s", username, err)
	}
	return nil
}

// runCommand runs a command to a remote server through SSH
func (s *SSHClient) runCommand(cmd string) (string, error) {
	session, err := s.client.NewSession()
	if err != nil {
		return "", fmt.Errorf("failed to create session: %w", err)
	}
	defer session.Close()
	var stdoutBuf bytes.Buffer
	session.Stdout = &stdoutBuf
	if err := session.Run(cmd); err != nil {
		return "", fmt.Errorf("failed to run command: %v", err)
	}

	return stdoutBuf.String(), nil
}
