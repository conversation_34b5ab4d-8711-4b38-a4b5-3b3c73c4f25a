// Package aws providers provides implementation of AWS operations to dynamically retrieve AMIs was done using the AWS SDK (latest version v1.156 at the time of writing) in Go: https://aws.github.io/aws-sdk-go-v2/docs/
//
// More specifically the SDK call DescribeImages https://pkg.go.dev/github.com/aws/aws-sdk-go-v2/service/ec2#Client.DescribeImages is used to dynamically fetch information about images using string search.
//
// For the purposes of this spike and to produce a MVP, we are using AMIs with the following operating systems: Debian 12 and Ubuntu 22.04 Server, both using amd64 architecture. The rules about the string search are described in their respective official documentation:
//
// Ubuntu 22.04: https://ubuntu.com/tutorials/search-and-launch-ubuntu-22-04-in-aws-using-cli#2-search-for-the-right-ami
// Debian 12: https://wiki.debian.org/Cloud/AmazonEC2Image/Bookworm
//
// Parallel to that, in order to ensure that the above calls are using valid AWS regions, we are also using the SDK call  DescribeRegions to dynamically fetch them https://pkg.go.dev/github.com/aws/aws-sdk-go-v2/service/ec2#Client.DescribeRegions . This way we ensure that the only valid regions are fetched for an associated AWS account of an Engagement, and subsequently valid AMIs are fetched properly for those regions.
// Both calls are implemented in code, using Go, in the current backend implementation.
package aws

import (
	"context"
	"errors"
	"fmt"
	"log"
	"sort"
	"strings"
	"sync"
	"time"

	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
)

func stringPointer(s string) *string {
	return &s
}

type AMI struct {
	ImageID      string    `json:"image_id"`
	Name         string    `json:"name"`
	Description  string    `json:"description"`
	CreationDate time.Time `json:"creation_date"`
}

type Ec2Client struct {
	svc                     *ec2.Client
	debianImagesAccountID   string
	debian11ImagesAccountID string
	//ubuntuImagesAccountID   string
}

type InstanceTypeMappingChange struct {
	Provider     string
	SizeAlias    string
	Priority     int
	InstanceType string
	Action       string // "add" or "delete"
}

// NewEc2Client initialises the SDK
func NewEc2Client(region string) *Ec2Client {
	cfg, err := config.LoadDefaultConfig(context.TODO(), config.WithRegion(region))
	if err != nil {
		log.Fatalf("error loading AWS config: %v", err)
	}

	svc := ec2.NewFromConfig(cfg)
	return &Ec2Client{
		svc:                     svc,
		debianImagesAccountID:   "************", // https://wiki.debian.org/Cloud/AmazonEC2Image/Bookworm
		debian11ImagesAccountID: "************", // Same account ID for Debian 11 (Bullseye)
		//ubuntuImagesAccountID: "************", // https://ubuntu.com/tutorials/search-and-launch-ubuntu-22-04-in-aws-using-cli#2-search-for-the-right-ami
	}
}

// Retrieve Debian AMI from AWS
func (c *Ec2Client) getDebianAMI() (*AMI, error) {
	params := &ec2.DescribeImagesInput{
		Owners: []string{c.debianImagesAccountID},
		Filters: []types.Filter{
			{
				Name:   stringPointer("name"),
				Values: []string{"debian-12-amd64-*"},
			},
		},
	}
	resp, err := c.svc.DescribeImages(context.TODO(), params)
	if err != nil {
		return nil, fmt.Errorf("error describing images: %v", err)
	}

	var amis []AMI

	for _, image := range resp.Images {

		creationDate, err := time.Parse(time.RFC3339, *image.CreationDate)
		if err != nil {
			return nil, fmt.Errorf("error parsing creation date")

		}
		ami := AMI{
			ImageID:      *image.ImageId,
			Name:         *image.Name,
			Description:  *image.Description,
			CreationDate: creationDate,
		}

		amis = append(amis, ami)
	}

	sort.Slice(amis, func(i, j int) bool {
		return amis[i].CreationDate.Before(amis[j].CreationDate)
	})

	return &amis[len(amis)-1], nil
}

// Retrieve Debian 11 AMI from AWS
func (c *Ec2Client) getDebian11AMI() (*AMI, error) {
	params := &ec2.DescribeImagesInput{
		Owners: []string{c.debian11ImagesAccountID},
		Filters: []types.Filter{
			{
				Name:   stringPointer("name"),
				Values: []string{"debian-11-amd64-*"},
			},
		},
	}
	resp, err := c.svc.DescribeImages(context.TODO(), params)
	if err != nil {
		return nil, fmt.Errorf("error describing images: %v", err)
	}

	var amis []AMI

	for _, image := range resp.Images {
		creationDate, err := time.Parse(time.RFC3339, *image.CreationDate)
		if err != nil {
			return nil, fmt.Errorf("error parsing creation date")
		}
		ami := AMI{
			ImageID:      *image.ImageId,
			Name:         *image.Name,
			Description:  *image.Description,
			CreationDate: creationDate,
		}

		amis = append(amis, ami)
	}

	sort.Slice(amis, func(i, j int) bool {
		return amis[i].CreationDate.Before(amis[j].CreationDate)
	})

	return &amis[len(amis)-1], nil
}

// instanceTypesExist checks if a list of Instance Types exist in a AWS Region
func (c *Ec2Client) instanceTypesExist(instances []string) bool {

	var instanceTypes []types.InstanceType

	for _, instance := range instances {
		instanceTypes = append(instanceTypes, types.InstanceType(instance))
	}

	// Prepare input parameters for DescribeInstanceTypes operation
	params := &ec2.DescribeInstanceTypesInput{
		InstanceTypes: instanceTypes,
	}

	// Call DescribeInstanceTypes API
	_, err := c.svc.DescribeInstanceTypes(context.Background(), params)

	return err == nil
}

// GetRegions retrieves all valid AWS regions associated with this AWS account
// and places US regions at the beginning of the list
func (c *Ec2Client) GetRegions() ([]string, error) {
	var regionNames []string
	var usRegions []string
	var otherRegions []string

	resp, err := c.svc.DescribeRegions(context.Background(), &ec2.DescribeRegionsInput{})
	if err != nil {
		return regionNames, err
	}

	for _, region := range resp.Regions {
		// Check if it's a US region (starts with "us-")
		if strings.HasPrefix(*region.RegionName, "us-") {
			usRegions = append(usRegions, *region.RegionName)
		} else {
			otherRegions = append(otherRegions, *region.RegionName)
		}
	}

	// Sort each slice alphabetically
	sort.Strings(usRegions)
	sort.Strings(otherRegions)

	// Combine the slices with US regions first
	regionNames = append(usRegions, otherRegions...)

	return regionNames, nil

}

// GetRegions retrieves all valid AWS regions associated with this AWS account
// and places US regions at the beginning of the list
func (c *Ec2Client) GetPrioritizedRegions(queries *db.Queries) ([]string, error) {
	var prioritizedRegions []string

	prRegions, err := queries.GetPrioritizedRegions(context.Background())
	if err != nil {
		return prioritizedRegions, err
	}

	// Extract region names from the PrioritizedRegion objects
	for _, region := range prRegions {
		prioritizedRegions = append(prioritizedRegions, region.Name.String)
	}
	return prioritizedRegions, nil

}

// Retrieve Ubuntu AMI from AWS
// func (c *Ec2Client) getUbuntuAMI() (*AMI, error) {
// 	params := &ec2.DescribeImagesInput{
// 		Owners: []string{c.ubuntuImagesAccountID},
// 		Filters: []types.Filter{
// 			{
// 				Name:   stringPointer("name"),
// 				Values: []string{"ubuntu/images/hvm-ssd/ubuntu-jammy-22.04-amd64-server*"},
// 			},
// 		},
// 	}
// 	resp, err := c.svc.DescribeImages(context.TODO(), params)
// 	if err != nil {
// 		return nil, fmt.Errorf("error describing images: %v", err)
// 	}

// 	var amis []AMI

// 	for _, image := range resp.Images {

// 		creationDate, err := time.Parse(time.RFC3339, *image.CreationDate)
// 		if err != nil {
// 			return nil, fmt.Errorf("error parsing creation date")

// 		}
// 		ami := AMI{
// 			ImageID:      *image.ImageId,
// 			Name:         *image.Name,
// 			Description:  *image.Description,
// 			CreationDate: creationDate,
// 		}

// 		amis = append(amis, ami)
// 	}

// 	sort.Slice(amis, func(i, j int) bool {
// 		return amis[i].CreationDate.Before(amis[j].CreationDate)
// 	})

// 	return &amis[len(amis)-1], nil
// }

// GetAllAmis retrieves all AMIs using parallel goroutines
func (c *Ec2Client) GetAllAmis() []AMI {
	var wg sync.WaitGroup
	wg.Add(2)
	var amis []AMI
	// go func() {
	// 	defer wg.Done()
	// 	ubuntuImage, _ := c.getUbuntuAMI()
	// 	amis = append(amis, *ubuntuImage)
	// }()

	go func() {
		defer wg.Done()
		debianImage, _ := c.getDebianAMI()
		amis = append(amis, *debianImage)
	}()

	go func() {
		defer wg.Done()
		debian11Image, _ := c.getDebian11AMI()
		amis = append(amis, *debian11Image)
	}()

	wg.Wait()
	return amis
}

type InstanceType struct {
	Alias string `json:"alias"`
	Type  string `json:"type"`
}

// GetInstanceTypes retrieves the AWS Instance Types that are available to use in a Region
func (c *Ec2Client) GetAllInstanceTypes(queries *db.Queries) ([]InstanceType, error) {

	var instanceTypes []InstanceType

	// First retrieve all priority numbers stored in the database for a provider, like 1,2,3 etc.
	priorities, err := queries.GetInstanceSizePrioritiesForProvider(context.TODO(), "AWS")
	if err != nil {
		return instanceTypes, err
	}

	if len(priorities) == 0 {
		return nil, errors.New("no instance types found")
	}

	// Then loop for each priority starting from the smallest, if mappings are found return them immediately
	for _, priority := range priorities {
		instanceMappings, err := queries.GetInstanceSizeMappingsForProvider(context.TODO(), db.GetInstanceSizeMappingsForProviderParams{Priority: priority, Provider: "AWS"})
		if err != nil {
			return instanceTypes, err
		}

		var instanceTypesNames []string

		// Call AWS for the found instance type mappings in the database to confirm their existence in the region
		for _, mapping := range instanceMappings {
			instanceTypesNames = append(instanceTypesNames, mapping.InstanceType)
		}

		// If they exist then map them and return them, otherwise continue looping through the priorities
		if c.instanceTypesExist(instanceTypesNames) {
			for _, mapping := range instanceMappings {
				instanceTypes = append(instanceTypes, InstanceType{
					Alias: mapping.SizeAlias,
					Type:  mapping.InstanceType,
				})
			}
			return instanceTypes, nil
		}
	}

	// If no applicable Instance Types are found then return an error
	return instanceTypes, errors.New("no instance types found")
}

// GetInstanceTypes retrieves the AWS Instance Types that are available to use in a Region
// and are compatible with the specified AMI. If amiID is empty, it returns all instance types
// without checking for AMI compatibility.
func (c *Ec2Client) GetInstanceTypes(queries *db.Queries, amiID string) ([]InstanceType, error) {
	var instanceTypes []InstanceType

	// First retrieve all priority numbers stored in the database for a provider, like 1,2,3 etc.
	priorities, err := queries.GetInstanceSizePrioritiesForProvider(context.TODO(), "AWS")
	if err != nil {
		return instanceTypes, err
	}

	// If no priorities found, the instance_size_mappings table might be empty
	// Fall back to fetching all instance types from AWS and filter by AMI compatibility
	if len(priorities) == 0 {
		log.Println("No priorities found in the database, falling back to fetching all instance types")
		return c.GetAllInstanceTypesFilteredByRegionAndOS(amiID)
	}

	// Then loop for each priority starting from the smallest, if mappings are found return them immediately
	for _, priority := range priorities {
		instanceMappings, err := queries.GetInstanceSizeMappingsForProvider(context.TODO(), db.GetInstanceSizeMappingsForProviderParams{Priority: priority, Provider: "AWS"})
		if err != nil {
			return instanceTypes, err
		}

		var compatibleInstanceTypes []InstanceType
		var instanceTypesNames []string

		// Call AWS for the found instance type mappings in the database to confirm their existence in the region
		for _, mapping := range instanceMappings {
			instanceTypesNames = append(instanceTypesNames, mapping.InstanceType)
		}

		// If they exist then map them and return them, otherwise continue looping through the priorities
		if c.instanceTypesExist(instanceTypesNames) {
			// If no AMI ID is provided, skip compatibility check and return all instance types
			if amiID == "" {
				for _, mapping := range instanceMappings {
					compatibleInstanceTypes = append(compatibleInstanceTypes, InstanceType{
						Alias: mapping.SizeAlias,
						Type:  mapping.InstanceType,
					})
				}

				if len(compatibleInstanceTypes) > 0 {
					return compatibleInstanceTypes, nil
				}
				continue
			}

			// Only check AMI compatibility if an AMI ID is provided
			for _, mapping := range instanceMappings {
				// Check if this instance type is compatible with the provided AMI
				isCompatible, err := c.isInstanceTypeCompatibleWithAMIArchitecture(mapping.InstanceType, amiID)
				if err != nil {
					// Log the error but continue with other instance types
					log.Printf("Error checking compatibility for %s: %v", mapping.InstanceType, err)
					continue
				}

				if isCompatible {
					compatibleInstanceTypes = append(compatibleInstanceTypes, InstanceType{
						Alias: mapping.SizeAlias,
						Type:  mapping.InstanceType,
					})
				}
			}

			if len(compatibleInstanceTypes) > 0 {
				return compatibleInstanceTypes, nil
			}
			// If no compatible types found at this priority, continue to next priority
		}
	}

	// If no applicable Instance Types are found from the database, fall back to AWS API
	log.Println("No compatible instance types found in database, falling back to fetching all instance types")
	return c.GetAllInstanceTypesFilteredByRegionAndOS(amiID)
}

func (c *Ec2Client) GetAllInstanceTypesFilteredByRegionAndOS(amiID string) ([]InstanceType, error) {
	var instanceTypes []InstanceType

	// Prepare input parameters for DescribeInstanceTypes operation
	input := &ec2.DescribeInstanceTypesInput{
		Filters: []types.Filter{
			{
				Name:   aws.String("current-generation"),
				Values: []string{"true"},
			},
		},
	}

	// If AMI ID is provided, get its architecture to filter compatible instance types
	var amiArch string
	if amiID != "" {
		// Get the AMI details to determine its architecture
		amiParams := &ec2.DescribeImagesInput{
			ImageIds: []string{amiID},
		}
		amiResp, err := c.svc.DescribeImages(context.TODO(), amiParams)
		if err != nil {
			return nil, fmt.Errorf("error describing AMI %s: %v", amiID, err)
		}

		if len(amiResp.Images) == 0 {
			return nil, fmt.Errorf("AMI %s not found", amiID)
		}

		// Extract AMI architecture
		amiArch = string(amiResp.Images[0].Architecture)
		log.Printf("Filtering instance types for AMI: %s (Architecture: %s)", amiID, amiArch)
	}

	paginator := ec2.NewDescribeInstanceTypesPaginator(c.svc, input)

	// Fetch all instance types from AWS
	for paginator.HasMorePages() {
		page, err := paginator.NextPage(context.TODO())
		if err != nil {
			return nil, fmt.Errorf("error fetching instance types: %v", err)
		}

		for _, it := range page.InstanceTypes {
			instanceTypeStr := string(it.InstanceType)

			// Check AMI compatibility if an AMI ID is provided
			if amiID != "" && amiArch != "" {
				// Check if this instance type supports the AMI's architecture
				isCompatible := false
				for _, supportedArch := range it.ProcessorInfo.SupportedArchitectures {
					supportedArchStr := string(supportedArch)

					// Direct match
					if supportedArchStr == amiArch {
						isCompatible = true
						break
					}

					// x86_64 family match (x86_64_mac, etc.)
					if amiArch == "x86_64" && strings.HasPrefix(supportedArchStr, "x86_64") {
						isCompatible = true
						break
					}

					// arm64 family match
					if amiArch == "arm64" && strings.HasPrefix(supportedArchStr, "arm64") {
						isCompatible = true
						break
					}
				}

				if !isCompatible {
					continue
				}
			}

			instanceTypes = append(instanceTypes, InstanceType{
				Alias: instanceTypeStr,
				Type:  instanceTypeStr,
			})
		}
	}

	if len(instanceTypes) == 0 {
		if amiID != "" {
			return nil, errors.New("no compatible instance types found for the selected AMI")
		}
		return nil, errors.New("no instance types found")
	}

	return instanceTypes, nil
}

func (c *Ec2Client) GetAllCloudInstanceTypes() ([]InstanceType, error) {
	var instanceTypes []InstanceType

	input := &ec2.DescribeInstanceTypesInput{
		Filters: []types.Filter{
			{
				Name:   aws.String("current-generation"),
				Values: []string{"true"},
			},
		},
	}
	paginator := ec2.NewDescribeInstanceTypesPaginator(c.svc, input)

	// Fetch all instance types from AWS without size filtering
	for paginator.HasMorePages() {
		page, err := paginator.NextPage(context.TODO())
		if err != nil {
			return nil, fmt.Errorf("error fetching instance types: %v", err)
		}

		for _, it := range page.InstanceTypes {
			instanceTypeStr := string(it.InstanceType)
			instanceTypes = append(instanceTypes, InstanceType{
				Alias: instanceTypeStr,
				Type:  instanceTypeStr,
			})
		}
	}

	if len(instanceTypes) == 0 {
		return nil, errors.New("no instance types found")
	}

	return instanceTypes, nil
}

type RegionInstanceTypeValidation struct {
	InstanceType string `json:"instance_type"`
	SizeAlias    string `json:"size_alias"`
	Priority     int32  `json:"priority"`
	Available    bool   `json:"available"`
}

func (c *Ec2Client) ValidateInstanceTypesInRegion(queries *db.Queries, category string) ([]RegionInstanceTypeValidation, error) {
	// Get mappings specific to this category or all if category is empty
	var mappings []db.InstanceSizeMapping
	var err error

	if category != "" {
		mappings, err = queries.GetInstanceSizeMappingsForCategory(context.TODO(), db.GetInstanceSizeMappingsForCategoryParams{
			Provider:  "AWS",
			SizeAlias: category,
		})
	} else {
		mappings, err = queries.GetAllInstanceSizeMappings(context.TODO())
	}

	if err != nil {
		return nil, fmt.Errorf("failed to get instance mappings: %w", err)
	}

	result := make([]RegionInstanceTypeValidation, 0)
	if len(mappings) == 0 {
		return result, nil
	}

	availableTypes := make(map[string]bool)

	// Use pagination to get ALL available instance types in the region
	input := &ec2.DescribeInstanceTypesInput{
		Filters: []types.Filter{
			{
				Name:   aws.String("current-generation"),
				Values: []string{"true"},
			},
		},
	}

	paginator := ec2.NewDescribeInstanceTypesPaginator(c.svc, input)
	for paginator.HasMorePages() {
		output, err := paginator.NextPage(context.TODO())
		if err != nil {
			return nil, fmt.Errorf("error describing instance types: %w", err)
		}

		for _, instanceType := range output.InstanceTypes {
			availableTypes[string(instanceType.InstanceType)] = true
		}
	}

	for _, mapping := range mappings {
		result = append(result, RegionInstanceTypeValidation{
			InstanceType: mapping.InstanceType,
			SizeAlias:    mapping.SizeAlias,
			Priority:     mapping.Priority,
			Available:    availableTypes[mapping.InstanceType],
		})
	}

	return result, nil
}

func (c *Ec2Client) UpdateInstanceTypes(ctx context.Context, queries *db.Queries, changes []InstanceTypeMappingChange) error {
	for _, change := range changes {
		if change.Action == "delete" {
			err := queries.DeleteInstanceType(ctx, db.DeleteInstanceTypeParams{
				Provider:  db.ProviderEnum(change.Provider),
				SizeAlias: change.SizeAlias,
				Priority:  int32(change.Priority),
			})
			if err != nil {
				return fmt.Errorf("failed to delete instance type: %w", err)
			}
		} else if change.Action == "add" {
			err := queries.UpdateInstanceTypes(ctx, db.UpdateInstanceTypesParams{
				Provider:     db.ProviderEnum(change.Provider),
				SizeAlias:    change.SizeAlias,
				Priority:     int32(change.Priority),
				InstanceType: change.InstanceType,
			})
			if err != nil {
				return fmt.Errorf("failed to update instance type: %w", err)
			}
		}
	}
	return nil
}

// isInstanceTypeCompatibleWithAMIArchitecture checks if an instance type is compatible with the architecture of an AMI
func (c *Ec2Client) isInstanceTypeCompatibleWithAMIArchitecture(instanceType string, amiID string) (bool, error) {
	// Get the AMI details to determine its architecture
	amiParams := &ec2.DescribeImagesInput{
		ImageIds: []string{amiID},
	}
	amiResp, err := c.svc.DescribeImages(context.TODO(), amiParams)
	if err != nil {
		return false, fmt.Errorf("error describing AMI %s: %v", amiID, err)
	}

	if len(amiResp.Images) == 0 {
		return false, fmt.Errorf("AMI %s not found", amiID)
	}

	// Extract AMI architecture
	amiArch := string(amiResp.Images[0].Architecture)
	log.Printf("Checking compatibility for AMI: %s (Architecture: %s)", amiID, amiArch)

	// Get the instance type details to determine supported architectures
	instanceParams := &ec2.DescribeInstanceTypesInput{
		InstanceTypes: []types.InstanceType{types.InstanceType(instanceType)},
	}
	instanceResp, err := c.svc.DescribeInstanceTypes(context.TODO(), instanceParams)
	if err != nil {
		return false, fmt.Errorf("error describing instance type %s: %v", instanceType, err)
	}

	if len(instanceResp.InstanceTypes) == 0 {
		return false, fmt.Errorf("instance type %s not found", instanceType)
	}

	// Check if the instance type supports the AMI's architecture
	instanceInfo := instanceResp.InstanceTypes[0]

	// Check architecture compatibility
	for _, supportedArch := range instanceInfo.ProcessorInfo.SupportedArchitectures {
		supportedArchStr := string(supportedArch)

		// Direct match
		if supportedArchStr == amiArch {
			log.Printf("Instance type %s is compatible with AMI %s (direct match)", instanceType, amiID)
			return true, nil
		}

		// x86_64 family match (x86_64_mac, etc.)
		if amiArch == "x86_64" && strings.HasPrefix(supportedArchStr, "x86_64") {
			log.Printf("Instance type %s is compatible with AMI %s (x86_64 family match)", instanceType, amiID)
			return true, nil
		}

		// arm64 family match
		if amiArch == "arm64" && strings.HasPrefix(supportedArchStr, "arm64") {
			log.Printf("Instance type %s is compatible with AMI %s (arm64 family match)", instanceType, amiID)
			return true, nil
		}
	}

	log.Printf("Instance type %s is NOT compatible with AMI %s", instanceType, amiID)
	return false, nil
}

// SyncPrioritizedRegions updates the prioritized_regions table with the provided regions
// If regions is empty, it will clear the table
func (c *Ec2Client) SyncPrioritizedRegions(ctx context.Context, queries *db.Queries, regions []string) error {
	// Call the SQL query to sync the prioritized regions
	err := queries.SyncPrioritizedRegions(ctx, regions)
	if err != nil {
		return fmt.Errorf("failed to sync prioritized regions: %w", err)
	}
	return nil
}
