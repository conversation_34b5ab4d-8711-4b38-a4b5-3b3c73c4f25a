package aws

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"time"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/service/iam"
	"github.com/aws/aws-sdk-go-v2/service/organizations"
	"github.com/aws/aws-sdk-go-v2/service/organizations/types"
	"github.com/aws/aws-sdk-go-v2/service/sts"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/deployments/keys"
)

// AddEmailAlias adds an alias to an email string
func AddEmailAlias(email string, alias string) (string, error) {
	if !strings.Contains(email, "@") {
		return "", errors.New("invalid email address")
	}

	parts := strings.SplitN(email, "@", 2)
	return fmt.Sprintf("%s+%s@%s", parts[0], alias, parts[1]), nil
}

// CreateAWSAccount automates the creation of an AWS account
func CreateAWSAccount(ctx context.Context, orgClient *organizations.Client, queries *db.Queries, accountName, accountEmail, roleName, ouID, scpID string) (string, error) {
	createAccountInput := &organizations.CreateAccountInput{
		AccountName: aws.String(accountName),
		Email:       aws.String(accountEmail),
		RoleName:    aws.String(roleName),
	}

	output, err := orgClient.CreateAccount(ctx, createAccountInput)
	if err != nil {
		return "", fmt.Errorf("failed to create account: %w", err)
	}

	requestID := *output.CreateAccountStatus.Id

	// Wait for account creation to complete
	var accountID string
	for {
		statusOutput, err := orgClient.DescribeCreateAccountStatus(ctx, &organizations.DescribeCreateAccountStatusInput{
			CreateAccountRequestId: aws.String(requestID),
		})
		if err != nil {
			return "", fmt.Errorf("failed to describe account creation status: %w", err)
		}

		status := statusOutput.CreateAccountStatus.State
		if status == "SUCCEEDED" {
			accountID = *statusOutput.CreateAccountStatus.AccountId
			break
		} else if status == "FAILED" {
			return "", fmt.Errorf("account creation failed: %s", statusOutput.CreateAccountStatus.FailureReason)
		}

		time.Sleep(30 * time.Second)
	}

	// Move account to Organizational Unit (if provided)
	if ouID != "" {
		_, err := orgClient.MoveAccount(ctx, &organizations.MoveAccountInput{
			AccountId:           aws.String(accountID),
			SourceParentId:      aws.String("r-root"),
			DestinationParentId: aws.String(ouID),
		})
		if err != nil {
			return "", fmt.Errorf("failed to move account to OU: %w", err)
		}
	}

	// Attach SCP (if provided)
	if scpID != "" {
		_, err := orgClient.AttachPolicy(ctx, &organizations.AttachPolicyInput{
			PolicyId: aws.String(scpID),
			TargetId: aws.String(accountID),
		})
		if err != nil {
			return "", fmt.Errorf("failed to attach SCP: %w", err)
		}
	}

	return accountID, nil
}

// AssumeRole assumes a new role in a specified AWS account
func AssumeRole(ctx context.Context, stsClient *sts.Client, accountID, roleName string) (*sts.AssumeRoleOutput, error) {
	roleARN := fmt.Sprintf("arn:aws:iam::%s:role/%s", accountID, roleName)

	output, err := stsClient.AssumeRole(ctx, &sts.AssumeRoleInput{
		RoleArn:         aws.String(roleARN),
		RoleSessionName: aws.String("NewAccountSession"),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to assume role: %w", err)
	}

	return output, nil
}

// CreateIAMAdmin creates a new IAMAdmin
func CreateIAMAdmin(ctx context.Context, iamClient *iam.Client, iamName string) (map[string]string, error) {
	password, err := keys.GeneratePassword(16)
	if err != nil {
		return nil, fmt.Errorf("failed to generate password: %w", err)
	}

	// Create user
	_, err = iamClient.CreateUser(ctx, &iam.CreateUserInput{
		UserName: aws.String(iamName),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create IAM user: %w", err)
	}

	// Create login profile
	_, err = iamClient.CreateLoginProfile(ctx, &iam.CreateLoginProfileInput{
		UserName:              aws.String(iamName),
		Password:              aws.String(password),
		PasswordResetRequired: false,
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create login profile: %w", err)
	}

	// Attach AdministratorAccess policy
	_, err = iamClient.AttachUserPolicy(ctx, &iam.AttachUserPolicyInput{
		UserName:  aws.String(iamName),
		PolicyArn: aws.String("arn:aws:iam::aws:policy/AdministratorAccess"),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to attach admin policy: %w", err)
	}

	// Create access key
	accessKeyOutput, err := iamClient.CreateAccessKey(ctx, &iam.CreateAccessKeyInput{
		UserName: aws.String(iamName),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to create access key: %w", err)
	}

	data := map[string]string{
		"admin_username":    iamName,
		"admin_password":    password,
		"access_key_id":     *accessKeyOutput.AccessKey.AccessKeyId,
		"access_key_secret": *accessKeyOutput.AccessKey.SecretAccessKey,
	}
	return data, nil
}

// ListAWSAccounts retrieves and lists all AWS accounts in an organization.
func ListAWSAccounts(ctx context.Context, orgClient *organizations.Client) ([]types.Account, error) {
	var nextToken *string
	var accounts []types.Account

	for {
		resp, err := orgClient.ListAccounts(ctx, &organizations.ListAccountsInput{
			MaxResults: aws.Int32(5),
			NextToken:  nextToken,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to list accounts: %w", err)
		}

		accounts = append(accounts, resp.Accounts...)

		if resp.NextToken == nil {
			break
		}
		nextToken = resp.NextToken
	}

	return accounts, nil
}
