package azure

import (
	"context"
	"encoding/json"
	"fmt"
	"os"

	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/resources/armresources"
	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/resources/armsubscriptions"
	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
)

// GetAzureCredential creates a credential from secrets.
func GetAzureCredential(tenantID, clientID, clientSecret string) (*azidentity.ClientSecretCredential, error) {
	cred, err := azidentity.NewClientSecretCredential(tenantID, clientID, clientSecret, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create Azure credential: %w", err)
	}
	return cred, nil
}

// NewSubscriptionClient returns a subscriptions client
func NewSubscriptionClient(cred *azidentity.ClientSecretCredential) (*armsubscriptions.Client, error) {
	return armsubscriptions.NewClient(cred, nil)
}

// NewResourceClient returns a resource client (for e.g. locations)
func NewResourceClient(cred *azidentity.ClientSecretCredential, subscriptionID string) (*armresources.Client, error) {
	return armresources.NewClient(subscriptionID, cred, nil)
}

type SubaccountCredentials struct {
	TenantID       string `query:"tenantId,required" doc:"Azure Tenant ID"`
	AppID          string `query:"AppId,required" doc:"Azure App ID"`
	AppSecret      string `query:"AppSecret,required" doc:"Azure App Secret"`
	SubscriptionID string `query:"SubscriptionId,required" doc:"Azure Subscription ID"`
}

func GetAzureSecret(secretName string) (*SubaccountCredentials, error) {
	region := os.Getenv("AWS_ROOT_REGION")
	cfg, err := config.LoadDefaultConfig(context.TODO(), config.WithRegion(region))
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS SDK config: %w", err)
	}

	client := secretsmanager.NewFromConfig(cfg)
	result, err := client.GetSecretValue(context.TODO(), &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(secretName),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve secret: %w", err)
	}

	var creds SubaccountCredentials
	err = json.Unmarshal([]byte(*result.SecretString), &creds)
	if err != nil {
		return nil, fmt.Errorf("failed to parse secret JSON: %w", err)
	}
	return &creds, nil
}
