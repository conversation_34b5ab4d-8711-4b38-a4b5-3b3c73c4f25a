package engagements

import (
	"context"
	"database/sql"
	"errors"
	"log"
	"net/netip"
	"slices"
	"sort"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"github.com/jackc/pgx/v5/pgxpool"
	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/accounts"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"golang.org/x/crypto/ssh"

	"log/slog"
)

type Node struct {
	ID        string          `json:"id" format:"uuid"`
	Type      db.NodeTypeEnum `json:"type"`
	Name      string          `json:"name"`
	CreatedAt time.Time       `json:"created_at"`
	UpdatedAt time.Time       `json:"updated_at"`
}

type NodeGroup struct {
	ID        string    `json:"id" format:"uuid"`
	Name      string    `json:"name"`
	IsActive  bool      `json:"is_active"`
	CreatedAt time.Time `json:"created_at"`
	UpdatedAt time.Time `json:"updated_at"`
	Nodes     []Node    `json:"nodes,omitempty"`
}

type Engagement struct {
	ID           string                  `json:"id" format:"uuid"`
	Status       db.EngagementStatusEnum `json:"status"`
	ErrorMessage string                  `json:"error_message"`
	Title        string                  `json:"title"`
	ClientName   string                  `json:"client_name"`
	WbsCode      string                  `json:"wbs_code"`
	Users        []EngagementUser        `json:"users"`
	IsActive     bool                    `json:"is_active"`
	CreatedAt    time.Time               `json:"created_at"`
	UpdatedAt    time.Time               `json:"updated_at"`
	NodeGroups   []NodeGroup             `json:"node_groups,omitempty"`
}

type EngagementUser struct {
	ID                  string `json:"id" format:"uuid"`
	FullName            string `json:"full_name"`
	Username            string `json:"username"`
	CustomUsername      string `json:"custom_username"`
	ValidCustomUsername bool   `json:"valid_custom_username"`
	ValidSshKey         bool   `json:"valid_ssh_key"`
	AppRole             string `json:"app_role"`
}

// GetEngagements retrieves all Engagements and their associated Users
func GetEngagements(queries *db.Queries, userID string, roles []string) ([]Engagement, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	isAdmin := slices.Contains(roles, "Admin")

	var engagementsDB []db.ListEngagementsWithUsersRow

	if isAdmin {
		rows, err := queries.ListEngagementsWithUsers(context.TODO())
		if err != nil {
			return nil, err
		}
		engagementsDB = rows
	} else {
		rows, err := queries.ListEngagementsWithStandardUsers(context.TODO(), *userIDPgType)
		if err != nil {
			return nil, err
		}
		for _, row := range rows {
			engagementsDB = append(engagementsDB, db.ListEngagementsWithUsersRow{
				ID:                  row.ID,
				Status:              row.Status,
				ErrorMessage:        row.ErrorMessage,
				EngagementTitle:     row.EngagementTitle,
				ClientName:          row.ClientName,
				WbsCode:             row.WbsCode,
				EngagementCreatedAt: row.EngagementCreatedAt,
				EngagementUpdatedAt: row.EngagementUpdatedAt,
				IsActive:            row.IsActive,
				UserID:              row.UserID,
				FullName:            row.FullName,
				Username:            row.Username,
				SshKey:              row.SshKey,
			})
		}
	}

	engagementMap := make(map[pgtype.UUID]Engagement)

	for _, engagementDB := range engagementsDB {
		if _, ok := engagementMap[engagementDB.ID]; !ok {
			engagementIDString, _ := converters.PgTypeUUIDToString(engagementDB.ID)
			engagementMap[engagementDB.ID] = Engagement{
				ID:           *engagementIDString,
				Status:       engagementDB.Status,
				ErrorMessage: engagementDB.ErrorMessage.String,
				Title:        engagementDB.EngagementTitle,
				ClientName:   engagementDB.ClientName,
				WbsCode:      engagementDB.WbsCode,
				Users:        []EngagementUser{},
				CreatedAt:    engagementDB.EngagementCreatedAt.Time,
				UpdatedAt:    engagementDB.EngagementUpdatedAt.Time,
				IsActive:     engagementDB.IsActive,
				// CloudAccountStatus: engagementDB.CloudAccountStatus.String,
			}
		}

		e := engagementMap[engagementDB.ID]

		userIDString, err := converters.PgTypeUUIDToString(engagementDB.UserID)
		if err != nil {
			return nil, err
		}
		validCustomUsername := len(engagementDB.Username) != 0
		validSshKey := engagementDB.SshKey.Valid && engagementDB.SshKey.String != ""

		e.Users = append(e.Users, EngagementUser{
			ID:                  *userIDString,
			FullName:            engagementDB.FullName.String,
			Username:            engagementDB.Username,
			ValidCustomUsername: validCustomUsername,
			ValidSshKey:         validSshKey,
		})
		engagementMap[engagementDB.ID] = e
	}

	var engagementsOutput []Engagement
	for _, engagement := range engagementMap {
		engagementsOutput = append(engagementsOutput, engagement)
	}
	return engagementsOutput, nil
}

// GetEngagement retrieves an Engagement's details
func GetEngagement(queries *db.Queries, engagementID string, userID string, includeNodeGroups bool) (*Engagement, error) {
	engagementIDPgType, err := converters.StringToPgTypeUUID(engagementID)
	if err != nil {
		return nil, err
	}
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}
	engagementDBRows, err := queries.GetEngagement(context.Background(), *engagementIDPgType)
	if err != nil {
		return nil, err
	}
	if len(engagementDBRows) > 0 {

		engagementIDString, _ := converters.PgTypeUUIDToString(engagementDBRows[0].ID)
		engagement := Engagement{
			ID:           *engagementIDString,
			Status:       engagementDBRows[0].Status,
			ErrorMessage: engagementDBRows[0].ErrorMessage.String,
			Title:        engagementDBRows[0].EngagementTitle,
			ClientName:   engagementDBRows[0].ClientName,
			WbsCode:      engagementDBRows[0].WbsCode,
			Users:        make([]EngagementUser, 0),
			CreatedAt:    engagementDBRows[0].EngagementCreatedAt.Time,
			UpdatedAt:    engagementDBRows[0].EngagementUpdatedAt.Time,
			IsActive:     engagementDBRows[0].IsActive,
			NodeGroups:   make([]NodeGroup, 0),
		}
		for _, engagementDBRow := range engagementDBRows {
			if engagementDBRow.UserID.Valid {
				userIDString, err := converters.PgTypeUUIDToString(engagementDBRow.UserID)

				if err != nil {
					return nil, err
				}

				_, _, _, _, err = ssh.ParseAuthorizedKey([]byte(engagementDBRow.SshKey.String))
				isValidSshKey := true
				if err != nil {
					isValidSshKey = false
				}

				engagement.Users = append(engagement.Users, EngagementUser{
					ID:                  *userIDString,
					FullName:            engagementDBRow.FullName.String,
					Username:            engagementDBRow.Username.String,
					ValidCustomUsername: len(engagementDBRow.CustomUsername.String) > 0 && engagementDBRow.CustomUsername.Valid,
					ValidSshKey:         isValidSshKey,
				})
			}
		}

		if includeNodeGroups {
			nodeGroups, err := GetEngagementTree(queries, *engagementIDPgType, *userIDPgType)
			if err != nil {
				return nil, err
			}
			engagement.NodeGroups = nodeGroups
		}
		return &engagement, nil
	}
	return nil, nil
}

// CreateEngagement creates a new Engagement in a database, associates it with a Client and assigns Users to it
func CreateEngagement(logger *slog.Logger,
	secretKey string,
	dbConn *pgxpool.Pool,
	queries *db.Queries,
	rabbitMqChannel *amqp.Channel,
	terraformExecPath string,
	clientName string,
	wbsCode string,
	engagementTitle string,
	usernames []string,
	awsRootEmail string,
	awsRootRegion string,
	createdBy string) error {

	// The following functionality will require many database operations:
	// querying a client, create an Engagement, querying existing users, and assigning users to the Engagement
	// Run all of those in a Transaction (https://docs.sqlc.dev/en/stable/howto/transactions.html) and rollback if any of those fail.
	tx, err := dbConn.Begin(context.Background())
	if err != nil {
		return err
	}
	// Check for rollback error explicitly in defer
	defer func() {
		if rbErr := tx.Rollback(context.Background()); rbErr != nil && !errors.Is(rbErr, sql.ErrTxDone) {
			// sql.ErrTxDone is returned if the transaction has already been committed or rolled back, so ignore it.
			log.Printf("rollback failed: %v", rbErr)
		}
	}()

	qtx := queries.WithTx(tx)

	logger = logger.With(
		"client_name", clientName,
		"engagement_title", engagementTitle,
		"wbs_code", wbsCode,
	)

	// Retrieve the Client ID from its name, use it to create and associate it with a new Engagement
	clientDB, err := qtx.GetClientForName(context.Background(), clientName)
	if err != nil {
		if errors.Is(err, sql.ErrNoRows) {
			// Client doesn't exist, so create it.
			err = qtx.CreateClient(context.Background(), clientName)
			if err != nil {
				logger.Error("Failed to create client", "error", err.Error())
				return err
			}
			clientDB, err = qtx.GetClientForName(context.Background(), clientName)
			if err != nil {
				logger.Error("Unable to retrieve client after creation", "error", err.Error())
				return err
			}
		} else {
			logger.Error("Unable to get Client for client name during Engagement creation", "error", err.Error())
			return err
		}
	}

	engagement, err := qtx.CreateEngagement(context.Background(), db.CreateEngagementParams{
		Title:    engagementTitle,
		WbsCode:  wbsCode,
		IsActive: true,
		ClientID: clientDB.ID,
	})
	if err != nil {
		logger.Error("Unable to create Engagement",
			"error", err.Error())
		return err
	}

	engagementIDString, err := converters.PgTypeUUIDToString(engagement.ID)
	if err != nil {
		logger.Error("Failed to convert Engagement ID to string",
			"error", err.Error())
		return err
	}

	logger = logger.With(
		"engagement_id", *engagementIDString,
	)

	// For each Team Member in the request, get their User ID and associate it with the Engagement
	for _, username := range usernames {
		userDB, err := qtx.GetUserForUsername(context.Background(), username)
		if err != nil {
			logger.Error("Unable to get User for username during Engagement creation",
				"username", username,
				"error", err.Error())
			return err
		}
		err = qtx.AssignUsersToEngagement(context.Background(), db.AssignUsersToEngagementParams{
			EngagementID: engagement.ID,
			UserID:       userDB.ID,
		})
		if err != nil {
			logger.Error("Unable to assign User to Engagement during Engagement creation",
				"user_id", userDB.ID,
				"username", username,
				"error", err.Error())
			return err
		}
	}

	// Create a first default Node Group for the Engagement
	_, err = qtx.CreateNodeGroup(context.Background(), db.CreateNodeGroupParams{
		Name:         "Node Group 1",
		IsActive:     true,
		EngagementID: engagement.ID,
		CreatedAt: pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		},
		UpdatedAt: pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		},
	})
	if err != nil {
		logger.Error("Unable to create Node Group during Engagement creation",
			"error", err.Error())
		_ = queries.SetEngagementStatusToError(context.Background(), db.SetEngagementStatusToErrorParams{
			ErrorMessage: pgtype.Text{String: err.Error(), Valid: true},
			ID:           engagement.ID,
		})
		return err
	}
	q, err := rabbitMqChannel.QueueDeclare(
		*engagementIDString, // name
		true,                // durable
		false,               // delete when unused
		false,               // exclusive
		false,               // no-wait
		nil,                 // arguments
	)
	if err != nil {
		logger.Error("Failed to declare queue for Engagement",
			"error", err.Error())
		return err
	}
	logger.Info("Queue declared for Engagement",
		"queue_name", *engagementIDString)

	_, err = rabbitMqChannel.Consume(
		q.Name, // queue
		"",     // consumer
		false,  // auto-ack
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	if err != nil {
		logger.Error("Failed to register consumer for Engagement")
		panic("Failed to register consumer for Engagement")
	}
	logger.Info("Consumer registered for Engagement", "queue_name", *engagementIDString)

	nickname := "Primary Account"

	err = accounts.PublishAWSAccountCreationMessage(rabbitMqChannel, *engagementIDString, secretKey, awsRootEmail, awsRootRegion, logger, queries, nickname, createdBy)
	if err != nil {
		logger.Error("Failed to publish aws account creation msg", "error", err.Error())
		return err
	}

	return tx.Commit(context.Background())
}

// GetEngagementTree gets an Engagement's details along with all its Nodes grouped by Node Groups.
func GetEngagementTree(queries *db.Queries, engagementID pgtype.UUID, userID pgtype.UUID) ([]NodeGroup, error) {

	engagementTreeRows, err := queries.GetEngagementTree(context.Background(), db.GetEngagementTreeParams{
		ID:   engagementID,
		ID_2: userID,
	})
	if err != nil {
		return nil, err
	}

	var nodeGroups = make([]NodeGroup, 0)

	if len(engagementTreeRows) > 0 {
		nodeGroupsMap := make(map[pgtype.UUID]NodeGroup)

		// Loop through results from the JOIN query and group Nodes by Node Group
		for _, engagementTreeRow := range engagementTreeRows {

			// If the Node Group doesn't exist yet in memory, create it
			if _, ok := nodeGroupsMap[engagementTreeRow.NodeGroupID]; !ok {
				nodeGroupIDString, _ := converters.PgTypeUUIDToString(engagementTreeRow.NodeGroupID)
				nodeGroupsMap[engagementTreeRow.NodeGroupID] = NodeGroup{
					ID:        *nodeGroupIDString,
					Name:      engagementTreeRow.NodeGroupName,
					IsActive:  engagementTreeRow.NodeGroupIsActive,
					Nodes:     []Node{},
					CreatedAt: engagementTreeRow.NodeGroupCreatedAt.Time,
					UpdatedAt: engagementTreeRow.NodeGroupUpdatedAt.Time,
				}
			}
			// Only append to the Nodes if it's a valid Node
			if engagementTreeRow.NodeID.Valid {
				nodeIDString, _ := converters.PgTypeUUIDToString(engagementTreeRow.NodeID)
				node := Node{ID: *nodeIDString,
					Type:      engagementTreeRow.NodeType,
					Name:      engagementTreeRow.NodeName,
					CreatedAt: engagementTreeRow.NodeCreatedAt.Time,
					UpdatedAt: engagementTreeRow.NodeUpdatedAt.Time}
				nodeGroup := nodeGroupsMap[engagementTreeRow.NodeGroupID]
				nodeGroup.Nodes = append(nodeGroup.Nodes, node)
				nodeGroupsMap[engagementTreeRow.NodeGroupID] = nodeGroup
			}
		}

		for _, nodeGroup := range nodeGroupsMap {
			nodeGroups = append(nodeGroups, nodeGroup)
		}

		// Sort NodeGroups primarily by ascending alphabetically NodeGroup name and secondarily by descending CreatedAt time
		sort.Slice(nodeGroups, func(i, j int) bool {
			if nodeGroups[i].Name == nodeGroups[j].Name {
				return nodeGroups[j].CreatedAt.Before(nodeGroups[i].CreatedAt)
			}
			return nodeGroups[i].Name < nodeGroups[j].Name
		})
	}
	return nodeGroups, nil
}

type CloudInstance struct {
	Node
	OperatingSystemImageID string          `json:"operating_system_image_id"`
	Provider               db.ProviderEnum `json:"provider"`
	Name                   string          `json:"name"`
	Region                 string          `json:"region"`
	PublicIpV4Address      *netip.Addr     `json:"public_ipv4_address" format:"ipv4"`
	OpenPorts              []int32         `json:"open_ports"`
	CiDeploymentStatus     string          `json:"ci_deployment_status" enum:"PENDING,IN-PROGRESS,SUCCESS,WARNING,ERROR"`
	CloudInstanceState     string          `json:"cloud_instance_state" enum:"pending,running,stopping,stopped,shutting-down,terminated,error,new"`
	CloudInstanceID        string          `json:"cloud_instance_id"`
}

type CloudInstanceNodeGroups struct {
	ID             string          `json:"id" format:"uuid"`
	Name           string          `json:"name"`
	IsActive       bool            `json:"is_active"`
	CreatedAt      time.Time       `json:"created_at"`
	UpdatedAt      time.Time       `json:"updated_at"`
	CloudInstances []CloudInstance `json:"cloud_instances"`
}

// GetEngagementCloudInstances gets the cloud instances of an Engagement grouped by Node Groups.
func GetEngagementCloudInstances(queries *db.Queries, engagementIDs []string, userID string) ([]CloudInstanceNodeGroups, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}
	var engagementIDsSlice []pgtype.UUID
	for _, engagementID := range engagementIDs {
		var engagementIDPgType *pgtype.UUID
		engagementIDPgType, err = converters.StringToPgTypeUUID(engagementID)
		if err != nil {
			return nil, err
		}
		engagementIDsSlice = append(engagementIDsSlice, *engagementIDPgType)
	}
	cloudInstancesDB, err := queries.GetEngagementCloudInstancesForUser(context.Background(), db.GetEngagementCloudInstancesForUserParams{
		ID:  *userIDPgType,
		Ids: engagementIDsSlice,
	})

	if err != nil {
		return nil, err
	}

	cloudInstanceNodeGroups := make([]CloudInstanceNodeGroups, 0)
	nodeGroupsMap := make(map[pgtype.UUID]CloudInstanceNodeGroups)

	// Loop through results from the JOIN query and group Cloud Instances by Node Group
	for _, cloudInstanceDB := range cloudInstancesDB {

		// If the Node Group doesn't exist yet in memory, create it
		if _, ok := nodeGroupsMap[cloudInstanceDB.NodeGroupID]; !ok {
			nodeGroupIDString, _ := converters.PgTypeUUIDToString(cloudInstanceDB.NodeGroupID)
			nodeGroupsMap[cloudInstanceDB.NodeGroupID] = CloudInstanceNodeGroups{
				ID:             *nodeGroupIDString,
				Name:           cloudInstanceDB.NodeGroupName,
				IsActive:       cloudInstanceDB.NodeGroupIsActive,
				CreatedAt:      cloudInstanceDB.NodeGroupCreatedAt.Time,
				UpdatedAt:      cloudInstanceDB.NodeGroupUpdatedAt.Time,
				CloudInstances: []CloudInstance{}}
		}
		// Only append to the Cloud Instances if it's a valid Cloud Instance
		if cloudInstanceDB.NodeID.Valid {
			nodeIDString, _ := converters.PgTypeUUIDToString(cloudInstanceDB.NodeID)
			node := Node{
				ID:        *nodeIDString,
				Type:      "CLOUD_INSTANCE",
				CreatedAt: cloudInstanceDB.NodeCreatedAt.Time,
				UpdatedAt: cloudInstanceDB.NodeUpdatedAt.Time,
			}
			cloudInstance := CloudInstance{
				Node:                   node,
				OperatingSystemImageID: cloudInstanceDB.OperatingSystemImageID,
				Provider:               cloudInstanceDB.Provider,
				Name:                   cloudInstanceDB.Name,
				Region:                 cloudInstanceDB.Region,
				PublicIpV4Address:      cloudInstanceDB.PublicIpv4Address,
				OpenPorts:              cloudInstanceDB.OpenPorts,
				CiDeploymentStatus:     string(cloudInstanceDB.CiDeploymentStatus),
				CloudInstanceState:     string(cloudInstanceDB.CloudInstanceState.CiStateEnum),
				CloudInstanceID:        cloudInstanceDB.CloudInstanceID.String,
			}
			nodeGroup := nodeGroupsMap[cloudInstanceDB.NodeGroupID]
			nodeGroup.CloudInstances = append(nodeGroup.CloudInstances, cloudInstance)
			nodeGroupsMap[cloudInstanceDB.NodeGroupID] = nodeGroup
		}
	}

	for _, nodeGroup := range nodeGroupsMap {
		cloudInstanceNodeGroups = append(cloudInstanceNodeGroups, nodeGroup)
	}
	return cloudInstanceNodeGroups, nil
}
