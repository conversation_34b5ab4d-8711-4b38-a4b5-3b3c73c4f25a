package engagements

import (
	"context"
	"fmt"
	"sort"
	"time"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

type Graph struct {
	NodeGroupID   string    `json:"node_group_id" format:"uuid"`
	NodeGroupName string    `json:"node_group_name"`
	CreatedAt     time.Time `json:"created_at"`
	UpdatedAt     time.Time `json:"updated_at"`
	Elements      []Element `json:"elements"`
}

type Element struct {
	Group   string   `json:"group"`
	Data    Data     `json:"data"`
	Classes []string `json:"classes,omitempty"`
}

type Data struct {
	Label              string `json:"label"`
	ID                 string `json:"id,omitempty"`
	Source             string `json:"source,omitempty"`
	Target             string `json:"target,omitempty"`
	CloudInstanceState string `json:"cloud_instance_state"`
}

// GetEngagementGraphNodes returns all Nodes in an Engagement, grouped in Graphs compatible with Cytoscape.js
func GetEngagementGraphNodes(queries *db.Queries, engagementID string, userID string) ([]Graph, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	engagementIDPgType, err := converters.StringToPgTypeUUID(engagementID)
	if err != nil {
		return nil, err
	}

	graphNodes, err := queries.GetEngagementAllNodes(context.Background(), db.GetEngagementAllNodesParams{
		ID:   *engagementIDPgType,
		ID_2: *userIDPgType,
	})
	if err != nil {
		return nil, err
	}

	graphRelationships, err := queries.GetEngagementGraphRelationships(context.Background(), db.GetEngagementGraphRelationshipsParams{
		ID:   *engagementIDPgType,
		ID_2: *userIDPgType,
	})
	if err != nil {
		return nil, err
	}

	var graphs []Graph

	if len(graphNodes) > 0 {

		// graphsMap is a mapping of a Node Group ID to the Graph corresponding to it
		graphsMap := make(map[pgtype.UUID]Graph)

		// Loop through all Nodes associated with an Engagement
		for _, graphNode := range graphNodes {

			// If the Graph doesn't exist yet in memory, create it
			if _, ok := graphsMap[graphNode.NodeGroupID]; !ok {
				nodeGroupIDString, _ := converters.PgTypeUUIDToString(graphNode.NodeGroupID)
				graphsMap[graphNode.NodeGroupID] = Graph{
					NodeGroupID:   *nodeGroupIDString,
					NodeGroupName: graphNode.NodeGroupName,
					CreatedAt:     graphNode.CreatedAt.Time,
					UpdatedAt:     graphNode.UpdatedAt.Time,
					Elements:      []Element{},
				}
			}

			// Create a Cytoscape.js item that represents a graph node
			element := Element{
				Group: "nodes",
				Data: Data{
					Label:              graphNode.NodeName,
					ID:                 uuid.UUID(graphNode.NodeID.Bytes).String(),
					CloudInstanceState: string(graphNode.CloudInstanceState.CiStateEnum),
				},
				Classes: []string{string(graphNode.NodeType)},
			}
			// Each 'node' Element has the Node Group ID associated with it
			// This can be used add the Item to its appropriate Graph in the map
			graph := graphsMap[graphNode.NodeGroupID]
			graph.Elements = append(graph.Elements, element)
			graphsMap[graphNode.NodeGroupID] = graph
		}

		// Add edges
		for _, graphRelationship := range graphRelationships {

			// Create a Cytoscape.js item that represents a graph edge
			sourceNodeIDString := uuid.UUID(graphRelationship.SourceNodeID.Bytes).String()
			targetNodeIDString := uuid.UUID(graphRelationship.TargetNodeID.Bytes).String()
			item := Element{
				Group: "edges",
				Data: Data{
					Source: sourceNodeIDString,
					Target: targetNodeIDString,
					Label:  fmt.Sprintf("Edge from %s to %s", sourceNodeIDString, targetNodeIDString),
				},
			}

			// Each 'edge' Element has the Node Group ID associated with it
			// This can be used add the Item to its appropriate Graph in the map
			graph := graphsMap[graphRelationship.NodeGroupID]
			graph.Elements = append(graph.Elements, item)
			graphsMap[graphRelationship.NodeGroupID] = graph
		}

		// Format response to a slice of Graphs, get only the values of the graphMap
		for _, graph := range graphsMap {
			graphs = append(graphs, graph)
		}

		// Sort Graphs primarily by ascending alphabetically NodeGroup name and secondarily by descending CreatedAt time
		sort.Slice(graphs, func(i, j int) bool {
			if graphs[i].NodeGroupName == graphs[j].NodeGroupName {
				return graphs[j].CreatedAt.Before(graphs[i].CreatedAt)
			}
			return graphs[i].NodeGroupName < graphs[j].NodeGroupName
		})
	}
	return graphs, nil
}

// GetEngagementGraphNode returns all Nodes in an Engagement's NodeGroup, in a Graph compatible with Cytoscape.js
func GetEngagementGraphNode(queries *db.Queries, engagementID string, nodeGroupID string, userID string) (*Graph, error) {

	engagementIDPgType, err := converters.StringToPgTypeUUID(engagementID)
	if err != nil {
		return nil, err
	}
	nodeGroupIDPgType, err := converters.StringToPgTypeUUID(nodeGroupID)
	if err != nil {
		return nil, err
	}
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	graphNodes, err := queries.GetEngagementNodeGroupNodes(
		context.Background(), db.GetEngagementNodeGroupNodesParams{
			ID:          *engagementIDPgType,
			ID_2:        *userIDPgType,
			NodeGroupID: *nodeGroupIDPgType,
		})
	if err != nil {
		return nil, err
	}

	nodeGroupIDString, _ := converters.PgTypeUUIDToString(*nodeGroupIDPgType)
	graph := Graph{
		NodeGroupID:   *nodeGroupIDString,
		NodeGroupName: graphNodes[0].NodeName,
		CreatedAt:     graphNodes[0].CreatedAt.Time,
		UpdatedAt:     graphNodes[0].UpdatedAt.Time,
		Elements:      []Element{},
	}

	if len(graphNodes) > 0 {
		graphRelationships, err := queries.GetEngagementNodeGroupGraphRelationships(
			context.Background(),
			db.GetEngagementNodeGroupGraphRelationshipsParams{
				ID:          *engagementIDPgType,
				NodeGroupID: *nodeGroupIDPgType,
			})
		if err != nil {
			return nil, err
		}

		// Loop through all Nodes associated with an Engagement Node Group
		for _, graphNode := range graphNodes {
			// Create a Cytoscape.js item that represents a graph node
			element := Element{
				Group: "nodes",
				Data: Data{
					Label: graphNode.NodeName,
					ID:    uuid.UUID(graphNode.NodeID.Bytes).String(),
				},
				Classes: []string{string(graphNode.NodeType)},
			}

			graph.Elements = append(graph.Elements, element)
		}

		// Add edges
		for _, graphRelationship := range graphRelationships {

			// Create a Cytoscape.js item that represents a graph edge
			sourceNodeIDString := uuid.UUID(graphRelationship.SourceNodeID.Bytes).String()
			targetNodeIDString := uuid.UUID(graphRelationship.TargetNodeID.Bytes).String()
			item := Element{
				Group: "edges",
				Data: Data{
					Source: sourceNodeIDString,
					Target: targetNodeIDString,
					Label:  fmt.Sprintf("Edge from %s to %s", sourceNodeIDString, targetNodeIDString),
				},
			}
			graph.Elements = append(graph.Elements, item)
		}
	}
	return &graph, nil
}
