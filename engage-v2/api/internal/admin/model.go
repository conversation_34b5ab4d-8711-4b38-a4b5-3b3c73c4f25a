package admin

import (
	"context"
	"time"

	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/converters"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

type AdminScript struct {
	ID          string            `json:"id" format:"uuid"`
	Name        string            `json:"name"`
	Description string            `json:"description"`
	Content     string            `json:"content"`
	ScriptType  db.ScriptTypeEnum `json:"script_type" enum:"ADMIN"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	UserID      string            `json:"user_id"`
}

func GetScripts(queries *db.Queries) ([]AdminScript, error) {
	scriptsDB, err := queries.GetAdminScripts(context.Background())
	if err != nil {
		return nil, err
	}

	var scripts []AdminScript
	for _, scriptDB := range scriptsDB {
		scriptIDString, _ := converters.PgTypeUUIDToString(scriptDB.ID)
		userIDString, _ := converters.PgTypeUUIDToString(scriptDB.UserID)

		script := AdminScript{
			ID:          *scriptIDString,
			Name:        scriptDB.Name,
			Description: scriptDB.Description,
			Content:     scriptDB.Content,
			ScriptType:  scriptDB.ScriptType,
			CreatedAt:   scriptDB.CreatedAt.Time,
			UpdatedAt:   scriptDB.UpdatedAt.Time,
			UserID:      *userIDString,
		}
		scripts = append(scripts, script)
	}
	return scripts, nil
}

func CreateNewAdminScript(queries *db.Queries, name string, description string, content string, scriptType db.ScriptTypeEnum, userID string) (*AdminScript, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	newScript, err := queries.CreateAdminScript(context.Background(), db.CreateAdminScriptParams{
		Name:        name,
		Description: description,
		Content:     content,
		UserID:      *userIDPgType,
		CreatedAt: pgtype.Timestamp{
			Time:             time.Now(),
			InfinityModifier: 0,
			Valid:            true,
		},
	})
	if err != nil {
		return nil, err
	}

	scriptIDString, err := converters.PgTypeUUIDToString(newScript.ID)
	if err != nil {
		return nil, err
	}

	userIDString, _ := converters.PgTypeUUIDToString(newScript.UserID)
	if err != nil {
		return nil, err
	}

	scriptResponse := AdminScript{
		ID:          *scriptIDString,
		Name:        newScript.Name,
		Description: newScript.Description,
		Content:     newScript.Content,
		ScriptType:  db.ScriptTypeEnumADMIN,
		CreatedAt:   newScript.CreatedAt.Time,
		UpdatedAt:   newScript.UpdatedAt.Time,
		UserID:      *userIDString,
	}

	return &scriptResponse, nil
}

func UpdateAdminScript(queries *db.Queries, scriptID string, name string, description string, content string, scriptType db.ScriptTypeEnum, userID string) (*AdminScript, error) {
	userIDPgType, err := converters.StringToPgTypeUUID(userID)
	if err != nil {
		return nil, err
	}

	scriptIDPgType, err := converters.StringToPgTypeUUID(scriptID)
	if err != nil {
		return nil, err
	}

	updatedScript, err := queries.EditAdminScript(context.Background(), db.EditAdminScriptParams{
		ID:          *scriptIDPgType,
		Name:        name,
		Description: description,
		Content:     content,
		UserID:      *userIDPgType,
	})
	if err != nil {
		return nil, err
	}

	scriptResponse := AdminScript{
		ID:          scriptID,
		Name:        updatedScript.Name,
		Description: updatedScript.Description,
		Content:     updatedScript.Content,
		ScriptType:  scriptType,
		CreatedAt:   updatedScript.CreatedAt.Time,
		UpdatedAt:   updatedScript.UpdatedAt.Time,
		UserID:      userID,
	}

	return &scriptResponse, nil
}
