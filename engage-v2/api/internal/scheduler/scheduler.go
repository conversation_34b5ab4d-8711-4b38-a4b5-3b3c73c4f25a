package scheduler

import (
	"context"
	"log/slog"

	amqp "github.com/rabbitmq/amqp091-go"

	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

func PublishAccountSyncTask(ctx context.Context, ch *amqp.Channel, queueName string) error {
	msg := amqp.Publishing{
		ContentType: "text/plain",
		Body:        []byte("sync"),
	}
	return ch.Publish(
		"",
		queueName,
		false,
		false,
		msg,
	)
}

func StartAccountSyncWorker(
	ch *amqp.Channel,
	queueName string,
	queries *db.Queries,
	logger *slog.Logger,
	syncFunc func(ctx context.Context, queries *db.Queries, logger *slog.Logger) error,
) {
	q, err := ch.QueueDeclare(
		queueName,
		true, false, false, false, nil,
	)
	if err != nil {
		logger.Error("Failed to declare accounts sync queue", "error", err)
		return
	}

	msgs, err := ch.Consume(q.Name, "", false, false, false, false, nil)
	if err != nil {
		logger.Error("Failed to register consumer", "error", err)
		return
	}

	go func() {
		for d := range msgs {
			logger.Info("Received accounts sync message", "queue", q.Name, "message", string(d.Body))
			if err := syncFunc(context.Background(), queries, logger); err != nil {
				logger.Error("Account sync failed", "queue", q.Name, "error", err)
			}
			if err := d.Ack(false); err != nil {
				logger.Error("Failed to ack message", "queue", q.Name, "error", err)
			}
		}
	}()
}
