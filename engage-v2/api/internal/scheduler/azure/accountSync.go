package azureScheduler

import (
	"context"
	"fmt"
	"log/slog"

	"github.com/jackc/pgx/v5/pgtype"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/azure"
)

func SyncAzureTenantSubscriptionStatus(
	ctx context.Context,
	queries *db.Queries,
	logger *slog.Logger,
) error {
	// Step 1: Fetch all tenants with a subscription ID
	tenantRows, err := queries.ListAzureTenantsWithSubscriptions(ctx)
	if err != nil {
		return fmt.Errorf("failed to get tenants with subscription: %w", err)
	}

	var updatedCount int

	for _, row := range tenantRows {
		subscriptionID := row.SubscriptionID.String
		tenantUUID := row.ID // used for secrets manager lookup
		tenantIDString := row.ID.String()

		// Step 2: Fetch credentials using tenant UUID from AWS Secrets Manager
		secret, err := azure.GetAzureSecret(tenantIDString)
		if err != nil {
			logger.Error("Failed to fetch Azure credentials", "id", tenantIDString, "error", err)
			continue
		}

		// Step 3: Build Azure credential from secret
		cred, err := azure.GetAzureCredential(secret.TenantID, secret.AppID, secret.AppSecret)
		if err != nil {
			logger.Error("Failed to create Azure credential", "id", tenantIDString, "error", err)
			continue
		}

		// Step 4: Create subscriptions client
		client, err := azure.NewSubscriptionClient(cred)
		if err != nil {
			logger.Error("Failed to create Azure subscriptions client", "id", tenantIDString, "error", err)
			continue
		}

		// Step 5: Get subscription status
		subResp, err := client.Get(ctx, subscriptionID, nil)
		if err != nil {
			logger.Error("Failed to fetch subscription status", "subscriptionID", subscriptionID, "error", err)
			continue
		}

		status := "Disabled" // default fallback
		if subResp.State != nil {
			status = string(*subResp.State)
		}

		// Step 6: Update if status changed
		if !row.AccountCloudStatus.Valid || row.AccountCloudStatus.String != status {
			err := queries.SetAzureSubscriptionStatus(ctx, db.SetAzureSubscriptionStatusParams{
				ID:                 tenantUUID,
				AccountCloudStatus: pgtype.Text{String: status, Valid: true},
			})
			if err != nil {
				logger.Error("Failed to update subscription status", "id", tenantIDString, "error", err)
				continue
			}
			updatedCount++
			logger.Info("Updated subscription status", "id", tenantIDString, "status", status)
		}
	}

	logger.Info("Azure tenant subscription sync complete", "updatedCount", updatedCount)
	return nil
}
