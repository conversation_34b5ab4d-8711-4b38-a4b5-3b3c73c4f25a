package azureScheduler

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"log/slog"
	"strings"
	"sync"

	"github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/compute/armcompute"
	"github.com/jackc/pgx/v5/pgtype"
	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/azure"
)

// PublishAzureCloudInstanceSyncTask publishes a simple message to trigger the Azure VM sync task.
func PublishAzureCloudInstanceSyncTask(ctx context.Context, ch *amqp.Channel, queueName string) error {
	task := map[string]string{"task": "sync_azure_cloud_instances"}
	body, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("failed to marshal Azure cloud instance sync task: %w", err)
	}

	err = ch.PublishWithContext(
		ctx,
		"",        // exchange
		queueName, // routing key
		false,     // mandatory
		false,     // immediate
		amqp.Publishing{
			ContentType: "application/json",
			Body:        body,
		})
	if err != nil {
		return fmt.Errorf("failed to publish Azure cloud instance sync task: %w", err)
	}
	return nil
}

// StartAzureCloudInstanceSyncWorker starts a RabbitMQ consumer that listens for Azure cloud instance sync tasks.
func StartAzureCloudInstanceSyncWorker(ch *amqp.Channel, queueName string, queries *db.Queries, logger *slog.Logger) {
	q, err := ch.QueueDeclare(
		queueName, // name
		true,      // durable
		false,     // delete when unused
		false,     // exclusive
		false,     // no-wait
		nil,       // arguments
	)
	if err != nil {
		logger.Error("Failed to declare Azure cloud instance sync queue", "error", err)
		return
	}

	msgs, err := ch.Consume(
		q.Name, // queue
		"",     // consumer
		false,  // auto-ack
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	if err != nil {
		logger.Error("Failed to register Azure cloud instance sync consumer", "error", err)
		return
	}

	go func() {
		for msg := range msgs {
			logger.Info("Received Azure cloud instance sync task")
			err := SyncAzureVMInstanceState(queries, context.Background(), logger)
			if err != nil {
				logger.Error("Error syncing Azure cloud instance state", "error", err)
			}
			if err := msg.Ack(false); err != nil {
				logger.Error("Failed to acknowledge Azure cloud instance sync message", "error", err)
			}
		}
	}()
}

// SyncAzureVMInstanceState syncs Azure VM instance states similar to AWS EC2 sync
func SyncAzureVMInstanceState(queries *db.Queries, ctx context.Context, logger *slog.Logger) error {
	log.Println("Syncing Azure VM instances started")

	// Mark instances as 'failed' if they have no cloud_instance_id even after 10 min after deployment
	err := queries.MarkStuckInstancesAsFailed(ctx)
	if err != nil {
		log.Printf("Failed to mark stuck instances as 'failed': %v", err)
	} else {
		log.Println("Stuck instances mark step done")
	}

	// Mark instances as 'terminated' if their Azure tenants are inactive
	err = queries.MarkInstancesAsTerminatedForInactiveAzureTenants(ctx)
	if err != nil {
		log.Printf("Failed to mark instances as 'terminated' for inactive tenants: %v", err)
	} else {
		log.Println("Inactive tenants instances sync step done")
	}

	// Get Azure instances whose status should be checked for updates
	instances, err := queries.GetAzureCloudInstancesWithRegionAndTenantInfo(ctx)
	if err != nil {
		log.Printf("Error fetching updatable Azure instances: %v", err)
		return nil
	}

	// If nothing validates for updates, stop update here
	if len(instances) == 0 {
		log.Println("Nothing to sync with Azure, all instances states final. Stopping sync process.")
		return nil
	}

	log.Printf("Found %d Azure instances to sync", len(instances))

	// Group instances by tenant ID and subscription ID
	tenantGroups := make(map[string]map[string][]string) // tenantID -> subscriptionID -> []instanceIDs

	for _, instance := range instances {
		if !instance.CloudInstanceID.Valid || instance.CloudInstanceID.String == "" {
			continue
		}

		tenantID := instance.AzureTenantID.String()
		subscriptionID := instance.SubscriptionID.String

		if tenantGroups[tenantID] == nil {
			tenantGroups[tenantID] = make(map[string][]string)
		}
		if tenantGroups[tenantID][subscriptionID] == nil {
			tenantGroups[tenantID][subscriptionID] = make([]string, 0)
		}

		tenantGroups[tenantID][subscriptionID] = append(
			tenantGroups[tenantID][subscriptionID],
			instance.CloudInstanceID.String,
		)
	}

	var wg sync.WaitGroup

	// Process each tenant
	for tenantID, subscriptions := range tenantGroups {
		wg.Add(1)
		go func(tenantID string, subscriptions map[string][]string) {
			defer wg.Done()

			// Get Azure credentials for this tenant
			secret, err := azure.GetAzureSecret(tenantID)
			if err != nil {
				log.Printf("[Tenant: %s] Failed to get Azure credentials: %v", tenantID, err)
				return
			}

			cred, err := azure.GetAzureCredential(secret.TenantID, secret.AppID, secret.AppSecret)
			if err != nil {
				log.Printf("[Tenant: %s] Failed to create Azure credential: %v", tenantID, err)
				return
			}

			// Process each subscription for this tenant
			for subscriptionID, instanceIDs := range subscriptions {
				log.Printf("[Tenant: %s] [Subscription: %s] Processing subscription with instances: %v", tenantID, subscriptionID, instanceIDs)

				wg.Add(1)
				go func(tenantID, subscriptionID string, instanceIDs []string) {
					defer wg.Done()

					client, err := armcompute.NewVirtualMachinesClient(subscriptionID, cred, nil)
					if err != nil {
						log.Printf("[Tenant: %s] [Subscription: %s] Failed to create compute client: %v", tenantID, subscriptionID, err)
						return
					}

					log.Printf("[Tenant: %s] [Subscription: %s] Compute client initialized", tenantID, subscriptionID)
					getAzureVMStates(queries, client, instanceIDs, tenantID, subscriptionID, ctx)
					log.Printf("[Tenant: %s] [Subscription: %s] VM state retrieval completed.", tenantID, subscriptionID)
				}(tenantID, subscriptionID, instanceIDs)
			}
		}(tenantID, subscriptions)
	}

	wg.Wait()
	log.Println("Azure VM instances sync completed")
	return nil
}

func getAzureVMStates(queries *db.Queries, client *armcompute.VirtualMachinesClient, instanceIDs []string, tenantID, subscriptionID string, ctx context.Context) {
	foundInstances := make(map[string]bool)

	for _, instanceID := range instanceIDs {
		// Parse resource group and VM name from instance ID
		// Azure VM instance ID format: /subscriptions/{subscription}/resourceGroups/{resourceGroup}/providers/Microsoft.Compute/virtualMachines/{vmName}
		parts := strings.Split(instanceID, "/")
		if len(parts) < 9 {
			log.Printf("[Tenant: %s] [Subscription: %s] Invalid instance ID format: %s", tenantID, subscriptionID, instanceID)
			continue
		}

		resourceGroupName := parts[4]
		vmName := parts[8]

		// Get VM instance view to get power state
		resp, err := client.InstanceView(ctx, resourceGroupName, vmName, nil)
		if err != nil {
			if strings.Contains(err.Error(), "ResourceNotFound") || strings.Contains(err.Error(), "NotFound") {
				log.Printf("[Tenant: %s] [Subscription: %s] VM %s no longer exists, marking as terminated.", tenantID, subscriptionID, instanceID)
				markAzureVMTerminated(queries, instanceID, ctx)
				continue
			}
			log.Printf("[Tenant: %s] [Subscription: %s] Error getting VM instance view for %s: %v", tenantID, subscriptionID, instanceID, err)
			continue
		}

		foundInstances[instanceID] = true

		// Extract power state from statuses
		powerState := "unknown"
		if resp.Statuses != nil {
			for _, status := range resp.Statuses {
				if status.Code != nil && strings.HasPrefix(*status.Code, "PowerState/") {
					powerState = strings.TrimPrefix(*status.Code, "PowerState/")
					break
				}
			}
		}

		log.Printf("[Tenant: %s] [Subscription: %s] VM %s is in state: %s", tenantID, subscriptionID, instanceID, powerState)

		// Map Azure power states to our enum values
		mappedState := mapAzurePowerStateToEnum(powerState)

		updateParams := db.UpdateCloudInstanceAzureStateParams{
			CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum(mappedState), Valid: true},
			CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
		}

		if err := queries.UpdateCloudInstanceAzureState(ctx, updateParams); err != nil {
			log.Printf("Error updating state for Azure VM %s: %v", instanceID, err)
		}
	}

	// Handle any missing instance IDs
	for _, instanceID := range instanceIDs {
		if !foundInstances[instanceID] {
			log.Printf("[Tenant: %s] [Subscription: %s] VM %s missing from Azure response, marking as terminated.", tenantID, subscriptionID, instanceID)
			markAzureVMTerminated(queries, instanceID, ctx)
		}
	}
}

func markAzureVMTerminated(queries *db.Queries, instanceID string, ctx context.Context) {
	updateParams := db.UpdateCloudInstanceAzureStateParams{
		CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum("terminated"), Valid: true},
		CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
	}
	if err := queries.UpdateCloudInstanceAzureState(ctx, updateParams); err != nil {
		log.Printf("Error marking Azure VM %s as terminated: %v", instanceID, err)
	}
}

// mapAzurePowerStateToEnum maps Azure VM power states to our ci_state_enum values
func mapAzurePowerStateToEnum(azureState string) string {
	switch azureState {
	case "running":
		return "running"
	case "stopped":
		return "stopped"
	case "stopping":
		return "stopping"
	case "starting":
		return "pending"
	case "deallocated":
		return "stopped"
	case "deallocating":
		return "stopping"
	default:
		return "unknown"
	}
}
