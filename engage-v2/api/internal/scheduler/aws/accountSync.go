package awsScheduler

import (
	"context"
	"log/slog"

	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/service/organizations"
	"github.com/jackc/pgx/v5/pgtype"

	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/providers/aws"
)

func SyncAWSAccountStatus(ctx context.Context, queries *db.Queries, logger *slog.Logger) {
	cfg, err := config.LoadDefaultConfig(ctx)
	if err != nil {
		logger.Error("Error loading AWS config", "error", err)
		return
	}

	orgClient := organizations.NewFromConfig(cfg)

	// 1. Get all accounts from AWS Org
	awsAccounts, err := aws.ListAWSAccounts(ctx, orgClient)
	if err != nil {
		logger.Error("Error listing AWS accounts", "error", err)
		return
	}

	// 2. Build map of AWS account ID -> status
	awsAccountMap := make(map[string]string)
	for _, acc := range awsAccounts {
		if acc.Id != nil && acc.Status != "" {
			awsAccountMap[*acc.Id] = string(acc.Status)
		}
	}

	// 3. Fetch all aws_accounts with non-null cloud_account_id
	accountRows, err := queries.GetAWSAccountsAccountID(ctx)
	if err != nil {
		logger.Error("Error fetching aws accounts", "error", err)
		return
	}

	var updates []db.SetAccountAWSStatusParams

	for _, row := range accountRows {
		accountID := row.CloudAccountID.String

		newStatus := "CLOSED"
		if status, found := awsAccountMap[accountID]; found {
			newStatus = status
		} else {
			logger.Warn("AWS account not found in org, marking CLOSED", "accountID", accountID)
		}

		if !row.AccountCloudStatus.Valid || row.AccountCloudStatus.String != newStatus {
			updates = append(updates, db.SetAccountAWSStatusParams{
				ID:                 row.ID,
				AccountCloudStatus: pgtype.Text{String: newStatus, Valid: true},
			})
		}
	}

	// 4. Run updates
	for _, u := range updates {
		err := queries.SetAccountAWSStatus(ctx, u)
		if err != nil {
			logger.Error("Error updating aws account cloud status", "accountID", u.ID, "error", err)
		}
	}

	logger.Info("AWS account sync task done", "updatedCount", len(updates))
}
