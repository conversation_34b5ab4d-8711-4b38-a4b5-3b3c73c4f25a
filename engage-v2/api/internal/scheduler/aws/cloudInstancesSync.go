package awsScheduler

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"log/slog"
	"os"
	"strings"
	"sync"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/secretsmanager"
	"github.com/jackc/pgx/v5/pgtype"
	amqp "github.com/rabbitmq/amqp091-go"
	"gitlab.cyberdefense.global/cyber-defense-infrastructure/engage/engage-v2/api/internal/db"
)

// PublishCloudInstanceSyncTask publishes a simple message to trigger the sync task.
func PublishCloudInstanceSyncTask(ctx context.Context, ch *amqp.Channel, queueName string) error {
	task := map[string]string{"task": "sync_cloud_instances"}
	body, err := json.Marshal(task)
	if err != nil {
		return fmt.Errorf("failed to marshal cloud instance sync task: %w", err)
	}

	err = ch.PublishWithContext(ctx,
		"",        // exchange
		queueName, // routing key (queue name)
		false,     // mandatory
		false,     // immediate
		amqp.Publishing{
			ContentType: "application/json",
			Body:        body,
		},
	)
	if err != nil {
		return fmt.Errorf("failed to publish cloud instance sync task: %w", err)
	}
	return nil
}

// StartCloudInstanceSyncWorker starts a RabbitMQ consumer that listens for cloud instance sync tasks.
func StartCloudInstanceSyncWorker(ch *amqp.Channel, queueName string, queries *db.Queries, logger *slog.Logger) {
	q, err := ch.QueueDeclare(
		queueName, // name
		true,      // durable
		false,     // delete when unused
		false,     // exclusive
		false,     // no-wait
		nil,       // arguments
	)
	if err != nil {
		logger.Error("Failed to declare cloud instance sync queue", "error", err)
		return
	}

	msgs, err := ch.Consume(
		q.Name, // queue
		"",     // consumer
		false,  // auto-ack - we want to ack after processing
		false,  // exclusive
		false,  // no-local
		false,  // no-wait
		nil,    // args
	)
	if err != nil {
		logger.Error("Failed to register cloud instance sync consumer", "error", err)
		return
	}

	go func() {
		for msg := range msgs {
			logger.Info("Received cloud instance sync task")
			err := SyncCloudInstanceState(queries, context.Background())
			if err != nil {
				logger.Error("Error syncing cloud instance state", "error", err)
			}
			if err := msg.Ack(false); err != nil {
				logger.Error("Failed to acknowledge cloud instance sync message", "error", err)
			}
		}
	}()
}

type SubaccountCredentials struct {
	AccessKey string `json:"access_key_id"`
	SecretKey string `json:"access_key_secret"`
}

func getSecret(secretName string) (*SubaccountCredentials, error) {
	region := os.Getenv("AWS_ROOT_REGION")
	cfg, err := config.LoadDefaultConfig(context.TODO(), config.WithRegion(region))
	if err != nil {
		return nil, fmt.Errorf("failed to load AWS SDK config: %w", err)
	}

	client := secretsmanager.NewFromConfig(cfg)
	result, err := client.GetSecretValue(context.TODO(), &secretsmanager.GetSecretValueInput{
		SecretId: aws.String(secretName),
	})
	if err != nil {
		return nil, fmt.Errorf("failed to retrieve secret: %w", err)
	}

	var creds SubaccountCredentials
	err = json.Unmarshal([]byte(*result.SecretString), &creds)
	if err != nil {
		return nil, fmt.Errorf("failed to parse secret JSON: %w", err)
	}
	return &creds, nil
}

func getEC2Client(accessKey, secretKey, region string) (*ec2.Client, error) {
	cfg, err := config.LoadDefaultConfig(context.TODO(),
		config.WithCredentialsProvider(credentials.NewStaticCredentialsProvider(accessKey, secretKey, "")),
		config.WithRegion(region),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to create AWS config: %w", err)
	}
	return ec2.NewFromConfig(cfg), nil
}

func getInstanceStates(queries *db.Queries, client *ec2.Client, instanceIDs []string, accountID string) {
	if len(instanceIDs) == 0 {
		log.Printf("[Account: %s] No instances to process.", accountID)
		return
	}

	log.Printf("[Account: %s] Processing up to %d instance IDs", accountID, len(instanceIDs))

	foundInstances := make(map[string]bool)

	batchSize := 10
	for i := 0; i < len(instanceIDs); i += batchSize {
		end := i + batchSize
		if end > len(instanceIDs) {
			end = len(instanceIDs)
		}
		batch := instanceIDs[i:end]

		resp, err := client.DescribeInstances(context.TODO(), &ec2.DescribeInstancesInput{
			InstanceIds: batch,
		})
		if err != nil && strings.Contains(err.Error(), "InvalidInstanceID.NotFound") {
			log.Printf("[Account: %s] Batch had deleted instances, retrying individually...", accountID)
			for _, id := range batch {
				resp, err := client.DescribeInstances(context.TODO(), &ec2.DescribeInstancesInput{
					InstanceIds: []string{id},
				})
				if err != nil && strings.Contains(err.Error(), "InvalidInstanceID.NotFound") {
					log.Printf("[Account: %s] Instance %s no longer exists, marking as terminated.", accountID, id)
					markTerminated(queries, accountID, id)
					continue
				}
				processAWSResponse(resp, queries, accountID, foundInstances)
			}
			continue
		}

		if err != nil {
			log.Printf("[Account: %s] Unexpected DescribeInstances error: %v", accountID, err)
			continue
		}

		processAWSResponse(resp, queries, accountID, foundInstances)
	}

	// Handle any missing instance IDs - happens only if the syncing process has not been run for that long
	// that AWS deleted data about the terminated instance completely - the deleting of data seems to happen several days after
	// the real termination
	for _, id := range instanceIDs {
		if !foundInstances[id] {
			log.Printf("[Account: %s] Instance %s missing from AWS response, marking as terminated.", accountID, id)
			markTerminated(queries, accountID, id)
		}
	}
}

func processAWSResponse(resp *ec2.DescribeInstancesOutput, queries *db.Queries, accountID string, found map[string]bool) {
	for _, reservation := range resp.Reservations {
		for _, instance := range reservation.Instances {
			instanceID := *instance.InstanceId
			state := string(instance.State.Name)

			found[instanceID] = true
			log.Printf("[Account: %s] Instance %s is in state: %s", accountID, instanceID, state)

			updateParams := db.UpdateCloudInstanceAWSStateParams{
				CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum(state), Valid: true},
				CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
			}

			if err := queries.UpdateCloudInstanceAWSState(context.TODO(), updateParams); err != nil {
				log.Printf("Error updating state for %s: %v", instanceID, err)
			}
		}
	}
}

func markTerminated(queries *db.Queries, accountID, instanceID string) {
	updateParams := db.UpdateCloudInstanceAWSStateParams{
		CloudInstanceState: db.NullCiStateEnum{CiStateEnum: db.CiStateEnum("terminated"), Valid: true},
		CloudInstanceID:    pgtype.Text{String: instanceID, Valid: true},
	}
	if err := queries.UpdateCloudInstanceAWSState(context.TODO(), updateParams); err != nil {
		log.Printf("Error marking instance %s as terminated: %v", instanceID, err)
	}
}

// process Cloud Instances in parallel
func SyncCloudInstanceState(queries *db.Queries, ctx context.Context) error {
	log.Println("Syncing AWS cloud instances started")

	// Mark instances as 'failed' if they have no cloud_instance_id even after 10 min after deployemnt
	err := queries.MarkStuckInstancesAsFailed(ctx)
	if err != nil {
		log.Printf("Failed to mark stuck instances as 'failed': %v", err)
	} else {
		log.Println("Stuck instances mark step done")
	}

	// Mark instances as 'terminated' if their AWS accounts are inactive
	err = queries.MarkInstancesAsTerminatedForInactiveAccounts(ctx)
	if err != nil {
		log.Printf("Failed to mark instances as 'terminated' for inactive accounts: %v", err)
	} else {
		log.Println("Inactive accounts instances sync step done")
	}

	// Get instances whose status should be checked for updates
	instances, err := queries.GetCloudInstancesWithRegionAndSecretNameForAccount(ctx)

	if err != nil {
		log.Printf("Error fetching updatable instances: %v", err)
		return nil
	}

	// If nothing validates for updates (no cloud instance id, no ACTIVE aws account, already terminated), stop update here
	if len(instances) == 0 {
		log.Println("Nothing to sync with aws, all instances states final. Stopping sync process.")
		return nil
	}

	// Create a nested map: account → region → instances that returns:
	// map[************:map[eu-west-1:[i-034295fe21c3bebf7] eu-west-2:[i-007b97bf88c997c6a]]]
	accountInstancesMap := make(map[string]map[string][]string)
	secretMap := make(map[string]string)

	for _, instance := range instances {
		accountID := instance.CloudAccountID.String

		if accountInstancesMap[accountID] == nil {
			accountInstancesMap[accountID] = make(map[string][]string)
		}
		accountInstancesMap[accountID][instance.Region] = append(accountInstancesMap[accountID][instance.Region], instance.CloudInstanceID.String)
		secretMap[accountID] = instance.SecretID.String()
	}

	// Use Goroutines to process accounts in parallel
	var wg sync.WaitGroup

	for accountID, regions := range accountInstancesMap {

		wg.Add(1)
		go func(accountID string, regions map[string][]string) {
			defer wg.Done()

			secretName := secretMap[accountID]
			creds, err := getSecret(secretName)
			if err != nil {
				log.Printf("[Account: %s] Skipping sync due to secret retrieval failure: %v", accountID, err)
				return
			}
			log.Printf("[Account: %s] Secret retrieved successfully!", accountID)

			// Process each region
			for region, instanceIDs := range regions {
				log.Printf("[Account: %s] [Region: %s] Processing region with instances: %v", accountID, region, instanceIDs)

				wg.Add(1)
				go func(accountID, region string, instanceIDs []string) {
					defer wg.Done()

					client, err := getEC2Client(creds.AccessKey, creds.SecretKey, region)
					if err != nil {
						log.Printf("[Account: %s] [Region: %s] Skipping region due to AWS client error: %v", accountID, region, err)
						return
					}
					log.Printf("[Account: %s] [Region: %s] EC2 client initialized", accountID, region)
					getInstanceStates(queries, client, instanceIDs, accountID)
					log.Printf("[Account: %s] [Region: %s] Instance state retrieval completed.", accountID, region)
				}(accountID, region, instanceIDs)
			}
		}(accountID, regions)
	}
	wg.Wait()

	log.Println("Instances sync process done")

	return nil
}
