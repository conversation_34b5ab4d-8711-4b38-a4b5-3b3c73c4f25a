-- Clients
INSERT INTO clients (id, name)
VALUES ('ccd5e335-fdc4-4232-a940-0e6b9abd3996', 'ACME');

-- Engagements
INSERT INTO engagements (id, title, wbs_code, is_active, client_id, created_at, updated_at, status, error_message)
VALUES ('fe1619c5-dfd5-4d1e-ae51-74b51c307d6e', 'Engagement 1', 'WBS0001', true, 'ccd5e335-fdc4-4232-a940-0e6b9abd3996',
        '2025-07-14 15:30:00', '2025-07-14 15:34:00', 'SUCCESS', null);

INSERT INTO engagements (id, title, wbs_code, is_active, client_id, created_at, updated_at, status, error_message)
VALUES ('fe1619c5-dfd5-4d1e-ae51-74b51c307d7e', 'Engagement 2', 'WBS0002', true, 'ccd5e335-fdc4-4232-a940-0e6b9abd3996',
        '2025-07-14 15:30:00', '2025-07-14 15:35:00', 'SUCCESS', null);

-- Users
INSERT INTO users (id, username, full_name)
VALUES ('52556ed5-a80c-4364-aa79-224b18141385', '<EMAIL>', 'Gabor Csepregi');
INSERT INTO users (id, username, full_name)
VALUES ('cdfb2590-b9b4-47ec-b2ea-3463a4da44f6', '<EMAIL>', 'Angelos Panagiotopoulos');
INSERT INTO users (id, username, full_name)
VALUES ('40eeb83c-746f-4832-9b71-dac3374baacf', '<EMAIL>', 'Elizabeth Wun');

-- Node Groups
INSERT INTO public.node_groups (id, name, engagement_id, is_active)
VALUES ('9a7e965f-43ed-434b-bf08-059e8dca0111', 'Node Group 1', 'b4ccc447-46bc-465d-8526-621f1cab1c8b', true);
INSERT INTO public.node_groups (id, name, engagement_id, is_active)
VALUES ('19360f1e-bf6e-4153-9ff8-cacb8ff3e823', 'Node Group 2', 'b4ccc447-46bc-465d-8526-621f1cab1c8b', true);

-- Cloud Instance 1
INSERT INTO public.nodes (id, node_type, name, node_group_id)
VALUES ('a58a3afc-c34e-440d-ba75-2045bb0c7577', 'CLOUD_INSTANCE', 'Cloud Instance 1',
        '9a7e965f-43ed-434b-bf08-059e8dca0111');
INSERT INTO public.node_type_cloud_instances (provider, region, operating_system_image_id, size, name, node_id,
                                              open_ports)
VALUES ('AWS', 'eu-west-2', 'ami-08fd64ff859518952', 'small', 'Cloud Instance 1',
        'a58a3afc-c34e-440d-ba75-2045bb0c7577', '{22,80}');

-- Cloud Instance 2
INSERT INTO public.nodes (id, node_type, name, node_group_id)
VALUES ('6ef90edb-3adf-4eb2-adeb-74d82c0155eb', 'CLOUD_INSTANCE', 'Cloud Instance 2',
        '9a7e965f-43ed-434b-bf08-059e8dca0111');
INSERT INTO public.node_type_cloud_instances (provider, region, operating_system_image_id, size, name, node_id,
                                              open_ports)
VALUES ('AWS', 'ap-south-1', 'ami-076c2bb3fcb4ca39b', 'medium', 'Cloud Instance 2',
        '6ef90edb-3adf-4eb2-adeb-74d82c0155eb', '{22,80}');

-- Email Address
INSERT INTO public.nodes (id, node_type, name, node_group_id)
VALUES ('962d5baa-6d73-4caa-b343-af32a3819805', 'EMAIL_ADDRESS', '<EMAIL>',
        '9a7e965f-43ed-434b-bf08-059e8dca0111');
INSERT INTO public.node_type_email_addresses (email_address, node_id)
VALUES ('<EMAIL>', '962d5baa-6d73-4caa-b343-af32a3819805');

-- Host
INSERT INTO public.nodes (id, node_type, name, node_group_id)
VALUES ('29ffb3ac-18a8-499e-ba5d-442f89c25175', 'HOST', 'Host A',
        '9a7e965f-43ed-434b-bf08-059e8dca0111');
INSERT INTO public.node_type_hosts(name, ip_addresses, alternative_names, node_id)
VALUES ('Host A', '{127.0.0.1, ***********}', '{Host A1, Host A2}', '29ffb3ac-18a8-499e-ba5d-442f89c25175');

-- Person
INSERT INTO public.nodes (id, node_type, name, node_group_id)
VALUES ('48955848-1ee7-4d3d-8bdf-b48e75627ffc', 'PERSON', 'John Doe',
        '9a7e965f-43ed-434b-bf08-059e8dca0111');
INSERT INTO public.node_type_persons (first_name, last_name, email, company, title, node_id)
VALUES ('John', 'Doe', '<EMAIL>', 'ACME', 'CEO', '48955848-1ee7-4d3d-8bdf-b48e75627ffc');

-- URL
INSERT INTO public.nodes (id, node_type, name, node_group_id)
VALUES ('0955f7fc-bed1-4e27-a77f-8d33012b8294', 'URL', 'https://www.accenture.com',
        '9a7e965f-43ed-434b-bf08-059e8dca0111');
INSERT INTO public.node_type_urls (url, node_id)
VALUES ('https://www.accenture.com', '0955f7fc-bed1-4e27-a77f-8d33012b8294');

-- Scripts
INSERT INTO scripts (name, description, content, script_type, created_at, updated_at, user_id)
VALUES ('Python 3', 'Install Python 3', 'sudo apt update -y && sudo apt install -y python3 python3-pip', 'ADMIN',
        CURRENT_TIMESTAMP, CURRENT_TIMESTAMP, '52556ed5-a80c-4364-aa79-224b18141385'),
       ('Deno', 'Install Deno', 'curl -fsSL https://deno.land/install.sh | sh', 'STANDARD', CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP, 'cdfb2590-b9b4-47ec-b2ea-3463a4da44f6'),
       ('Go', 'Install Go', 'sudo apt update -y && sudo apt install -y golang', 'ADMIN', CURRENT_TIMESTAMP,
        CURRENT_TIMESTAMP,
        '40eeb83c-746f-4832-9b71-dac3374baacf');

-- Size mappings
INSERT INTO public.instance_size_mappings (provider, size_alias, priority, instance_type)
VALUES ('AWS', 'small', 1, 't2.micro');
INSERT INTO public.instance_size_mappings (provider, size_alias, priority, instance_type)
VALUES ('AWS', 'medium', 1, 't2.medium');
INSERT INTO public.instance_size_mappings (provider, size_alias, priority, instance_type)
VALUES ('AWS', 'large', 1, 't2.large');
INSERT INTO public.instance_size_mappings (provider, size_alias, priority, instance_type)
VALUES ('AWS', 'small', 2, 't3.micro');
INSERT INTO public.instance_size_mappings (provider, size_alias, priority, instance_type)
VALUES ('AWS', 'medium', 2, 't3.medium');
INSERT INTO public.instance_size_mappings (provider, size_alias, priority, instance_type)
VALUES ('AWS', 'large', 2, 't3.large');

-- Terraform templates 
INSERT INTO terraform_templates (name, content)
VALUES ('aws_ec2_instance',
        'terraform {
  backend "pg" {
    conn_str = "postgres://engage:engage@localhost/engage?sslmode=disable"
  }
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 4.16"
    }
  }

  required_version = ">= 1.2.0"
}

provider "aws" {
  region     = "{{ .Region }}"
  access_key = "{{ .AccessKey }}"
  secret_key = "{{ .SecretKey }}"
}

resource "aws_key_pair" "ssh_key" {
  key_name = "{{ .DeploymentID }}-ssh-public-key"
  public_key = trimspace("{{ .SshPublicKey }}")
}

resource "aws_instance" "app_server" {
  ami           = "{{ .OperatingSystemImageID }}"
  instance_type = "{{ .InstanceType }}"
  key_name      = aws_key_pair.ssh_key.key_name

  root_block_device {
    encrypted = true
  }
  tags = {
    Name = "{{ .Name }}"
  }

  vpc_security_group_ids = [aws_security_group.engagement_ec2_security_group.id]
  user_data_base64 = "{{ .StartupScript }}"
}

resource "aws_security_group" "engagement_ec2_security_group" {
  name        = "{{ .DeploymentID }}-sg"
  description = "Deployment {{ .DeploymentID }} Security Group"

  {{range .OpenIngressTcpPorts}}
    ingress {
      from_port   = {{.}}
      to_port     = {{.}}
      protocol    = "tcp"
      cidr_blocks = ["0.0.0.0/0"]
    }
  {{end}}

  egress {
    from_port   = 0
    to_port     = 65535
    protocol    = "tcp"
    cidr_blocks = ["0.0.0.0/0"]
  }
}

output "instance_public_ipv4" {
  value = aws_instance.app_server.public_ip
  description = "The public IP address of the instance"
}
output "instance_id" {
  value       = aws_instance.app_server.id
  description = "The ID of the EC2 instance"
}
');

INSERT INTO terraform_templates (name, content)
VALUES ('azure_vm_instance',
        'terraform {
  backend "pg" {
    conn_str = "postgres://engage:engage@localhost/engage?sslmode=disable"
  }

  required_providers {
    azurerm = {
      source  = "hashicorp/azurerm"
      version = "~> 3.0"
    }
  }

  required_version = ">= 1.2.0"
}

provider "azurerm" {
  features {}
  subscription_id = "{{ .SubscriptionID }}"
  client_id       = "{{ .ClientID }}"
  client_secret   = "{{ .ClientSecret }}"
  tenant_id       = "{{ .TenantID }}"
}

resource "azurerm_resource_group" "rg" {
  name     = "{{ .DeploymentID }}-rg"
  location = "{{ .Region }}"
}

resource "azurerm_virtual_network" "vnet" {
  name                = "{{ .DeploymentID }}-vnet"
  address_space       = ["10.0.0.0/16"]
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name
}

resource "azurerm_subnet" "subnet" {
  name                 = "{{ .DeploymentID }}-subnet"
  resource_group_name  = azurerm_resource_group.rg.name
  virtual_network_name = azurerm_virtual_network.vnet.name
  address_prefixes     = ["********/24"]
}

resource "azurerm_network_security_group" "nsg" {
  name                = "{{ .DeploymentID }}-nsg"
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name

  {{ range .IngressRules }}
    security_rule {
      name                       = "allow-port-{{ .Port }}"
      priority                   = {{ .Priority }}
      direction                  = "Inbound"
      access                     = "Allow"
      protocol                   = "Tcp"
      source_port_range          = "*"
      destination_port_range     = "{{ .Port }}"
      source_address_prefix      = "*"
      destination_address_prefix = "*"
    }
  {{ end }}

  security_rule {
    name                       = "allow-egress-all"
    priority                   = 100
    direction                  = "Outbound"
    access                     = "Allow"
    protocol                   = "*"
    source_port_range          = "*"
    destination_port_range     = "*"
    source_address_prefix      = "*"
    destination_address_prefix = "*"
  }
}

resource "azurerm_network_interface" "nic" {
  name                = "{{ .DeploymentID }}-nic"
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name

  ip_configuration {
    name                          = "ipconfig"
    subnet_id                     = azurerm_subnet.subnet.id
    private_ip_address_allocation = "Dynamic"
    public_ip_address_id          = azurerm_public_ip.pip.id
  }
}

resource "azurerm_public_ip" "pip" {
  name                = "{{ .DeploymentID }}-pip"
  location            = azurerm_resource_group.rg.location
  resource_group_name = azurerm_resource_group.rg.name
  allocation_method   = "Dynamic"
}

resource "azurerm_network_interface_security_group_association" "nic_nsg" {
  network_interface_id      = azurerm_network_interface.nic.id
  network_security_group_id = azurerm_network_security_group.nsg.id
}

resource "azurerm_linux_virtual_machine" "vm" {
  name                            = "{{ .Name }}"
  resource_group_name             = azurerm_resource_group.rg.name
  location                        = azurerm_resource_group.rg.location
  size                            = "{{ .InstanceType }}"
  admin_username                  = "azureuser"
  disable_password_authentication = true
  network_interface_ids           = [azurerm_network_interface.nic.id]

  admin_ssh_key {
    username   = "azureuser"
    public_key = trimspace("{{ .SshPublicKey }}")
  }

  os_disk {
    name                 = "{{ .DeploymentID }}-osdisk"
    caching              = "ReadWrite"
    storage_account_type = "Standard_LRS"
  }

  source_image_reference {
    publisher = "{{ .ImagePublisher }}"
    offer     = "{{ .ImageOffer }}"
    sku       = "{{ .ImageSKU }}"
    version   = "{{ .ImageVersion }}"
  }

  custom_data = base64encode("{{ .StartupScript }}")
}

output "instance_public_ipv4" {
  value       = azurerm_public_ip.pip.ip_address
  description = "The public IP address of the instance"
}

output "instance_id" {
  value       = azurerm_linux_virtual_machine.vm.id
  description = "The ID of the Azure VM"
}
');

