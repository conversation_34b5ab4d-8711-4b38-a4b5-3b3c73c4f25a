import React, { createContext, useContext } from "react";

type RolesContextType = string[];

const RolesContext = createContext<RolesContextType | undefined>(undefined);

export const RolesProvider: React.FC<{
  roles: string[];
  children: React.ReactNode;
}> = ({ roles, children }) => {
  return (
    <RolesContext.Provider value={roles}>{children}</RolesContext.Provider>
  );
};

export const useRoles = () => {
  const context = useContext(RolesContext);
  if (!context) {
    throw new Error("useRoles must be used within a RolesProvider");
  }
  return context;
};
