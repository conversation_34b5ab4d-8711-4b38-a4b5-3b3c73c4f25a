export type CustomType = 'custom_alpha' | 'custom_beta' | 'custom_gamma' | 'custom_delta';
export type CategoryType = "small" | "medium" | "large" | `custom_${CustomType}`;
export type OptionType = "primary" | "secondary" | "fallback";

export type InstanceTypeSelections = {
  [K in CategoryType]: {
    [P in OptionType]: string | null;
  };
};

export type CustomRegions = {
  [K in `${CustomType}`]: string;
};