.custom-context-menu {
  border: none !important;
  background-color: #fafafa !important;
  box-shadow:
    0px 0px 8px 0px rgba(0, 0, 0, 0.12),
    0px 8px 8px 0px rgba(0, 0, 0, 0.24) !important;
}

.custom-menu-item {
  width: 140px !important;
  border: none !important;
  height: 32px !important;
  padding-left: 12px !important;
  color: rgba(0, 0, 0, 0.87) !important;
  background-color: #fafafa !important;
  font-weight: normal !important;
  text-align: left !important;
  box-shadow: none !important;
}

.custom-menu-item:hover {
  background-color: #9333ea !important;
  color: #ffffff !important;
}

.custom-context-menu-background {
  border: none !important;
  box-shadow:
    0px 0px 8px 0px rgba(0, 0, 0, 0.12),
    0px 8px 8px 0px rgba(0, 0, 0, 0.24) !important;
  position: absolute !important;
  background-color: #fafafa;
  z-index: 9999;
  display: none;
}

.p-dropdown.p-inputwrapper-focus,
.p-dropdown:not(.p-disabled).p-focus {
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 1) !important;
  outline: none !important;
}

.p-dropdown .p-dropdown-label {
  padding: 8px 16px;
}

.p-dropdown-item,
.p-dropdown-item .p-focus,
.p-dropdown-panel .p-dropdown-header .p-dropdown-filter,
.p-dropdown-empty-message {
  padding: 6px;
}

.p-dropdown-panel .p-dropdown-items .p-dropdown-item .p-highlight {
  background: rgb(147 51 234);
  color: white;
  border-radius: 2px;
}

.p-dropdown-panel .p-dropdown-header .p-dropdown-filter-icon {
  top: 0.5rem;
}

.p-inputtext:enabled:focus,
.p-multiselect:not(.p-disabled).p-focus,
.p-chips:not(.p-disabled).p-focus .p-chips-multiple-container {
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 1) !important;
  outline: none !important;
}

.p-multiselect-filter-container {
  padding: 0px 8px;
}

.p-multiselect-header {
  padding: 4px;
}

.p-multiselect-token {
  padding: 2px 8px;
}

.p-multiselect-token-label {
  margin-right: 4px;
}

.p-multiselect-item {
  padding: 6px;
}

.p-multiselect .p-multiselect-label {
  padding: 8px 16px;
}

.p-checkbox.p-highlight .p-checkbox-box {
  background: rgb(147 51 234);
}

.p-multiselect-panel .p-multiselect-items .p-multiselect-item.p-highlight,
.p-dropdown-panel .p-dropdown-items .p-dropdown-item.p-highlight.p-focus {
  background: rgb(243 232 255);
  color: black;
  padding: 6px;
}

.p-multiselect-panel
  .p-multiselect-header
  .p-multiselect-filter-container
  .p-multiselect-filter-icon {
  top: 0.3rem;
}

.p-multiselect.p-multiselect-chip .p-multiselect-token,
.p-multiselect-panel
  .p-multiselect-items
  .p-multiselect-item:not(.p-highlight):not(.p-disabled).p-focus {
  background-color: rgb(241 245 249);
}

.dark .p-multiselect-panel,
.dark .p-multiselect-header,
.dark .p-dropdown-items,
.dark .p-dropdown-header {
  background-color: #64748b;
  color: white;
}

.dark
  .p-multiselect-panel
  .p-multiselect-items
  .p-multiselect-item.p-highlight {
  background: #d8b4fe80;
  color: white;
  padding: 6px;
}

.dark
  .p-multiselect-panel
  .p-multiselect-items
  .p-multiselect-item:not(.p-highlight):not(.p-disabled).p-focus,
.dark
  .p-multiselect
  .p-component
  .p-inputwrapper
  .p-multiselect-chip
  .p-inputwrapper-focus {
  background-color: #1f2937;
  color: white;
}

.dark .p-dropdown,
.dark .p-multiselect {
  background-color: #475569;
  border: transparent;
}

.dark .p-multiselect-item {
  color: #f1f5f9;
  padding: 6px;
}

.dark .p-multiselect-label .p-placeholder,
.dark .p-dropdown-label .p-inputtext .p-placeholder {
  color: #f1f5f9;
}

.dark .p-chips-input-token {
  background-color: #475569;
  border: transparent;
  color: #f1f5f9;
}

.dark .p-inputtext {
  background: transparent;
}

.custom-dark-context-menu-background {
  border: none !important;
  box-shadow:
    0px 0px 8px 0px rgba(0, 0, 0, 0.12),
    0px 8px 8px 0px rgba(0, 0, 0, 0.24) !important;
  position: absolute !important;
  background-color: #404e65;
  z-index: 9999;
  display: none;
}

.custom-dark-menu-item {
  width: 145px !important;
  border: none !important;
  height: 32px !important;
  padding-left: 12px !important;
  color: #fafafa !important;
  background-color: #404e65 !important;
  font-weight: normal !important;
  text-align: left !important;
  box-shadow: none !important;
}

.custom-dark-menu-item:hover {
  background-color: #9333ea !important;
  color: #ffffff !important;
}

.tooltip {
  @apply invisible absolute;
}

.has-tooltip:hover .tooltip {
  @apply visible z-50;
}
