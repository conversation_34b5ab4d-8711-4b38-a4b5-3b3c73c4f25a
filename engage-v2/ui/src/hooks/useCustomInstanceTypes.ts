import { useQuery } from '@tanstack/react-query';
import { getAwsInstanceTypes, getGetAwsInstanceTypesQueryKey } from '../client';
import { CustomType } from "../types";

export const useCustomInstanceTypes = (type: CustomType, region: string) => {
  return useQuery({
    queryKey: getGetAwsInstanceTypesQueryKey(region, { type }),
    queryFn: () => getAwsInstanceTypes(region, {type}),
    enabled: !!region,
    select: (data) => ({
      instance_types: data.instance_types || [],
      validation: data.validation || [],
      mappings: data.mappings || []
    })
  });
};
