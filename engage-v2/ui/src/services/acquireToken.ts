import { InteractionRequiredAuthError } from "@azure/msal-browser";

import { msalInstance } from "../msalInstance";

export const acquireToken = async (): Promise<string | null> => {
  const account = msalInstance.getAllAccounts()[0];
  if (!account) {
    console.log("No account found.");
    return null;
  }

  const accessTokenRequest = {
    scopes: [import.meta.env.VITE_AZURE_API_SCOPE],
    account: account,
  };

  try {
    const response = await msalInstance.acquireTokenSilent(accessTokenRequest);
    return response.accessToken;
  } catch (error) {
    if (error instanceof InteractionRequiredAuthError) {
      await msalInstance.acquireTokenRedirect(accessTokenRequest);
    } else {
      console.log("Unexpected token acquisition error:", error);
    }
  }
  return null;
};
