import { acquireToken } from "../acquireToken";

interface ApiResponse {
  sshKey?: string;
  sshKeyLabel?: string;
}

interface ClientError {
  type: string;
  message: string;
  errors?: [];
  status: string;
}

export const client = (
  method: string,
  endpoint: string,
  body?: object,
): Promise<ApiResponse | ClientError> => {
  return acquireToken()
    .then((accessToken) => {
      const config = {
        method: method,
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${accessToken}`,
        },
        ...(body && { body: JSON.stringify(body) }),
      };

      return fetch(`${import.meta.env.VITE_BASE_URL_API}/${endpoint}`, config);
    })
    .then((fetchResult) => {
      if (
        fetchResult.status === 204 ||
        fetchResult.headers.get("content-length") === "0"
      ) {
        return {};
      }

      return fetchResult.json().then((result) => {
        if (fetchResult.ok) {
          return result;
        }
        throw {
          type: "Error",
          message: result.detail || result.message || "Something went wrong",
          errors: result.errors || null, // Capture the errors array from the response
          status: result.status || fetchResult.status || "",
        };
      });
    })
    .catch((error) => {
      if (error.status) {
        throw error;
      }
      const networkError = {
        type: "NetworkError",
        message: error.message || "Failed to fetch the request",
        data: null,
        code: 500,
        originalError: error,
      };

      throw networkError;
    });
};
