import { useSuspenseQuery } from "@tanstack/react-query";
import { Link, createFileRoute } from "@tanstack/react-router";
import { format } from "date-fns/format";

import {
  getDeployment,
  getGetDeploymentQueryKey,
  getGetDeploymentQueryOptions,
} from "../client";
import ErrorPage from "../components/ErrorHandling/ErrorPage";
import NotFound from "../components/ErrorHandling/NotFound";
import HighlightedTextarea from "../components/HighlightedTextArea";
import {
  capitalizeFirstLetterEachWord,
  errorCode,
  getStatusColor,
  getStatusIcon,
  getUserBgColor,
} from "../utils/assets";

export const Route = createFileRoute("/deployments/$deploymentId")({
  errorComponent: ({ error }: any) => {
    const status = error?.status in errorCode ? error?.status : 500;
    const errorData = errorCode[status] || errorCode[500];
    return (
      <ErrorPage
        code={status}
        title={errorData.title}
        description={errorData.description}
        colour={errorData.colour}
      />
    );
  },
  notFoundComponent: () => {
    return <NotFound />;
  },
  component: DeploymentDetails,
  loader: async ({ context: { queryClient }, params }) => {
    const getDeploymentDetails = queryClient.ensureQueryData(
      getGetDeploymentQueryOptions(params.deploymentId),
    );
    const [deploymentData] = await Promise.all([getDeploymentDetails]);
    return { deploymentData };
  },
});

function DeploymentDetails() {
  const { deploymentId } = Route.useParams();
  const engagementQueryKey = getGetDeploymentQueryKey(deploymentId);
  const queryFn = () => getDeployment(deploymentId);
  const { data: deploymentList } = useSuspenseQuery({
    queryKey: engagementQueryKey,
    queryFn,
  });
  const deployment = deploymentList.deployment || {};
  const bgColor = getUserBgColor(deployment.user_id);

  return (
    <>
      <div className="mt-2 flex flex-col md:flex-row md:space-x-4">
        <div
          className={`flex w-fit flex-row content-center items-center rounded-full px-2 py-1 pr-3 shadow-md ${getStatusColor(deployment.status)}`}
        >
          {getStatusIcon(deployment.status)}
          {capitalizeFirstLetterEachWord(deployment.status)}
        </div>
        <div className="mt-4 flex flex-col content-center items-start md:mt-0 md:flex-row md:items-center md:space-x-2">
          <span className="dark:text-white">Created by </span>
          <div className="mt-1 flex flex-row items-center space-x-2 md:mt-0">
            <div
              className={`${bgColor} h-5 w-5 cursor-pointer content-center rounded-full text-center text-xs text-white`}
            >
              {deployment.username.charAt(0).toUpperCase()}
            </div>
            <span className="font-semibold dark:text-slate-400">
              {deployment.username}
            </span>
          </div>
        </div>
      </div>
      <div className="mt-2 flex flex-row justify-between">
        <div className="flex flex-col space-y-2">
          <span className="flex text-sm font-normal text-gray-400 dark:text-white">
            Created on:{" "}
            {format(
              new Date(`${deployment.created_at}`),
              "MMMM dd, yyyy | HH:mm aa",
            )}
          </span>
        </div>
      </div>
      <div className="mt-10 flex flex-col md:flex-row md:space-x-4">
        <span className="w-28 text-sm font-semibold text-black dark:text-white">
          Deployment ID
        </span>
        <span className="md:w-100 dark:text-slate-400">{deployment.id}</span>
      </div>
      <div className="mt-4 flex flex-col space-x-4 md:mt-0 md:flex-row">
        <span className="w-28 text-sm font-semibold text-black dark:text-white">
          Engagement ID
        </span>
        <span className="md:w-100 dark:text-slate-400">
          {deployment.engagement_id}
        </span>
        <span className="font-semibold text-purple-800 dark:text-purple-500">
          <Link
            to={`/engagements/$engagementId`}
            params={{ engagementId: deployment.engagement_id }}
            className="hover:text-purple-600"
          >
            [{deployment.title}]
          </Link>
        </span>
      </div>
      <div className="mt-4 flex flex-col md:mt-0 md:flex-row md:space-x-4">
        <span className="w-28 text-sm font-semibold text-black dark:text-white">
          Node ID
        </span>
        <span className="md:w-100 dark:text-slate-400">
          {deployment.node_id}
        </span>
        <span className="font-semibold text-purple-800 dark:text-purple-500">
          [{deployment.node_name}]
        </span>
      </div>
      <div className="container mx-auto mt-6 space-y-4">
        {deployment.status === "WARNING" && (
          <div className="space-y-4">
            <span className="text-sm font-semibold text-black dark:text-white">
              Warnings
            </span>
            <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-[#374357b5]">
              {deployment.warnings?.map((line, index) => (
                <div key={index}>{line}</div>
              ))}
            </div>
          </div>
        )}
        {deployment.status === "ERROR" && (
          <div className="space-y-4">
            <span className="text-sm font-semibold text-black dark:text-white">
              Error Message
            </span>
            <div className="rounded-lg bg-white p-4 shadow-sm dark:bg-[#374357b5] dark:text-white">
              {deployment.error_message}
            </div>
          </div>
        )}
      </div>
      <div className="container mx-auto mt-6 space-y-4">
        <div className="text-sm font-semibold text-black dark:text-white">
          Terraform Module
        </div>
        <div className="rounded-lg bg-white p-2 shadow-sm dark:bg-[#374357b5]">
          <HighlightedTextarea value={deployment.terraform_module} />
        </div>
      </div>
    </>
  );
}
