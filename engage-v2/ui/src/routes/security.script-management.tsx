import { Disclosure, DisclosureButton } from "@headlessui/react";
import { useQueryClient, useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { AxiosError } from "axios";
import { format } from "date-fns";
import { useRef, useState } from "react";
import { HiOutlinePencil, HiOutlineTrash } from "react-icons/hi";
import { RiAdminLine } from "react-icons/ri";
import { toast } from "react-toastify";

import {
  getAdminScripts,
  getGetAdminScriptsQueryKey,
  getGetAdminScriptsQueryOptions,
  useEditAdminScript,
  usePostAdminScript,
} from "../client";
import { ActionButtons, ButtonProps } from "../components/ActionButtons";
import AddAdminScriptForm from "../components/AdminScripts/AddAdminScriptForm";
import DeleteAdminScriptModal from "../components/AdminScripts/DeleteAdminScriptModal";
import EditAdminScriptForm from "../components/AdminScripts/EditAdminScriptForm";
import BreadCrumbs from "../components/BreadCrumbs";
import ErrorPage from "../components/ErrorHandling/ErrorPage";
import NotFound from "../components/ErrorHandling/NotFound";
import ErrorMessageModal, {
  ErrorMessage,
} from "../components/ErrorMessageModal";
import Header from "../components/Header";
import ResponsiveSideNav from "../components/ResponsiveSideNav";
import ViewScriptModal from "../components/UserSettings/ViewScriptModal";
import {
  AdminScript,
  CreateAdminScriptInputBody,
  EditAdminScriptInputBody,
} from "../model";
import { errorCode } from "../utils/assets";

export const Route = createFileRoute("/security/script-management")({
  component: AdminScripts,
  loader: async ({ context: { queryClient } }) => {
    return queryClient.ensureQueryData(getGetAdminScriptsQueryOptions());
  },
  errorComponent: ({ error }: any) => {
    const status = error?.status in errorCode ? error?.status : 500;
    const errorData = errorCode[status] || errorCode[500];
    return (
      <ErrorPage
        code={status}
        title={errorData.title}
        description={errorData.description}
        colour={errorData.colour}
      />
    );
  },
  notFoundComponent: () => {
    return <NotFound />;
  },
});

function AdminScripts() {
  const queryClient = useQueryClient();
  const adminScriptsQueryKey = getGetAdminScriptsQueryKey();
  const adminScriptsQueryFn = () => getAdminScripts();
  const { data: adminScriptsData } = useSuspenseQuery({
    queryKey: adminScriptsQueryKey,
    queryFn: adminScriptsQueryFn,
  });

  const scripts = adminScriptsData.scripts || [];

  const createScriptFormRef = useRef<HTMLDivElement>(null);
  const [showScriptForm, setShowScriptForm] = useState<boolean>(false);
  const [openId, setOpenId] = useState<string | null>(null);
  const [selectedScript, setSelectedScript] = useState<AdminScript | null>(
    null,
  );
  const [isViewScriptModal, setIsViewScriptModal] = useState<boolean>(false);
  const [isOpenDeleteModal, setIsOpenDeleteModal] = useState<boolean>(false);
  const [isOpenEditModal, setIsOpenEditModal] = useState<boolean>(false);
  const [isOpenErrorModal, setIsOpenErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);
  const [isSideNavOpen, setIsSideNavOpen] = useState(false);
  const toggleSideNav = () => setIsSideNavOpen((prev) => !prev);

  const secondaryScriptButton: ButtonProps = {
    label: "Create New Admin Script",
    onClick: () => {
      setShowScriptForm(true);
      setTimeout(() => {
        if (createScriptFormRef.current) {
          createScriptFormRef.current.scrollIntoView({ behavior: "smooth" });
        }
      }, 100);
    },
    variant: "secondary",
  };

  const handleDisclosureToggle = (id: string) => {
    setOpenId(id);
    setIsViewScriptModal(true);
  };

  const openEditScriptForm = (selectedScriptId: string) => {
    const selectedScript =
      scripts?.find((script: AdminScript) => script.id === selectedScriptId) ||
      null;
    setSelectedScript(selectedScript);
    setIsOpenEditModal(true);
    setShowScriptForm(false);
  };

  const openDeleteModal = (selectedScriptId: string) => {
    const selectedScript =
      scripts?.find((script: AdminScript) => script.id === selectedScriptId) ||
      null;
    setSelectedScript(selectedScript);
    setIsOpenDeleteModal(true);
  };

  const createScriptMutation = usePostAdminScript({
    mutation: {
      onError: (error: unknown) => {
        if (error instanceof AxiosError && error.response) {
          const errorMessage =
            error.response.data?.errors?.[0]?.message ??
            "An error occurred while creating admin script.";
          toast.error(errorMessage);
        } else {
          toast.error("An unexpected error occurred.");
        }
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Admin script has been successfully created.");
        }
        const queryKey = getGetAdminScriptsQueryKey();
        queryClient.invalidateQueries({ queryKey });
      },
    },
  });

  const handleCreateScript = (data: CreateAdminScriptInputBody) => {
    createScriptMutation.mutate({
      data,
    });
  };

  const editScriptMutation = useEditAdminScript({
    mutation: {
      onError: () => {
        openErrorModal({
          title: "Error editing Admin script.",
          message: "Please try again later.",
        });
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Admin script has been successfully edited.");
        }
        const queryKey = getGetAdminScriptsQueryKey();
        queryClient.invalidateQueries({ queryKey });
        setSelectedScript(null);
      },
    },
  });

  const handleEditScript = (
    scriptId: string,
    data: EditAdminScriptInputBody,
  ) => {
    editScriptMutation.mutate({
      scriptId: scriptId,
      data,
    });
  };

  function openErrorModal(errorMessage: ErrorMessage) {
    if (errorMessage) {
      setErrorMessage(errorMessage);
    }
    setIsOpenErrorModal(true);
  }

  function closeErrorModal() {
    setIsOpenErrorModal(false);
  }

  return (
    <div className="flex h-full w-full">
      <ResponsiveSideNav
        toggleSideNav={toggleSideNav}
        isSideNavOpen={isSideNavOpen}
      />
      <div className="flex w-full flex-col">
        <Header toggleSideNav={toggleSideNav} />
        <div className="flex h-full flex-col bg-slate-50 p-6 dark:bg-slate-800">
          <BreadCrumbs />
          <div
            id="engagements-page-main-title"
            className="flex flex-col space-y-2 py-4"
          >
            <span className="text-3xl font-semibold text-black dark:text-white">
              Script Management
            </span>
          </div>
          <div className="container mx-auto mt-10 rounded-lg bg-white p-8 shadow-sm dark:bg-[#374357b5]">
            <div className="flex flex-col space-y-2 md:flex-row md:justify-between">
              <div className="content-center text-2xl font-semibold text-black dark:text-white">
                Admin Scripts
              </div>
              <ActionButtons secondaryButton={secondaryScriptButton} />
            </div>
            <div className="mt-4">
              <ul className="max-h-[calc(7*3rem)] list-none overflow-y-auto">
                <li className="sticky top-0 z-10 mb-2 flex w-full border-b-2 border-black bg-white p-2 font-bold dark:border-slate-200 dark:bg-[#303c4f] dark:text-white">
                  <div className="w-full text-left sm:w-1/4">Name</div>
                  <div className="w-full text-left sm:w-1/2">Description</div>
                  <div className="w-full text-left sm:w-1/4">Creation Date</div>
                </li>
                <Disclosure as="div">
                  {scripts
                    ? scripts
                        ?.slice()
                        .sort(
                          (
                            a: { created_at: string },
                            b: { created_at: string },
                          ) =>
                            new Date(a.created_at).getTime() -
                            new Date(b.created_at).getTime(),
                        )
                        .map((script: AdminScript) => (
                          <div key={script.id}>
                            <li className="flex p-2">
                              <div
                                className={`w-full md:w-1/4 ${script.script_type === "ADMIN" ? "text-sky-500 dark:text-sky-300" : "text-purple-500 dark:text-purple-300"}`}
                              >
                                <DisclosureButton
                                  onClick={() =>
                                    handleDisclosureToggle(script.id)
                                  }
                                  className="flex cursor-pointer text-left"
                                  as="div"
                                >
                                  <RiAdminLine className="mr-4 h-5 w-5 text-sky-600 dark:text-sky-300" />
                                  {script.name || "-"}
                                </DisclosureButton>
                              </div>
                              <div className="w-full text-left md:w-1/2 dark:text-slate-400">
                                {script.description &&
                                script.description.length > 100
                                  ? `${script.description.slice(0, 50)}...`
                                  : script.description || "-"}
                              </div>
                              <div className="flex w-full justify-between md:w-1/4">
                                <div className="text-left dark:text-slate-400">
                                  {format(
                                    new Date(script.created_at),
                                    "dd-MMM-yyyy, HH:mm",
                                  )}
                                </div>
                                <div className="flex space-x-2 text-left dark:text-slate-200">
                                  <HiOutlinePencil
                                    className="h-5 w-5 cursor-pointer"
                                    onClick={() => {
                                      openEditScriptForm(script.id);
                                    }}
                                  />
                                  <HiOutlineTrash
                                    className="h-5 w-5 cursor-pointer"
                                    onClick={() => openDeleteModal(script.id)}
                                  />
                                </div>
                              </div>
                            </li>
                            {openId === script.id && (
                              <ViewScriptModal
                                isOpen={isViewScriptModal}
                                closeModal={() => setIsViewScriptModal(false)}
                                scriptDetails={script}
                              />
                            )}
                          </div>
                        ))
                    : null}
                </Disclosure>
              </ul>
            </div>
            {showScriptForm && (
              <div ref={createScriptFormRef}>
                <AddAdminScriptForm
                  setShowScriptForm={setShowScriptForm}
                  handleCreateScript={handleCreateScript}
                />
              </div>
            )}
            {isOpenEditModal && selectedScript && (
              <EditAdminScriptForm
                handleEditScript={handleEditScript}
                script={selectedScript}
                isOpen={isOpenEditModal && selectedScript ? true : false}
                closeModal={() => setIsOpenEditModal(false)}
              />
            )}
            {isOpenDeleteModal && selectedScript && (
              <DeleteAdminScriptModal
                script={selectedScript}
                isOpen={isOpenDeleteModal && selectedScript ? true : false}
                closeModal={() => setIsOpenDeleteModal(false)}
              />
            )}
            <ErrorMessageModal
              isOpen={isOpenErrorModal}
              closeModal={closeErrorModal}
              errorMessage={errorMessage}
            />
          </div>
        </div>
      </div>
    </div>
  );
}
