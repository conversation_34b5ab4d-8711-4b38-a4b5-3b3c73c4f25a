import { useSuspenseQuery } from "@tanstack/react-query";
import { createFileRoute } from "@tanstack/react-router";
import { useState } from "react";

import {
  getAssignmentLogs,
  getGetAssignmentLogsQueryKey,
  getGetAssignmentLogsQueryOptions,
} from "../client.ts";
import BreadCrumbs from "../components/BreadCrumbs.tsx";
import ErrorPage from "../components/ErrorHandling/ErrorPage.tsx";
import NotFound from "../components/ErrorHandling/NotFound.tsx";
import Header from "../components/Header.tsx";
import { LogsColumn } from "../components/LogAssignments/LogAssignmentsColumn.tsx";
import ResponsiveSideNav from "../components/ResponsiveSideNav.tsx";
import Table from "../components/Table.tsx";
import { errorCode } from "../utils/assets.tsx";

export const Route = createFileRoute("/security/assignment-logs")({
  component: SecurityAuditLogs,
  loader: async ({ context: { queryClient } }) => {
    return queryClient.ensureQueryData(getGetAssignmentLogsQueryOptions());
  },
  errorComponent: ({ error }: any) => {
    const status = error?.status in errorCode ? error?.status : 500;
    const errorData = errorCode[status] || errorCode[500];
    return (
      <ErrorPage
        code={status}
        title={errorData.title}
        description={errorData.description}
        colour={errorData.colour}
      />
    );
  },
  notFoundComponent: () => {
    return <NotFound />;
  },
});

function SecurityAuditLogs() {
  const [isSideNavOpen, setIsSideNavOpen] = useState(false);
  const toggleSideNav = () => setIsSideNavOpen((prev) => !prev);

  const engagementQueryKey = getGetAssignmentLogsQueryKey();
  const queryFn = () => getAssignmentLogs();
  const { data: assignmentLogsData } = useSuspenseQuery({
    queryKey: engagementQueryKey,
    queryFn,
  });
  const allAssignmentLogs = assignmentLogsData.userAssignmentsLogs || [];
  return (
    <div className="flex h-screen w-full">
      <ResponsiveSideNav
        toggleSideNav={toggleSideNav}
        isSideNavOpen={isSideNavOpen}
      />
      <div className="flex w-full flex-col">
        <Header toggleSideNav={toggleSideNav} />
        <div className="flex h-full flex-col bg-slate-50 p-6 dark:bg-slate-800">
          <BreadCrumbs />
          <div
            id="engagements-page-main-title"
            className="flex flex-col space-y-2 py-4"
          >
            <span className="text-3xl font-semibold text-black dark:text-white">
              Assignment Logs
            </span>
          </div>
          <div
            id="logs-list"
            className="relative mt-10 flex flex-col space-y-2"
          >
            <div className="flex w-full flex-col rounded-lg bg-white px-3 py-3 dark:bg-[#374357b5]">
              <div className="w-full overflow-hidden">
                <div className="h-8" />
                <Table data={allAssignmentLogs} columns={LogsColumn} />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
