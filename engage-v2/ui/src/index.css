@import 'tailwindcss';

@config '../tailwind.config.js';

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
  *,
  ::after,
  ::before,
  ::backdrop,
  ::file-selector-button {
    border-color: var(--color-gray-200, currentColor);
  }
}

.card {
  background: var(--surface-card);

  border-radius: 10px;
  margin-bottom: 1rem;
}

::-webkit-scrollbar {
  width: 10px;
}

::-webkit-scrollbar-track {
  background: #e2e8f0;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb {
  background: #535e6c;
  border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
  background: #64748b;
}

@media (max-width: 768px) {
  ::-webkit-scrollbar {
    width: 5px;
  }
}
