import {
  Disclosure,
  DisclosureButton,
  DisclosurePanel,
} from "@headlessui/react";
import { Link, useLocation, useMatchRoute } from "@tanstack/react-router";
import { AiFillSecurityScan } from "react-icons/ai";
import { CiMemoPad } from "react-icons/ci";
import { FaCogs, FaShieldAlt } from "react-icons/fa";
import { HiChevronDown, HiOutlineGlobe } from "react-icons/hi";
import { PiBoxArrowUpFill } from "react-icons/pi";

import { useTheme } from "../context/ThemeProvider";
import { useRoles } from "../context/useRoles";

export default function SideNav() {
  const matchRoute = useMatchRoute();
  const roles = useRoles();
  const { isDarkMode } = useTheme();
  const isActive = (path: string) =>
    matchRoute({ to: path }) ? "bg-purple-100 dark:bg-slate-600" : "";
  const useIsPathActive = (route: string) => {
    const location = useLocation();
    return location.pathname.startsWith(route);
  };
  const isSecurityOpen = useIsPathActive("/security");
  return (
    <div className="dark:bg-darkbg h-screen w-64 flex-none border-r-2 border-solid border-slate-200 bg-white p-6 pt-0 md:pt-6 dark:border-slate-500 dark:text-slate-200">
      <div className="flex flex-row items-center justify-start space-x-2 pb-4 md:pb-8">
        <AiFillSecurityScan className="h-9 w-9 text-black! md:h-12 md:w-12 dark:text-white!" />
        <div className="text-xl font-bold md:text-2xl">Engage</div>
      </div>
      <div className="w-full space-y-1 text-slate-900 dark:text-slate-200">
        <div className="py-2 text-sm font-semibold opacity-50">MENU</div>
        <button
          className={`flex w-full items-center justify-between rounded-lg px-2 py-2 text-left font-medium ${isActive("/")}`}
        >
          <Link to="/">
            <div className="flex flex-row items-center space-x-2 dark:text-slate-200">
              <FaCogs
                className={`${isDarkMode ? "text-white" : "text-black"} h-6 w-6 cursor-pointer`}
              />
              <span className="opacity-70">Engagements</span>
            </div>
          </Link>
        </button>
        <button
          className={`flex w-full items-center justify-between rounded-lg px-2 py-2 text-left font-medium ${isActive("/inventory")}`}
        >
          <Link to="/inventory">
            <div className="flex flex-row items-center space-x-2">
              <CiMemoPad
                className={`${isDarkMode ? "text-white" : "text-black"} h-6 w-6 cursor-pointer`}
              />
              <span className="opacity-70">Inventory</span>
            </div>
          </Link>
        </button>
        <button
          className={`flex w-full items-center justify-between rounded-lg px-2 py-2 text-left font-medium ${isActive("/domains")}`}
        >
          <Link to="/domains">
            <div className="flex flex-row items-center space-x-2">
              <HiOutlineGlobe
                className={`${isDarkMode ? "text-white" : "text-black"} h-6 w-6 cursor-pointer`}
              />
              <span className="opacity-70">Domains</span>
            </div>
          </Link>
        </button>
        <button
          className={`flex w-full items-center justify-between rounded-lg px-2 py-2 text-left font-medium ${isActive("/deployments")}`}
        >
          <Link to="/deployments">
            <div className="flex flex-row items-center space-x-2">
              <PiBoxArrowUpFill
                className={`${isDarkMode ? "text-white" : "text-black"} h-6 w-6 cursor-pointer`}
              />
              <span className="opacity-70">Deployments</span>
            </div>
          </Link>
        </button>
        {roles?.some(role => role.includes("Admin")) ? (
          <Disclosure defaultOpen={isSecurityOpen} as="div">
            {() => (
              <>
                <DisclosureButton
                  as="div"
                  className={`${isSecurityOpen ? "bg-purple-50 dark:bg-slate-600" : ""} flex w-full cursor-pointer items-center justify-between rounded-lg px-2 py-2 text-left font-medium`}
                >
                  <div className="flex flex-row items-center space-x-2">
                    <FaShieldAlt
                      className={`${isDarkMode ? "text-white" : "text-black"} h-6 w-6 cursor-pointer`}
                    />
                    <span className="opacity-70">Security</span>
                  </div>
                  <HiChevronDown
                    className={`${isSecurityOpen ? "rotate-180 transform" : ""} h-5 w-5 opacity-70`}
                  />
                </DisclosureButton>
                <DisclosurePanel as="div">
                  <div className="ml-8 flex flex-col space-y-1 opacity-70">
                    <button
                      className={`flex w-full items-center justify-between rounded-lg px-2 py-2 text-left font-medium ${isActive("/security/user-management")}`}
                    >
                      <Link to="/security/user-management">
                        <span className="opacity-70">User Management</span>
                      </Link>
                    </button>
                    <button
                      className={`flex w-full items-center justify-between rounded-lg px-2 py-2 text-left font-medium ${isActive("/security/script-management")}`}
                    >
                      <Link to="/security/script-management">
                        <span className="opacity-70">Script Management</span>
                      </Link>
                    </button>
                    <button
                      className={`flex w-full items-center justify-between rounded-lg px-2 py-2 text-left font-medium ${isActive("/security/assignment-logs")}`}
                    >
                      <Link to="/security/assignment-logs">
                        <span className="opacity-70">Audit Logs</span>
                      </Link>
                    </button>
                    <button
                      className={`flex w-full items-center justify-between rounded-lg px-2 py-2 text-left font-medium ${isActive("/security/assignment-logs")}`}
                    >
                      <Link to="/security/set-instance-types">
                        <span className="opacity-70">Set Instance Types</span>
                      </Link>
                    </button>
                  </div>
                </DisclosurePanel>
              </>
            )}
          </Disclosure>
        ) : null}
      </div>
    </div>
  );
}
