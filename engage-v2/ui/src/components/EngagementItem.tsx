import { Link } from "@tanstack/react-router";
import { FaAws } from "react-icons/fa";
import { HiEllipsisVertical } from "react-icons/hi2";
import { SiGooglecloud } from "react-icons/si";
import { VscAzure } from "react-icons/vsc";

import { useTheme } from "../context/ThemeProvider";
import { EngagementUsers } from "../model";
import { getAwsStatusIcon, getAzureStatusIcon, getStatusColor, getStatusIcon } from "../utils/assets";

type Props = {
  id: string;
  status: string;
  clientName: string;
  title: string;
  users: EngagementUsers;
  activeProviders: string[];
  awsStatus: string;
  azureStatus: string;
};

export default function EngagementItem({
  id,
  clientName,
  title,
  users,
  status,
  awsStatus,
  azureStatus,
}: Props) {
  const { isDarkMode } = useTheme();
  return (
    <Link
      to="/engagements/$engagementId"
      params={{
        engagementId: id,
      }}
      activeProps={{ className: "text-black font-bold" }}
      className="block text-blue-800 hover:text-blue-600"
    >
      <div className="flex flex-col divide-x rounded-lg bg-white shadow-md md:flex-row dark:divide-none dark:bg-[#374357b5] dark:md:divide-solid">
        <div className="flex basis-1/3 flex-row-reverse content-center justify-between space-y-1 space-x-5 p-4 md:flex-row md:items-center md:justify-start">
          <div className="content-center md:basis-1/2 lg:basis-1/4">
            <span
              className={`flex flex-row content-center items-center rounded-full px-3 py-1 shadow-md ${getStatusColor(status)} w-full`}
            >
              {getStatusIcon(status)}
              {status
                .toLowerCase()
                .replace(/[-_]/g, " ")
                .replace(/\b\w/g, (char: string) => char.toUpperCase())}
            </span>
          </div>
          <div className="flex flex-col">
            <span className="text-xs font-normal text-slate-400 uppercase">
              {clientName}
            </span>
            <span className="text-base font-semibold text-slate-700 dark:text-white">
              {title}
            </span>
          </div>
        </div>
        <div className="flex basis-1/3 flex-col space-y-1 p-4 md:items-end">
          <div className="text-xs font-normal text-slate-400">
            TEAM ({users?.length})
          </div>
          <div className="flex flex-row space-x-2">
            {users?.map((user, index) => (
              <div
                key={index}
                className="flex h-8 w-8 items-center justify-center rounded-full bg-lime-500 text-sm font-medium text-white"
              >
                {user.full_name
                  .split(" ")
                  .map((word) => word.charAt(0))
                  .join("")}
              </div>
            ))}
          </div>
        </div>
        <div className="flex basis-1/3 flex-row items-center justify-between space-x-4 p-4 md:justify-end">
          <div className="flex flex-row space-x-4">
            <div className="flex flex-row items-center justify-center space-x-1">
              {getAwsStatusIcon(awsStatus)}
              <FaAws
                className={`${isDarkMode ? "text-white" : "text-black"} h-6 w-6`}
              />
            </div>
            <div className="flex flex-row items-center justify-center space-x-1">
              <div className="flex h-1 w-1 items-center justify-center rounded-full bg-slate-300 text-sm font-medium text-white"></div>
              <SiGooglecloud
                className={`${isDarkMode ? "text-white" : "text-black"} h-6 w-6`}
              />
            </div>
            <div className="flex flex-row items-center justify-center space-x-1">
              {azureStatus ? getAzureStatusIcon(azureStatus) : (
                <div className="flex h-1 w-1 items-center justify-center rounded-full bg-slate-300 text-sm font-medium text-white"></div>
              )}
              <VscAzure
                className={`${isDarkMode ? "text-white" : "text-black"} h-6 w-6`}
              />
            </div>
          </div>
          <HiEllipsisVertical className="h-6 w-6 text-black! dark:text-white!" />
        </div>
      </div>
    </Link>
  );
}
