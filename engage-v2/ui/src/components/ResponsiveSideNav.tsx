import { HiX } from "react-icons/hi";

import SideNav from "./SideNav";

export interface ResponsiveSideNavProps {
  toggleSideNav: () => void;
  isSideNavOpen: boolean;
}

export default function ResponsiveSideNav({
  toggleSideNav,
  isSideNavOpen,
}: ResponsiveSideNavProps) {
  return (
    <div
      className={`bg-opacity-10 dark:bg-opacity-50 fixed inset-0 top-0 left-0 z-30 transform bg-black/25 shadow-[2px_0_2px_-2px_rgba(0,0,0,0.2)] transition-transform duration-300 lg:relative lg:z-0 lg:w-64 lg:translate-x-0 lg:border-r-2 lg:border-solid lg:border-slate-200 lg:bg-white dark:bg-black ${
        isSideNavOpen ? "translate-x-0" : "-translate-x-full"
      }`}
      onClick={toggleSideNav}
    >
      <div
        className="dark:bg-darkbg flex w-64 flex-col border-r-2 border-solid border-slate-200 bg-white lg:hidden dark:border-none"
        onClick={(e) => e.stopPropagation()}
      >
        <div className="flex items-center justify-between p-4">
          <button
            onClick={toggleSideNav}
            className="rounded-md text-gray-700 hover:bg-gray-100 dark:text-white dark:hover:bg-gray-700"
          >
            <HiX className="h-4 w-4 cursor-pointer" />
          </button>
        </div>
        <div className="flex-1 overflow-x-hidden overflow-y-auto">
          <SideNav />
        </div>
      </div>
      <div className="hidden w-64 flex-1 flex-col border-r-2 border-solid border-slate-200 bg-white lg:flex">
        <SideNav />
      </div>
    </div>
  );
}
