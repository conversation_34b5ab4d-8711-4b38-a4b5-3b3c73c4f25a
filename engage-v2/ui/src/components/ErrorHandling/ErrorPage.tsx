import { Link } from "@tanstack/react-router";

type Props = {
  code: number;
  title: string;
  description: string;
  colour: string;
};

export default function ErrorPage({ code, title, description, colour }: Props) {
  return (
    <div className="my-8 flex h-full flex-col items-center justify-center space-y-8">
      <svg
        width="100"
        height="100"
        viewBox="0 0 100 100"
        fill="none"
        xmlns="http://www.w3.org/2000/svg"
      >
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M43.1853 41.0947L44.329 60.5392C44.3938 61.6404 45.3058 62.5001 46.4087 62.5001H53.591C54.694 62.5001 55.6059 61.6403 55.6707 60.5392L56.8144 41.0947C56.8848 39.898 55.9334 38.889 54.7347 38.889H45.2648C44.0663 38.889 43.115 39.898 43.1853 41.0947ZM57.2917 72.2223C57.2917 76.2494 54.0271 79.514 50 79.514C45.9729 79.514 42.7084 76.2494 42.7084 72.2223C42.7084 68.1952 45.9729 64.9307 50 64.9307C54.0271 64.9307 57.2917 68.1952 57.2917 72.2223ZM57.2183 9.71973C54.0181 4.17268 45.9879 4.16261 42.7818 9.71973L1.12451 81.9468C-2.07376 87.4907 1.93024 94.4446 8.34274 94.4446H91.6563C98.0564 94.4446 102.079 87.5013 98.8745 81.9468L57.2183 9.71973ZM9.23458 84.5491L49.0978 15.4529C49.4986 14.7581 50.5014 14.7581 50.9023 15.4529L90.7655 84.5489C91.1662 85.2433 90.665 86.111 89.8632 86.111H10.1368C9.33527 86.1112 8.83405 85.2435 9.23458 84.5491Z"
          fill={colour}
        />
      </svg>
      <div className="text-7xl font-bold text-gray-500 dark:text-gray-200">
        {code}
      </div>
      <div className="text-4xl font-bold text-gray-700 dark:text-gray-200">
        {title}
      </div>
      <div className="flex flex-col items-center text-lg font-medium text-gray-700 dark:text-gray-200">
        {description?.includes(".")
          ? description?.split(". ").map((sentence, index, arr) => (
              <span key={index}>
                {sentence}
                {index < arr.length - 1 && "."}
                <br />
              </span>
            ))
          : description}
      </div>
      <div className="flex flex-col">
        <Link to="/">
          <button className="rounded bg-purple-700 px-28 py-4 text-base font-medium text-white">
            <span>Back to Home</span>
          </button>
        </Link>
      </div>
    </div>
  );
}
