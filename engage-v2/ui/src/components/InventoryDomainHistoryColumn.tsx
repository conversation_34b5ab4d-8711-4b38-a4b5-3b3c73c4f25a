import { createColumnHelper } from "@tanstack/react-table";
import { HiOutlineGlobe } from "react-icons/hi";
import { InventoryDomainResponse } from "../model";

const columnHelper = createColumnHelper<InventoryDomainResponse>();

// Helper function to get background color based on activity type
const getActivityTypeColor = (type: string | null): string => {
  if (!type) return "";
  
  switch (type.toUpperCase()) {
    case "DOMAIN_ASSIGNED":
      return "bg-green-50";
    case "DOMAIN_UNASSIGNED":
      return "";
    case "DOMAIN_BURNED":
      return "bg-red-50";
    case "DOMAIN_EXPIRED":
      return "bg-blue-50";
    case "DOMAIN_QUARANTINED":
      return "bg-yellow-50";
    default:
      return "";
  }
};

export const inventoryDomainHistoryColumns = [
  {
    id: "rowBackground",
    cell: () => null,
    meta: {
      getRowClassName: (row: any) => getActivityTypeColor(row.original.type)
    }
  },
  columnHelper.accessor("url", {
    header: () => "Domain",
    cell: (info) => (
      <div className="flex items-center space-x-1 py-2 min-w-[120px]">
        <span className="flex-shrink-0">
          <HiOutlineGlobe className="text-blue-500 h-4 w-4" />
        </span>
        <span className="truncate text-sm flex-1">{info.getValue() || '-'}</span>
      </div>
    ),
  }),
  columnHelper.accessor("registrar", {
    header: () => "Registrar",
    cell: (info) => (
      <div className="py-2 min-w-[100px] truncate text-sm">
        {info.getValue() || "-"}
      </div>
    ),
  }),
  
  columnHelper.accessor("engagement", {
    header: () => "Engagement",
    cell: (info) => (
      <div className="py-2 min-w-[80px] truncate text-sm">
        {info.getValue() || "-"}
      </div>
    ),
  }),
  columnHelper.accessor("client", {
    header: () => "Client",
    cell: (info) => (
      <div className="py-2 min-w-[60px] truncate text-sm">
        {info.getValue() || "-"}
      </div>
    ),
  }),
  columnHelper.accessor("type", {
    header: () => "Activity Type",
    cell: (info) => (
      <div className="py-2 min-w-[100px] truncate text-sm">
        {info.getValue() || "-"}
      </div>
    ),
  }),
  columnHelper.accessor("last_activity", {
    header: () => "Created At",
    cell: (info) => (
      <div className="py-2 min-w-[100px] truncate text-sm">
        {info.getValue() || "-"}
      </div>
    ),
  }),
  columnHelper.accessor("username", {
    header: () => "Last User",
    cell: (info) => (
      <div className="py-2 min-w-[100px] truncate text-sm">
        {info.getValue() || "-"}
      </div>
    ),
  }),
];
