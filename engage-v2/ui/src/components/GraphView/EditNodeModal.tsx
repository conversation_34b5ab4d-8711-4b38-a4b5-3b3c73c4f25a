import { useMutation, useQuery, useQuery<PERSON>lient } from "@tanstack/react-query";
import { AxiosError } from "axios";
import { useEffect, useState } from "react";
import { HiPencil } from "react-icons/hi";
import { toast } from "react-toastify";

import {
  EditNodesCloudInstanceMutationBody,
  editNodesCloudInstance,
  editNodesEmailAddress,
  editNodesHost,
  editNodesPerson,
  editNodesUrl,
  getGetEngagementGraphsQuery<PERSON>ey,
  getGetEngagementQueryKey,
  getGetInventoryCloudInstancesQueryKey,
  getGetInventoryEmailAddressesQueryKey,
  getGetInventoryHostsQueryKey,
  getGetInventoryPersonsQueryKey,
  getGetInventoryQueryKey,
  getGetInventoryUrlsQueryKey,
  getGetNodesCloudInstanceQueryKey,
  getGetNodesEmailAddress<PERSON>uery<PERSON><PERSON>,
  getGetNodesHost<PERSON><PERSON><PERSON><PERSON><PERSON>,
  getGetNodesPerson<PERSON><PERSON><PERSON><PERSON><PERSON>,
  getGetNodesUrl<PERSON><PERSON><PERSON><PERSON><PERSON>,
  getNodesCloudInstance,
  getNodesEmailAddress,
  getNodesHost,
  getNodesPerson,
  getNodesUrl,
} from "../../client";
import { useTheme } from "../../context/ThemeProvider";
import {
  ActivityLog,
  EditNodeTypeCloudInstanceInputBody,
  EditNodeTypeCloudInstanceOutputBody,
  EditNodeTypeEmailAddressInputBody,
  EditNodeTypeEmailAddressOutputBody,
  EditNodeTypeHostInputBody,
  EditNodeTypePersonInputBody,
  EditNodeTypeUrlInputBody,
  EditNodeTypeUrlOutputBody,
  GetNodeTypeHostOutputBody,
  GetNodeTypePersonOutputBody,
  NodeEmailAddress,
  NodeHost,
  NodePerson,
  NodeUrl,
} from "../../model";
import Modal from "../Modal";
import ActivityTimeline from "./NodeModals.tsx/ActivityTimeline";
import { CloudNodeForm } from "./NodeModals.tsx/CloudNodeForm";
import { EmailNodeForm } from "./NodeModals.tsx/EmailNodeForm";
import { HostNodeForm } from "./NodeModals.tsx/HostNodeForm";
import { PersonNodeForm } from "./NodeModals.tsx/PersonNodeForm";
import { UrlNodeForm } from "./NodeModals.tsx/UrlNodeForm";

type EditNodeModalProps = {
  isOpen: boolean;
  closeModal: () => void;
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  nodeDetails?: any;
  nodeType?: string;
  engagementID?: string;
  isNodeGraph: boolean;
  engagementName?: string;
};

export default function EditNodeModal({
  isOpen,
  closeModal,
  nodeDetails,
  engagementID,
  isNodeGraph,
}: EditNodeModalProps) {
  const queryClient = useQueryClient();
  const { isDarkMode } = useTheme();
  const [isEditMode, setIsEditMode] = useState(false);
  const selectedDetails = isNodeGraph ? nodeDetails?.data : nodeDetails;
  const selectedNodeName = isNodeGraph
    ? selectedDetails?.label
    : selectedDetails?.name;
  const selectedNodeType = isNodeGraph
    ? nodeDetails?.classes[0]
    : selectedDetails?.type;

  const fetchNodeDetails = ({
    queryKey,
  }: {
    queryKey: [
      string,
      "CLOUD_INSTANCE" | "PERSON" | "HOST" | "EMAIL_ADDRESS" | "URL",
      string,
    ];
  }) => {
    const [, type, id] = queryKey;
    switch (type.toUpperCase()) {
      case "CLOUD_INSTANCE":
        return getNodesCloudInstance(id);
      case "PERSON":
        return getNodesPerson(id);
      case "HOST":
        return getNodesHost(id);
      case "EMAIL_ADDRESS":
        return getNodesEmailAddress(id);
      case "URL":
        return getNodesUrl(id);
      default:
        throw new Error("Unknown node type");
    }
  };

  const { data: nodeDetailsData } = useQuery({
    queryKey: [
      "GET_NODES_DETAILS",
      selectedNodeType?.toLowerCase(),
      selectedDetails?.id,
    ],
    // eslint-disable-next-line @typescript-eslint/ban-ts-comment
    // @ts-expect-error
    queryFn: fetchNodeDetails,
    enabled: !!selectedDetails?.id,
  });

 const { node: nodeData, activity_logs: activityLogs } = (nodeDetailsData || {}) as { node: any; activity_logs: ActivityLog[] };

  type EditNodeOutput =
    | EditNodeTypeCloudInstanceOutputBody
    | GetNodeTypePersonOutputBody
    | GetNodeTypeHostOutputBody
    | EditNodeTypeEmailAddressOutputBody
    | EditNodeTypeUrlOutputBody;

  const mutation = useMutation<
    EditNodeOutput | void,
    AxiosError,
    | EditNodeTypePersonInputBody
    | EditNodeTypeHostInputBody
    | EditNodeTypeEmailAddressInputBody
    | EditNodeTypeUrlInputBody
    | EditNodeTypeCloudInstanceInputBody
  >({
    mutationFn: async (
      updatedData:
        | EditNodeTypePersonInputBody
        | EditNodeTypeHostInputBody
        | EditNodeTypeEmailAddressInputBody
        | EditNodeTypeUrlInputBody
        | EditNodeTypeCloudInstanceInputBody,
    ) => {
      if (!updatedData) {
        toast.error("Updated data is required.");
        throw new Error("Updated data is required");
      }
      switch (selectedNodeType?.toUpperCase()) {
        case "CLOUD_INSTANCE":
          return editNodesCloudInstance(
            selectedDetails.id,
            updatedData as EditNodeTypeCloudInstanceInputBody,
          );
        case "PERSON":
          return editNodesPerson(
            selectedDetails.id,
            updatedData as EditNodeTypePersonInputBody,
          );
        case "HOST":
          return editNodesHost(
            selectedDetails.id,
            updatedData as EditNodeTypeHostInputBody,
          );
        case "EMAIL_ADDRESS":
          return editNodesEmailAddress(
            selectedDetails.id,
            updatedData as EditNodeTypeEmailAddressInputBody,
          );
        case "URL":
          return editNodesUrl(
            selectedDetails.id,
            updatedData as EditNodeTypeUrlInputBody,
          );
        default:
          throw new Error("Unknown node type");
      }
    },
    onSettled: (_data: any, error: unknown) => {
      if (!selectedDetails?.id) {
        toast.error("Node ID is required for invalidating queries.");
        throw new Error("Node ID is required for invalidating queries.");
      }
      if (!error) {
        toast.success("Node instance has been successfully edited.");
      }
      switch (selectedNodeType?.toUpperCase()) {
        case "CLOUD_INSTANCE":
          queryClient.invalidateQueries({
            queryKey: getGetNodesCloudInstanceQueryKey(selectedDetails.id),
          });
          queryClient.invalidateQueries({
            queryKey: getGetInventoryCloudInstancesQueryKey(),
          });
          break;
        case "PERSON":
          queryClient.invalidateQueries({
            queryKey: getGetNodesPersonQueryKey(selectedDetails.id),
          });
          queryClient.invalidateQueries({
            queryKey: getGetInventoryPersonsQueryKey(),
          });
          break;
        case "HOST":
          queryClient.invalidateQueries({
            queryKey: getGetNodesHostQueryKey(selectedDetails.id),
          });
          queryClient.invalidateQueries({
            queryKey: getGetInventoryHostsQueryKey(),
          });
          break;
        case "EMAIL_ADDRESS":
          queryClient.invalidateQueries({
            queryKey: getGetNodesEmailAddressQueryKey(selectedDetails.id),
          });
          queryClient.invalidateQueries({
            queryKey: getGetInventoryEmailAddressesQueryKey(),
          });
          break;
        case "URL":
          queryClient.invalidateQueries({
            queryKey: getGetNodesUrlQueryKey(selectedDetails.id),
          });
          queryClient.invalidateQueries({
            queryKey: getGetInventoryUrlsQueryKey(),
          });
          break;
        default:
          toast.error("Engagement ID is required.");
          throw new Error("Unknown node type");
      }
      if (isNodeGraph) {
        if (!engagementID) {
          toast.error("Engagement ID is required.");
          throw new Error("Engagement ID is required");
        }
        const queryKey = getGetEngagementGraphsQueryKey(engagementID);
        const engagementQueryKey = getGetEngagementQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey });
        queryClient.invalidateQueries({ queryKey: engagementQueryKey });
      } else {
        if (engagementID) {
          const queryKey = getGetEngagementGraphsQueryKey(engagementID);
          const engagementQueryKey = getGetEngagementQueryKey(engagementID);
          queryClient.invalidateQueries({ queryKey });
          queryClient.invalidateQueries({ queryKey: engagementQueryKey });
        } else {
          const queryKey = getGetInventoryQueryKey();
          queryClient.invalidateQueries({ queryKey });
        }
      }
      closeModal();
    },
  });

  useEffect(() => {
    if (!isOpen) {
      setIsEditMode(false);
    }
  }, [isOpen]);

  function editNodeMode() {
    setIsEditMode((prevEditMode) => !prevEditMode);
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleUpdate = (formData: any) => {
    let updatedData;
    switch (selectedNodeType?.toUpperCase()) {
      case "CLOUD_INSTANCE":
        updatedData = {
          name: formData.name,
          ...formData,
        } as EditNodesCloudInstanceMutationBody;
        break;
      case "PERSON":
        updatedData = {
          first_name: formData.first_name,
          last_name: formData.last_name,
          email: formData.email,
          company: formData.company,
          title: formData.title,
        } as EditNodeTypePersonInputBody;
        break;
      case "HOST":
        updatedData = {
          alternative_names: formData.alternative_names,
          ip_addresses: formData.ip_addresses,
          name: formData.name,
        } as EditNodeTypeHostInputBody;
        break;
      case "EMAIL_ADDRESS":
        updatedData = {
          email_address: formData.email_address,
        } as EditNodeTypeEmailAddressInputBody;
        break;
      case "URL":
        updatedData = {
          url: formData.url,
        } as EditNodeTypeUrlInputBody;
        break;
      default:
        throw new Error("Unknown node type");
    }
    mutation.mutate(updatedData);
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function isNodeEmailAddress(data: any): data is NodeEmailAddress {
    return data && "email_address" in data;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function isNodeHost(data: any): data is NodeHost {
    return data && "ip_addresses" in data;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function isNodePerson(data: any): data is NodePerson {
    return data && "first_name" in data;
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  function isNodeUrl(data: any): data is NodeUrl {
    return data && "url" in data;
  }

  function renderNodeForm() {
    if (!nodeData) return null; // Handle case where nodeData is not yet loaded
    switch (selectedNodeType) {
      case "CLOUD_INSTANCE":
        return (
          <CloudNodeForm
            nodeData={nodeData}
            isEditMode={isEditMode}
            setIsEditMode={setIsEditMode}
            onUpdate={handleUpdate}
          />
        );
      case "EMAIL_ADDRESS":
        if (isNodeEmailAddress(nodeData)) {
          return (
            <EmailNodeForm
              nodeData={nodeData}
              isEditMode={isEditMode}
              setIsEditMode={setIsEditMode}
              onUpdate={handleUpdate}
            />
          );
        }
        break;
      case "HOST":
        if (isNodeHost(nodeData)) {
          return (
            <HostNodeForm
              nodeData={nodeData}
              isEditMode={isEditMode}
              setIsEditMode={setIsEditMode}
              onUpdate={handleUpdate}
            />
          );
        }
        break;
      case "PERSON":
        if (isNodePerson(nodeData)) {
          return (
            <PersonNodeForm
              nodeData={nodeData}
              isEditMode={isEditMode}
              setIsEditMode={setIsEditMode}
              onUpdate={handleUpdate}
            />
          );
        }
        break;
      case "URL":
        if (isNodeUrl(nodeData)) {
          return (
            <UrlNodeForm
              nodeData={nodeData}
              isEditMode={isEditMode}
              setIsEditMode={setIsEditMode}
              onUpdate={handleUpdate}
            />
          );
        }
        break;
      default:
        return <div>Unknown node type</div>;
    }
  }

  return (
    <Modal
      title={`${isEditMode ? "Edit" : "View"} Node`}
      isOpen={isOpen}
      closeModal={closeModal}
      widthClass={
        selectedNodeType === "CLOUD_INSTANCE"
          ? "w-11/12 sm:w-10/12 md:w-10/12 lg:w-8/12"
          : "w-11/12 sm:w-10/12 md:w-9/12 lg:w-7/12"
      }
    >
      <div className="">
        <div className="flex flex-row text-lg font-semibold text-black dark:text-slate-300">
          {selectedNodeName}
          {!isEditMode && (
            <span className="cursor-pointer px-3" onClick={editNodeMode}>
              <HiPencil
                className={`${isDarkMode ? "text-white" : "text-black"} h-5 w-5`}
              />
            </span>
          )}
        </div>
        <div className="flex flex-col justify-between md:flex-row">
          <div className="flex w-full flex-col space-y-2 md:w-1/2">
            <div className="mt-4">{renderNodeForm()}</div>
          </div>
          <div className="mt-4 flex w-full flex-col space-y-2 md:w-1/2">
            <div className="border-b-2 border-solid border-black font-semibold dark:border-white dark:text-white">
              Activity Timeline
            </div>
            <div className="max-h-[calc(10*3rem)] list-none divide-y divide-slate-200 overflow-y-auto">
              {activityLogs && activityLogs?.length > 0 ? (
                <ActivityTimeline
                  data={activityLogs as ActivityLog[]}
                  selectedName={""}
                />
              ) : (
                <div className="dark:text-white">
                  No activity logs available
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className="mt-4 flex justify-end space-x-2"></div>
    </Modal>
  );
}
