import { ChangeEvent, useState } from "react";
import { useEffect } from "react";

import { EditNodesHostMutationBody } from "../../../client";
import { NodeHost } from "../../../model";
import { ActionButtons, ButtonProps } from "../../ActionButtons";
import { Errors } from "../../CreateNode/CreateNodeTypeModal";

interface UrlNodeFormProps {
  nodeData: NodeHost;
  isEditMode: boolean;
  setIsEditMode: (isEditMode: boolean) => void;
  onUpdate: (formData: EditNodesHostMutationBody) => void;
}

export function HostNodeForm({
  nodeData,
  isEditMode,
  setIsEditMode,
  onUpdate,
}: UrlNodeFormProps) {
  const [formData, setFormData] = useState<EditNodesHostMutationBody>({
    name: "",
    ip_addresses: [],
    alternative_names: [],
  });

  useEffect(() => {
    if (nodeData) {
      setFormData({
        name: nodeData.name,
        ip_addresses: Array.isArray(nodeData.ip_addresses) ? nodeData.ip_addresses : [],
        alternative_names: Array.isArray(nodeData.alternative_names) ? nodeData.alternative_names : [],
      });
      // Clear errors when new data is loaded
      setErrors({});
    }
  }, [nodeData]);

  const [errors, setErrors] = useState<Errors>({});

  const handleSubmit = () => {
    // Simple validation first - test if basic validation works
    const errors: Record<string, string> = {};

    // Test basic name validation
    if (!formData.name || formData.name.trim() === '') {
      errors.name = 'Name is required';
    }

    // Test IP address validation if any are provided
    if (formData.ip_addresses && formData.ip_addresses.length > 0) {
      const ipRegex = /^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$/;
      for (let i = 0; i < formData.ip_addresses.length; i++) {
        const ip = formData.ip_addresses[i];
        if (ip && ip.trim() !== '' && !ipRegex.test(ip.trim())) {
          errors.ip_addresses = `Invalid IP address: ${ip}`;
          break; // Show only first error
        }
      }
    }

    // Test alternative names validation if any are provided
    if (formData.alternative_names && formData.alternative_names.length > 0) {
      const nameRegex = /^[a-zA-Z0-9_.-]+$/;
      for (let i = 0; i < formData.alternative_names.length; i++) {
        const name = formData.alternative_names[i];
        if (name && name.trim() !== '' && !nameRegex.test(name.trim())) {
          errors.alternative_names = `Invalid alternative name: ${name}. Only letters, numbers, dots, hyphens, and underscores are allowed.`;
          break; // Show only first error
        }
      }
    }

    setErrors(errors);

    if (Object.keys(errors).length > 0) {
      return;
    }

    // Prepare data for API - backend expects arrays, not null
    const dataToSend = {
      name: formData.name.trim(),
      ip_addresses: formData.ip_addresses || [],
      alternative_names: formData.alternative_names || [],
    };

    onUpdate(dataToSend);
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    // Clear error for this field when user starts typing (optional UX improvement)
    if (errors[name]) {
      setErrors((prev) => ({
        ...prev,
        [name]: "",
      }));
    }
  };

  const handleInputArrayChange = (
    e: ChangeEvent<HTMLInputElement>,
    field: keyof typeof formData,
    index: number,
  ) => {
    const { value } = e.target;
    setFormData((prevData) => {
      const currentArray = (prevData[field] as string[]) || [];
      const updatedArray = value
        ? currentArray.length > 0
          ? currentArray.map((item, i) => (i === index ? value : item))
          : [value]
        : currentArray.filter((_, i) => i !== index);

      return {
        ...prevData,
        [field]: updatedArray.length > 0 ? updatedArray : [], // Reset to empty array if all values are removed
      };
    });
    // Clear error for this field when user starts typing (optional UX improvement)
    if (errors[field]) {
      setErrors((prev) => ({
        ...prev,
        [field]: "",
      }));
    }
  };



  const primaryButton: ButtonProps = {
    label: "Save",
    onClick: handleSubmit,
    variant: "primary",
    // disabled: ,
  };

  const secondaryButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      setFormData({
        name: nodeData.name,
        ip_addresses: Array.isArray(nodeData.ip_addresses) ? nodeData.ip_addresses : [],
        alternative_names: Array.isArray(nodeData.alternative_names) ? nodeData.alternative_names : [],
      });
      setIsEditMode(false);
    },
    variant: "secondary",
  };

  return (
    <div className="flex w-full flex-col md:w-5/6">
      <div
        className={`${isEditMode ? "flex-col" : "flex-row justify-between"} flex`}
      >
        <label className="font-semibold dark:text-slate-300">Name: </label>
        {isEditMode ? (
          <>
            <input
              type="text"
              className="mt-1 rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:text-black focus:outline-hidden focus:ring-2 focus:ring-purple-700 dark:border-transparent dark:bg-slate-600 dark:text-slate-100 dark:focus:ring-purple-400"
              value={formData?.name}
              name="name"
              onChange={handleInputChange}
            />
            {errors.name && <p className="mt-1 text-red-500">{errors.name}</p>}
          </>
        ) : (
          <p className="dark:text-white">{nodeData?.name}</p>
        )}
      </div>
      <div
        className={`${isEditMode ? "mt-3 flex-col" : "flex-row justify-between space-x-2"} mt-2 flex`}
      >
        <label className="font-semibold dark:text-slate-300">
          IP Addresses:
        </label>
        <div className={`${isEditMode ? "" : "flex flex-col text-end"}`}>
          {isEditMode ? (
            // Edit mode: use formData and allow editing
            <>
              {formData.ip_addresses && formData.ip_addresses.length > 0
                ? formData.ip_addresses.map((address, index) => (
                    <input
                      key={index}
                      type="text"
                      className="mt-1 w-full rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:text-black focus:outline-hidden focus:ring-2 focus:ring-purple-700 dark:border-transparent dark:bg-slate-600 dark:text-slate-100 dark:focus:ring-purple-400"
                      value={address}
                      name="ip_addresses"
                      onChange={(e) =>
                        handleInputArrayChange(e, "ip_addresses", index)
                      }
                    />
                  ))
                : (
                    <input
                      type="text"
                      className="mt-1 w-full rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:text-black focus:outline-hidden focus:ring-2 focus:ring-purple-700 dark:border-transparent dark:bg-slate-600 dark:text-slate-100 dark:focus:ring-purple-400"
                      name="ip_addresses"
                      placeholder="Add an IP address"
                      onChange={(e) => handleInputArrayChange(e, "ip_addresses", 0)}
                    />
                  )}
              {errors.ip_addresses && (
                <p className="mt-1 text-red-500">{errors.ip_addresses}</p>
              )}
            </>
          ) : (
            // View mode: use nodeData for display
            nodeData.ip_addresses && nodeData.ip_addresses.length > 0 ? (
              nodeData.ip_addresses.map((address, index) => (
                <p key={index} className="dark:text-white">{address}</p>
              ))
            ) : (
              <p className="dark:text-white">No IP addresses</p>
            )
          )}
        </div>
      </div>
      <div
        className={`${isEditMode ? "mt-3 flex-col" : "flex-row justify-between space-x-2"} mt-2 flex`}
      >
        <label className="font-semibold dark:text-slate-300">
          Alternative Names:
        </label>
        <div className={`${isEditMode ? "" : "flex flex-col text-end"}`}>
          {isEditMode ? (
            // Edit mode: use formData and allow editing
            <>
              {formData.alternative_names && formData.alternative_names.length > 0
                ? formData.alternative_names.map((name, index) => (
                    <input
                      key={index}
                      type="text"
                      className="mt-1 w-full rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:text-black focus:outline-hidden focus:ring-2 focus:ring-purple-700 dark:border-transparent dark:bg-slate-600 dark:text-slate-100 dark:focus:ring-purple-400"
                      value={name}
                      name="alternative_names"
                      onChange={(e) =>
                        handleInputArrayChange(e, "alternative_names", index)
                      }
                    />
                  ))
                : (
                    <input
                      type="text"
                      className="mt-1 w-full rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:text-black focus:outline-hidden focus:ring-2 focus:ring-purple-700 dark:border-transparent dark:bg-slate-600 dark:text-slate-100 dark:focus:ring-purple-400"
                      name="alternative_names"
                      placeholder="Add an alternative name"
                      onChange={(e) =>
                        handleInputArrayChange(e, "alternative_names", 0)
                      }
                    />
                  )}
              {errors.alternative_names && (
                <p className="mt-1 text-red-500">{errors.alternative_names}</p>
              )}
            </>
          ) : (
            // View mode: use nodeData for display
            nodeData.alternative_names && nodeData.alternative_names.length > 0 ? (
              nodeData.alternative_names.map((name, index) => (
                <p key={index} className="dark:text-white">{name}</p>
              ))
            ) : (
              <p className="dark:text-white">No alternative names</p>
            )
          )}
        </div>
      </div>
      {isEditMode && (
        <div className="mt-8 flex justify-end">
          <ActionButtons
            primaryButton={primaryButton}
            secondaryButton={secondaryButton}
          />
        </div>
      )}
    </div>
  );
}
