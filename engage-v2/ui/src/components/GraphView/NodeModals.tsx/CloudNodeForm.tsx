import { ChangeEvent, useState } from "react";

import { EditNodesCloudInstanceMutationBody } from "../../../client";
import { NodeCloudInstance } from "../../../model";
import { nodeValidationSchemas, validateFormData } from "../../../utils/validationutils";
import { ActionButtons, ButtonProps } from "../../ActionButtons";

interface CloudNodeFormProps {
  nodeData: NodeCloudInstance;
  isEditMode: boolean;
  setIsEditMode: (value: boolean) => void;
  onUpdate: (formData: EditNodesCloudInstanceMutationBody) => void;
}

export function CloudNodeForm({
  nodeData,
  isEditMode,
  setIsEditMode,
  onUpdate,
}: CloudNodeFormProps) {
  const [formData, setFormData] = useState({
    provider: nodeData.provider,
    region: nodeData.region,
    operating_system_image_id: nodeData.operating_system_image_id,
    size: nodeData.instance_type,
    name: nodeData.name,
    public_ipv4_address: nodeData.public_ipv4_address,
    open_ports: nodeData.open_ingress_tcp_ports || [],
  });
  const handleSubmit = () => {
    // Validate form data only when save is clicked
    const { isValid, errors } = validateFormData(
      { name: formData.name },
      nodeValidationSchemas.cloud_instance
    );

    setError(errors.name || "");

    if (isValid) {
      onUpdate({
        name: formData.name,
        open_ingress_tcp_ports: formData.open_ports,
      });
    }
  };

  const [error, setError] = useState("");

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    // Clear error when user starts typing (optional UX improvement)
    if (error) {
      setError("");
    }
  };

  const handlePortsChange = (e: ChangeEvent<HTMLInputElement>) => {
    const portValue = parseInt(e.target.value, 10);
    setFormData((prevData) => {
      const openPorts = e.target.checked
        ? [...prevData.open_ports, portValue]
        : prevData.open_ports.filter((port: number) => port !== portValue);
      return { ...prevData, open_ingress_tcp_ports: openPorts };
    });
  };



  const primaryButton: ButtonProps = {
    label: "Save",
    onClick: handleSubmit,
    variant: "primary",
    // disabled: ,
  };

  const secondaryButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      setIsEditMode(false);
    },
    variant: "secondary",
  };

  return (
    <div className="flex w-full flex-col md:w-5/6">
      <div
        className={`${isEditMode ? "flex-col" : "flex-row justify-between"} flex`}
      >
        <label className="font-semibold text-gray-700 dark:text-slate-300">
          Name:
        </label>
        {isEditMode ? (
          <>
            <input
              type="text"
              className="mt-1 rounded-sm border border-gray-300 px-4 py-2 text-gray-700 focus:ring-2 focus:ring-purple-700 focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100 dark:focus:ring-purple-400"
              value={formData.name}
              name="name"
              onChange={handleInputChange}
            />
            {error && <p className="mt-1 text-red-500">{error}</p>}
          </>
        ) : (
          <p className="dark:text-white">{nodeData.name}</p>
        )}
      </div>
      <div
        className={`${isEditMode ? "mt-3 flex-col" : "flex-row justify-between"} mt-2 flex`}
      >
        <label className="font-semibold text-gray-400 dark:text-slate-300">
          Provider:
        </label>
        {isEditMode ? (
          <input
            type="text"
            className="mt-1 rounded-sm border border-gray-300 px-4 py-2 text-gray-400 dark:border-transparent dark:bg-slate-600"
            value={formData.provider}
            name="provider"
            disabled
          />
        ) : (
          <p className="dark:text-white">{nodeData.provider}</p>
        )}
      </div>
      <div
        className={`${isEditMode ? "mt-3 flex-col" : "flex-row justify-between"} mt-2 flex`}
      >
        <label className="font-semibold text-gray-400 dark:text-slate-300">
          Region:
        </label>
        {isEditMode ? (
          <input
            type="text"
            className="mt-1 rounded-sm border border-gray-300 px-4 py-2 text-gray-400 dark:border-transparent dark:bg-slate-600"
            value={formData.region}
            name="region"
            disabled
          />
        ) : (
          <p className="dark:text-white">{nodeData.region}</p>
        )}
      </div>
      <div
        className={`${isEditMode ? "mt-3 flex-col" : "flex-row justify-between"} mt-2 flex`}
      >
        <label className="font-semibold text-gray-400 dark:text-slate-300">
          Size:
        </label>
        {isEditMode ? (
          <input
            type="text"
            className="mt-1 rounded-sm border border-gray-300 px-4 py-2 text-gray-400 dark:border-transparent dark:bg-slate-600"
            value={formData.size}
            name="size"
            disabled
          />
        ) : (
          <p className="dark:text-white">{nodeData.instance_type}</p>
        )}
      </div>
      <div
        className={`${isEditMode ? "mt-3 flex-col" : "flex-row justify-between"} mt-2 flex`}
      >
        <label className="font-semibold text-gray-400 dark:text-slate-300">
          Operating System Image ID:
        </label>
        {isEditMode ? (
          <input
            type="text"
            className="mt-1 rounded-sm border border-gray-300 px-4 py-2 text-gray-400 dark:border-transparent dark:bg-slate-600"
            value={formData.operating_system_image_id}
            name="operating_system_image_id"
            disabled
          />
        ) : (
          <p className="text-right dark:text-white">
            {nodeData.operating_system_image_id}
          </p>
        )}
      </div>
      <div
        className={`${isEditMode ? "mt-3 flex-col" : "flex-row justify-between"} mt-2 flex`}
      >
        <label className="font-semibold text-gray-400 dark:text-slate-300">
          Public IPv4 Address:
        </label>
        {isEditMode ? (
          <input
            type="text"
            className="mt-1 rounded-sm border border-gray-300 px-4 py-2 text-gray-400 dark:border-transparent dark:bg-slate-600"
            value={formData?.public_ipv4_address || ""}
            name="public_ipv4_address"
            disabled
          />
        ) : (
          <p className="text-right dark:text-white">
            {nodeData.public_ipv4_address}
          </p>
        )}
      </div>
      <div
        className={`${isEditMode ? "mt-3 flex-col" : "flex-row justify-between"} mt-2 flex`}
      >
        <label className="font-semibold text-gray-700 dark:text-slate-300">
          Open Ports:
        </label>
        <div
          id="port-selections"
          className="mt-1 flex flex-row space-x-4 text-gray-600 dark:text-white"
        >
          {[
            { id: "ssh_key", label: "ssh", value: 22 },
            { id: "http_selection", label: "http", value: 80 },
            { id: "https_selection", label: "https", value: 443 },
            { id: "udp-53-selection", label: "udp_53", value: 53 },
            { id: "udp-9100-selection", label: "udp_9100", value: 9100 },
          ].map(({ id, label, value }) => (
            <div key={id} className="flex-row items-center space-x-1">
              <input
                id={id}
                type="checkbox"
                value={value}
                checked={formData.open_ports.includes(value)}
                onChange={(e) => handlePortsChange(e)}
                disabled={!isEditMode}
              />
              <label htmlFor={id}>{label}</label>
            </div>
          ))}
        </div>
      </div>
      {isEditMode && (
        <div className="mt-8 flex justify-end">
          <ActionButtons
            primaryButton={primaryButton}
            secondaryButton={secondaryButton}
          />
        </div>
      )}
    </div>
  );
}
