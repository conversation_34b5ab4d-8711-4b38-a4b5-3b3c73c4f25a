import { ChangeEvent, useState } from "react";

import { EditNodesUrlMutationBody } from "../../../client";
import { NodeUrl } from "../../../model";
import { nodeValidationSchemas, validateFormData } from "../../../utils/validationutils";
import { ActionButtons, ButtonProps } from "../../ActionButtons";

interface UrlNodeFormProps {
  nodeData: NodeUrl;
  isEditMode: boolean;
  setIsEditMode: (isEditMode: boolean) => void;
  onUpdate: (formData: EditNodesUrlMutationBody) => void;
}

export function UrlNodeForm({
  nodeData,
  isEditMode,
  setIsEditMode,
  onUpdate,
}: UrlNodeFormProps) {
  const [formData, setFormData] = useState({
    url: nodeData.url,
  });
  const [error, setError] = useState("");

  const handleSubmit = () => {
    // Validate form data only when save is clicked
    const { isValid, errors } = validateFormData(
      formData,
      nodeValidationSchemas.url
    );

    setError(errors.url || "");

    if (isValid) {
      onUpdate(formData);
    }
  };

  const handleInputChange = (e: ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prevData) => ({
      ...prevData,
      [name]: value,
    }));
    // Clear error when user starts typing (optional UX improvement)
    if (error) {
      setError("");
    }
  };
  const primaryButton: ButtonProps = {
    label: "Save",
    onClick: handleSubmit,
    variant: "primary",
    // disabled: ,
  };

  const secondaryButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      setIsEditMode(false);
    },
    variant: "secondary",
  };

  return (
    <div
      className={`${isEditMode ? "flex-col" : "flex-row justify-between"} flex w-full md:w-5/6`}
    >
      <label className="font-semibold dark:text-slate-300">URL: </label>
      {isEditMode ? (
        <>
          <input
            type="text"
            className="mt-1 rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:text-black focus:outline-hidden focus:ring-2 focus:ring-purple-700 dark:border-transparent dark:bg-slate-600 dark:text-slate-100 dark:focus:ring-purple-400"
            value={formData.url}
            name="url"
            onChange={handleInputChange}
          />
          {error && <p className="mt-1 text-red-500">{error}</p>}
        </>
      ) : (
        <p className="font-normal dark:text-white">{nodeData?.url}</p>
      )}
      {isEditMode && (
        <div className="mt-8 flex justify-end">
          <ActionButtons
            primaryButton={primaryButton}
            secondaryButton={secondaryButton}
          />
        </div>
      )}
    </div>
  );
}
