import { MultiSelect, MultiSelectChangeEvent } from "primereact/multiselect";
import { ChangeEvent, useEffect, useState } from "react";

import { EngagementUser } from "../../model";
import { ActionButtons, ButtonProps } from "../ActionButtons";
import { EngagementUsersOptions } from "../EngagementUsersOptions";
import Modal from "../Modal";

type EditEngagementProps = {
  engagementID: string;
  title: string;
  wbsCode: string;
  clientName: string;
  users: EngagementUser[];
  availableUsers: EngagementUser[];
  handleSubmit: (
    engagementID: string,
    title: string,
    wbsCode: string,
    users: EngagementUser[],
  ) => void;
  isOpen: boolean;
  closeModal: () => void;
  handleMultiSelectChange: (e: MultiSelectChangeEvent) => void;
  handleInputChange: (e: ChangeEvent<HTMLInputElement>) => void;
};

export default function EditEngagementModal({
  engagementID,
  title,
  wbsCode,
  clientName,
  users,
  availableUsers,
  handleSubmit,
  isOpen,
  closeModal,
  handleMultiSelectChange,
  handleInputChange,
}: EditEngagementProps) {
  const [updateButtonIsDisabled, setUpdateButtonIsDisabled] = useState(true);

  useEffect(() => {
    setUpdateButtonIsDisabled(title === "" || wbsCode === "");
  }, [wbsCode, users, title]);

  const primaryButton: ButtonProps = {
    label: "Update",
    onClick: () => handleSubmit(engagementID, title, wbsCode, users),
    variant: "primary",
    disabled: updateButtonIsDisabled,
  };

  const secondaryButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      closeModal();
    },
    variant: "secondary",
  };

  return (
    <>
      <Modal
        title={"Edit Engagement"}
        isOpen={isOpen}
        closeModal={closeModal}
        widthClass="w-11/12 sm:w-10/12 md:w-8/12 lg:w-7/12"
      >
        <div id="dialog-content" className="space-y-4 md:space-y-6">
          <div className="flex flex-col space-y-4 md:flex-row md:space-y-0">
            <div id="client-input" className="flex w-full flex-col md:w-1/2">
              <label className="flex flex-row space-x-1 text-sm font-medium text-gray-400">
                <span className="font-semibold">Client</span>
                <span className="text-red-600">*</span>
              </label>
              <input
                disabled={true}
                className="mt-1 w-full rounded-sm border-2 border-solid border-gray-400 px-4 py-2 text-gray-400 md:w-3/4 dark:border-transparent"
                value={clientName}
                name="client_name"
                type="text"
              ></input>
            </div>
            <div id="wbs-code-input" className="flex w-full flex-col md:w-1/2">
              <label className="flex flex-row space-x-1 text-sm font-medium text-gray-400">
                <span className="font-semibold">WBS code</span>
                <span className="text-red-600">*</span>
              </label>
              <input
                className="mt-1 rounded-sm border-2 border-solid border-gray-400 px-4 py-2 text-black focus:text-gray-800 focus:ring-2 focus:ring-purple-700 focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
                placeholder="Select code"
                value={wbsCode}
                onChange={handleInputChange}
                name="wbs_code"
                type="text"
              ></input>
            </div>
          </div>
          <div id="engagement-title-input" className="flex w-full flex-col">
            <label className="flex flex-row space-x-1 text-sm font-medium">
              <span className="font-semibold dark:text-white">
                Engagement Title
              </span>
              <span className="text-red-600">*</span>
            </label>
            <input
              className="mt-1 rounded-sm border-2 border-solid border-gray-400 px-4 py-2 text-black focus:text-gray-800 focus:ring-2 focus:ring-purple-700 focus:outline-hidden dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
              placeholder="Enter title"
              maxLength={250}
              name="title"
              value={title}
              onChange={handleInputChange}
              type="text"
            ></input>
            <span className="pt-1 text-right text-sm font-normal text-slate-400">
              {(title || "").length}/250
            </span>
          </div>
          <div id="add-users-input" className="flex w-full flex-col">
            <label className="flex flex-row space-x-1 text-sm font-medium">
              <span className="font-semibold dark:text-white">Add Users</span>
              <span className="text-red-600">*</span>
            </label>
            <div className="card justify-content-center mt-1 flex">
              <MultiSelect
                value={users}
                onChange={handleMultiSelectChange}
                optionDisabled={(option: EngagementUser) =>
                  !option.valid_custom_username || !option.valid_ssh_key
                }
                options={availableUsers}
                optionLabel="full_name"
                itemTemplate={EngagementUsersOptions}
                filter={false}
                placeholder="Select User"
                display="chip"
                name="users"
                className="md:w-20rem w-full border-2 border-solid border-gray-400 text-gray-400 dark:text-slate-100"
              />
            </div>
          </div>
          <div className="mt-4 flex justify-end space-x-2">
            <ActionButtons
              primaryButton={primaryButton}
              secondaryButton={secondaryButton}
            />
          </div>
        </div>
      </Modal>
    </>
  );
}
