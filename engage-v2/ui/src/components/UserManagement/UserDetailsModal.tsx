import { format } from "date-fns";
import { HiCheck } from "react-icons/hi";

import {
  getGetUserEngagementTitlesQuery<PERSON>ey,
  useGetUserEngagementTitles,
} from "../../client.ts";
import { EngagementUser } from "../../model";
import { capitalizeFirstLetter } from "../../utils/validationutils.ts";
import { ActionButtons, ButtonProps } from "../ActionButtons";
import Modal from "../Modal";

interface UserDetailsModalProps {
  selectedUser: EngagementUser | null;
  isOpen: boolean;
  closeModal: () => void;
}

export default function UserDetailsModal({
  selectedUser,
  isOpen,
  closeModal,
}: UserDetailsModalProps) {
  const primaryButton: ButtonProps = {
    label: "OK",
    onClick: () => {
      closeModal();
    },
    variant: "primary",
  };

  const { data: engagementsList } = useGetUserEngagementTitles(
    selectedUser?.id ?? "",
    {
      query: {
        queryKey: getGetUserEngagementTitlesQueryKey(selectedUser?.id ?? ""),
        enabled: !!selectedUser,
      },
    },
  );
  const engagementTitles = engagementsList?.engagements || [];

  return (
    <>
      <Modal
        title={"User Details"}
        isOpen={isOpen}
        closeModal={closeModal}
        widthClass="w-11/12 sm:w-10/12 md:w-8/12 lg:7/12"
      >
        <div id="dialog-content" className="lg:space-y-4">
          <div className="pb-4 text-sm text-gray-400 dark:text-slate-300">
            Last updated: {format(new Date(), "dd-MMM-yyyy | HH:mm")}
          </div>
          <div className="flex flex-col md:flex-row">
            <div className="flex w-full flex-row lg:w-1/2">
              <div id="client-input" className="flex flex-col">
                <label className="flex flex-row space-x-2 text-sm font-medium">
                  <span className="font-semibold text-gray-600 dark:text-slate-100">
                    ID:
                  </span>
                  <span className="font-semibold break-all text-gray-400 dark:text-slate-300">
                    {selectedUser?.id}
                  </span>
                </label>
              </div>
            </div>
          </div>
          <div className="mt-2 flex flex-col space-y-2 lg:mt-0 lg:flex-row lg:space-y-0">
            <div className="flex w-full flex-row lg:w-1/2">
              <div id="client-input" className="flex flex-col">
                <label className="flex flex-row space-x-1 text-sm font-medium">
                  <span className="font-semibold text-gray-600 dark:text-slate-100">
                    Name:
                  </span>
                  <span className="font-semibold text-gray-400 dark:text-slate-300">
                    {selectedUser?.full_name}
                  </span>
                </label>
              </div>
            </div>
          </div>
          <div className="mt-2 flex flex-col space-y-2 lg:mt-0 lg:flex-row lg:space-y-0">
            <div className="flex w-full flex-row lg:w-1/2">
              <div id="client-input" className="flex w-full flex-col">
                <label className="flex flex-row space-x-1 text-sm font-medium">
                  <span className="font-semibold text-gray-600 dark:text-slate-100">
                    Username:
                  </span>
                  <span className="font-semibold break-all text-gray-400 dark:text-slate-300">
                    {selectedUser?.username}
                  </span>
                </label>
              </div>
            </div>
            <div className="flex flex-row lg:w-1/2">
              <div id="wbs-code-input" className="flex w-full flex-col">
                <label className="flex flex-row space-x-1 text-sm font-medium">
                  <span className="font-semibold text-gray-600 dark:text-slate-100">
                    Role:
                  </span>
                  <span className="font-semibold text-gray-400 dark:text-slate-300">
                    {selectedUser?.app_role
                      ? capitalizeFirstLetter(
                          selectedUser?.app_role?.toString(),
                        )
                      : "-"}
                  </span>
                </label>
              </div>
            </div>
          </div>
          <div className="space-y-4 pt-4">
            <div className="font-bold text-gray-700 dark:text-slate-100">
              Engagements ({engagementTitles.length})
            </div>
            {engagementTitles.length > 0 ? (
              <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
                {engagementTitles.map((engagement, index) => (
                  <div key={index} className="flex items-center space-x-2">
                    <HiCheck className="h-5 w-5 text-green-500 dark:text-green-300" />
                    <span className="text-slate-500 dark:text-slate-300">
                      {engagement}
                    </span>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-slate-500 dark:text-slate-300">
                No assigned engagements.
              </div>
            )}
          </div>
          <div className="mt-4 flex justify-end space-x-2">
            <ActionButtons primaryButton={primaryButton} />
          </div>
        </div>
      </Modal>
    </>
  );
}
