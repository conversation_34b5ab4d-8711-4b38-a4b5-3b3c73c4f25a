import { BsFillGridFill } from "react-icons/bs";
import { HiMoon, HiSun } from "react-icons/hi";
import { HiBars3, HiBell } from "react-icons/hi2";

import { useTheme } from "../context/ThemeProvider";
import UserIcon from "./UserIcon.tsx";

export interface HeaderProps {
  toggleSideNav: () => void;
}

export default function Header({ toggleSideNav }: HeaderProps) {
  const { isDarkMode, toggleDarkMode } = useTheme();

  return (
    <div className="dark:bg-darkbg sticky top-0 z-20 w-full border-b-2 border-solid border-slate-200 bg-white px-4 py-2 lg:px-2 lg:py-4 dark:border-slate-500">
      <div className="flex items-center justify-between lg:hidden">
        <HiBars3
          onClick={toggleSideNav}
          className="h-6 w-6 cursor-pointer text-black dark:text-white"
        />
        <div className="relative mr-2 flex flex-row items-center space-x-3">
          {isDarkMode && (
            <HiSun
              onClick={toggleDarkMode}
              className={`${isDarkMode ? "text-white" : "text-black"} h-5 w-5 cursor-pointer`}
            />
          )}
          {!isDarkMode && (
            <HiMoon
              onClick={toggleDarkMode}
              className={`${isDarkMode ? "text-white" : "text-black"} h-5 w-5 cursor-pointer`}
            />
          )}
          <HiBell
            className={`${isDarkMode ? "text-white" : "text-black"} h-5 w-5 cursor-pointer`}
          />
          <BsFillGridFill
            className={`${isDarkMode ? "text-white" : "text-black"} h-5 w-5 cursor-pointer`}
          />
          <UserIcon />
        </div>
      </div>
      <div className="hidden flex-row items-center justify-end space-x-2 lg:flex">
        <div className="relative mr-2 flex flex-row items-center space-x-3">
          {isDarkMode && (
            <HiSun
              onClick={toggleDarkMode}
              className={`${isDarkMode ? "text-white" : "text-black"} h-6 w-6 cursor-pointer`}
            />
          )}
          {!isDarkMode && (
            <HiMoon
              onClick={toggleDarkMode}
              className={`${isDarkMode ? "text-white" : "text-black"} h-6 w-6 cursor-pointer`}
            />
          )}
          <HiBell
            className={`${isDarkMode ? "text-white" : "text-black"} h-6 w-6 cursor-pointer`}
          />
          <BsFillGridFill
            className={`${isDarkMode ? "text-white" : "text-black"} h-6 w-6 cursor-pointer`}
          />
          <UserIcon />
        </div>
      </div>
    </div>
  );
}
