
import { useState } from "react";
import {
    usePostCreateAzureTenant,
    useGetEngagementAzureTenants,
    getGetEngagementAzureTenantsQueryKey,
} from "../../client";
import { useQueryClient } from "@tanstack/react-query";
import { ErrorMessage } from "../ErrorMessageModal.tsx";
import { toast } from "react-toastify";
import { VscAzure } from "react-icons/vsc";
import { createRefetchStatus } from "../../utils/refetchEngagementsStatus.ts";
import { getAzureStatusIcon } from "../../utils/assets.tsx";

interface AzureTenantForm {
    azure_tenant_id: string;
    azure_subscription_id: string;
    azure_app_id: string;
    azure_app_secret: string;
}

interface Props {
    engagementID: string;
    closeModal: () => void;
}

export default function AzureTenantFlow({ engagementID, closeModal }: Props) {
    // Single tenant state (no array)
    const [tenant, setTenant] = useState<AzureTenantForm>({
        azure_tenant_id: "",
        azure_subscription_id: "",
        azure_app_id: "",
        azure_app_secret: "",
    });

    const [errors, setErrors] = useState<Partial<Record<keyof AzureTenantForm, string>>>({});
    const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);
    const [isOpenErrorModal, setIsOpenErrorModal] = useState(false);
    const [hasSubmitted, setHasSubmitted] = useState(false);
    const [hasChanges, setHasChanges] = useState(false);
    const [searchTerm, setSearchTerm] = useState("");
    const [forceRefetch, setForceRefetch] = useState(false);

    const queryClient = useQueryClient();

    const {
        data: azureTenants,
        isLoading: tenantsLoading,
        isError: tenantsError,
    } = useGetEngagementAzureTenants(engagementID, {
        query: {
            refetchInterval: forceRefetch
                ? 1000  // More frequent polling during status check
                : createRefetchStatus("tenants", {
                    refetchIntervalSeconds: 5000,
                    statusField: "account_cloud_status",  // Fixed field name
                }),
        },
    });

    const handleMutationError = (error: any) => {
        const detailRaw = error?.response?.data?.detail;
        console.log("handleMutationError called with detail:", detailRaw);

        if (typeof detailRaw !== "string") {
            console.log("detail is not a string");
            setErrorMessage({
                title: "Unexpected Error",
                message: "An unknown error occurred while processing your request.",
            });
            setIsOpenErrorModal(true);
            return;
        }

        const prefix = "Validation failed: ";
        let detail = detailRaw;

        if (detailRaw.startsWith(prefix)) {
            detail = detailRaw.substring(prefix.length).trim();
            console.log("Stripped prefix, detail now:", detail);
        }

        const userInputErrorMap: Partial<Record<keyof AzureTenantForm, string>> = {};
        const globalErrors: string[] = [];

        // inputRegex for input errors
        const inputRegex = /\{([\w-]+)(?:\s+([\w-]+))?\s+(Tenant|Subscription)\s+(.+?)\}/g;
        let match: RegExpExecArray | null;

        while ((match = inputRegex.exec(detail)) !== null) {
            console.log("Matched input error:", match);
            const [_, tenantID, subscriptionID, type, message] = match;

            if (type === "Tenant" && tenant.azure_tenant_id === tenantID) {
                userInputErrorMap.azure_tenant_id = message;
            }
            if (type === "Subscription" && tenant.azure_subscription_id === subscriptionID) {
                userInputErrorMap.azure_subscription_id = message;
            }
        }

        // secretRegex for secret errors
        const secretRegex = /\{([\w-]+)\s+([\w-]+)\s+(.+?)\}/g;
        while ((match = secretRegex.exec(detail)) !== null) {
            console.log("Matched secret/global error:", match);
            const [_, tenantID, subscriptionID, message] = match;

            if (message.includes("Failed to save secrets")) {
                globalErrors.push(`Tenant ID: ${tenantID}, Subscription ID: ${subscriptionID}: ${message}`);
            }
        }

        console.log("userInputErrorMap:", userInputErrorMap);
        console.log("globalErrors:", globalErrors);

        setErrors(userInputErrorMap);

        if (globalErrors.length > 0) {
            console.log("Setting error modal open with global errors");
            setErrorMessage({
                title: "Azure Tenant Secret Creation Failed",
                message: globalErrors.join("\n"),
            });
            setIsOpenErrorModal(true);
        } else if (Object.keys(userInputErrorMap).length === 0) {
            console.log("Setting error modal open with generic validation message");
            setErrorMessage({
                title: "Validation Failed",
                message: detailRaw,
            });
            setIsOpenErrorModal(true);
        }
    };

    const mutation = usePostCreateAzureTenant({
        mutation: {
            onError: handleMutationError,
            onSuccess: () => {
                setForceRefetch(true);
                setTimeout(() => setForceRefetch(false), 15000); // Extended to 15 seconds for status check
                toast.success("Tenant added successfully - checking subscription status...");
                queryClient.invalidateQueries({
                    queryKey: getGetEngagementAzureTenantsQueryKey(engagementID),
                });
                setHasSubmitted(true);
                setHasChanges(false);
                setTenant({
                    azure_tenant_id: "",
                    azure_subscription_id: "",
                    azure_app_id: "",
                    azure_app_secret: "",
                });
                setErrors({});
            },
        },
    });

    const handleChange = (field: keyof AzureTenantForm, value: string) => {
        setHasChanges(true);
        setHasSubmitted(false);

        setTenant((prev) => ({
            ...prev,
            [field]: value,
        }));

        setErrors((prevErrors) => {
            const { [field]: _, ...rest } = prevErrors;
            return rest;
        });
    };

    const isFormValid = () => {
        return tenant.azure_tenant_id.trim() !== "" && Object.keys(errors).length === 0;
    };

    const validateTenant = () => {
        const formErrors: Partial<Record<keyof AzureTenantForm, string>> = {};

        const hasSubscription = tenant.azure_subscription_id.trim() !== "";
        const hasAppId = tenant.azure_app_id.trim() !== "";
        const hasAppSecret = tenant.azure_app_secret.trim() !== "";

        if (hasSubscription && (!hasAppId || !hasAppSecret)) {
            if (!hasAppId) formErrors.azure_app_id = "App ID is required when subscription is provided.";
            if (!hasAppSecret) formErrors.azure_app_secret = "App Secret is required when subscription is provided.";
        }

        if (!hasSubscription && (hasAppId || hasAppSecret)) {
            if (hasAppId) formErrors.azure_app_id = "Remove App ID when subscription is not provided.";
            if (hasAppSecret) formErrors.azure_app_secret = "Remove App Secret when subscription is not provided.";
        }

        setErrors(formErrors);
        return Object.keys(formErrors).length === 0;
    };

    const handleSave = async () => {
        if (!validateTenant()) {
            toast.error("Please fix validation errors before submitting.");
            return;
        }

        const tenantData = {
            engagementId: engagementID,
            data: {
                azure_tenant_id: tenant.azure_tenant_id.trim(),
                azure_subscription_id: tenant.azure_subscription_id.trim(),
                azure_app_id: tenant.azure_app_id.trim(),
                azure_app_secret: tenant.azure_app_secret.trim(),
            },
        };

        mutation.mutate(tenantData);
    };


    return (
        <>
            <div className="space-y-4 max-h-[80vh] overflow-y-auto pr-1">
                {/* Azure Tenants Table */}
                <div className="mb-6">
                    <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                        <VscAzure className="inline-block mr-2 h-5 w-5 text-blue-500" />
                        Existing Azure Tenants
                    </h3>

                    {tenantsLoading ? (
                        <div className="flex justify-center items-center h-24">
                            <div className="animate-pulse flex space-x-2">
                                <div className="h-2 w-2 bg-purple-600 rounded-full"></div>
                                <div className="h-2 w-2 bg-purple-600 rounded-full"></div>
                                <div className="h-2 w-2 bg-purple-600 rounded-full"></div>
                            </div>
                        </div>
                    ) : tenantsError ? (
                        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-md p-4 text-red-600 dark:text-red-400">
                            Failed to load Azure tenants. Please try again.
                        </div>
                    ) : (
                        <>
                            {(azureTenants?.tenants?.length ?? 0) > 0 ? (
                                <>
                                    <div className="overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 shadow">
                                        <input
                                            type="text"
                                            placeholder="Search by Tenant ID or Subscription ID"
                                            className="w-full border p-2 rounded-md mb-0 border-b"
                                            value={searchTerm}
                                            onChange={(e) => setSearchTerm(e.target.value)}
                                        />
                                        <div className="overflow-x-auto">
                                            <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
                                                <thead className="bg-gray-50 dark:bg-gray-800">
                                                    <tr>
                                                        <th
                                                            scope="col"
                                                            className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                                                        >
                                                            Tenant ID
                                                        </th>
                                                        <th
                                                            scope="col"
                                                            className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                                                        >
                                                            Subscription ID
                                                        </th>
                                                        <th
                                                            scope="col"
                                                            className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                                                        >
                                                            Secrets Saved
                                                        </th>
                                                        <th
                                                            scope="col"
                                                            className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                                                        >
                                                            Adding Status
                                                        </th>
                                                        <th
                                                            scope="col"
                                                            className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                                                        >
                                                            Subscription Cloud Status
                                                        </th>
                                                        <th
                                                            scope="col"
                                                            className="px-4 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-300 uppercase tracking-wider"
                                                        >
                                                            Added At
                                                        </th>
                                                    </tr>
                                                </thead>
                                                <tbody className="bg-white dark:bg-gray-900 divide-y divide-gray-200 dark:divide-gray-800">
                                                    {(() => {
                                                        const filtered = (azureTenants?.tenants ?? []).filter(
                                                            (t) =>
                                                                t.tenant_id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                                                                t.subscription_id?.toLowerCase().includes(searchTerm.toLowerCase())
                                                        );

                                                        return filtered.length > 0 ? (
                                                            filtered.map((tenant) => (
                                                                <tr
                                                                    key={`${tenant.tenant_id}-${tenant.subscription_id}`}
                                                                    className="hover:bg-gray-50 dark:hover:bg-gray-800/60 transition-colors"
                                                                >
                                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 font-mono">
                                                                        {tenant.tenant_id}
                                                                    </td>
                                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 font-mono">
                                                                        {tenant.subscription_id || "-"}
                                                                    </td>
                                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-900 dark:text-gray-100 font-mono">
                                                                        {String(tenant.secrets_saved)}
                                                                    </td>
                                                                    <td className="px-4 py-3 whitespace-nowrap text-sm">
                                                                        <span
                                                                            className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${tenant.creation_status === "SUCCESS"
                                                                                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400"
                                                                                : tenant.creation_status === "FAILED"
                                                                                    ? "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400"
                                                                                    : "bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400"
                                                                                }`}
                                                                        >
                                                                            {tenant.creation_status}
                                                                        </span>
                                                                    </td>
                                                                    <td className="px-4 py-3 whitespace-nowrap text-sm">
                                                                        <div className="flex items-center space-x-2">
                                                                            {tenant.account_cloud_status ? (
                                                                                <>
                                                                                    {getAzureStatusIcon(tenant.account_cloud_status)}
                                                                                    <span className="text-sm text-gray-900 dark:text-gray-100">
                                                                                        {tenant.account_cloud_status}
                                                                                    </span>
                                                                                </>
                                                                            ) : (
                                                                                <span className="text-sm text-gray-500 dark:text-gray-400">
                                                                                    -
                                                                                </span>
                                                                            )}
                                                                        </div>
                                                                    </td>
                                                                    <td className="px-4 py-3 whitespace-nowrap text-sm text-gray-500 dark:text-gray-400">
                                                                        {new Date(tenant.created_at).toLocaleString()}
                                                                    </td>
                                                                </tr>
                                                            ))
                                                        ) : (
                                                            <tr>
                                                                <td
                                                                    colSpan={6}
                                                                    className="px-4 py-6 text-center text-sm text-gray-500 dark:text-gray-400"
                                                                >
                                                                    No matching tenants found.
                                                                </td>
                                                            </tr>
                                                        );
                                                    })()}
                                                </tbody>
                                            </table>
                                        </div>
                                    </div>
                                </>
                            ) : (
                                <div className="p-6 text-center text-gray-500 bg-gray-50 dark:bg-gray-800 dark:text-gray-400 rounded-lg border border-gray-200 dark:border-gray-700">
                                    No tenants found for this engagement. Add your first tenant below.
                                </div>
                            )}
                        </>
                    )}
                </div>

                <hr className="border-gray-200 dark:border-gray-700" />

                {/* Tenant Input Form */}
                <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-5 shadow-sm">
                    <h3 className="text-lg font-semibold mb-3 text-gray-900 dark:text-white">
                        <VscAzure className="inline-block mr-2 h-5 w-5 text-blue-500" />
                        Add Azure Tenant
                    </h3>

                    <p className="text-sm text-gray-600 dark:text-gray-300 mb-4">
                        Add an Azure tenant to this engagement. You'll need the Tenant ID and optionally Subscription ID with credentials.
                    </p>

                    {hasSubmitted && (
                        <div className="mb-4 p-3 bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-md text-green-700 dark:text-green-400">
                            Tenant submitted. You can track the creation status in the table above.
                        </div>
                    )}

                    <div className="space-y-2 border p-4 rounded-md bg-white dark:bg-gray-800 dark:border-gray-700 mb-4">
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <label className="block">
                                Tenant ID <span className="text-red-500">*</span>
                                <input
                                    className="w-full border p-2 rounded-md mt-1 dark:bg-gray-700 dark:border-gray-600 dark:text-white"
                                    value={tenant.azure_tenant_id}
                                    onChange={(e) => handleChange("azure_tenant_id", e.target.value)}
                                    placeholder="Azure Tenant ID"
                                    required
                                />
                                {errors.azure_tenant_id && (
                                    <p className="text-red-500 text-sm mt-1">
                                        {errors.azure_tenant_id}
                                    </p>
                                )}
                            </label>

                            <label className="block">
                                Subscription ID
                                <input
                                    className="w-full border p-2 rounded-md mt-1"
                                    value={tenant.azure_subscription_id}
                                    onChange={(e) => handleChange("azure_subscription_id", e.target.value)}
                                />
                                {errors.azure_subscription_id && (
                                    <p className="text-red-500 text-sm mt-1">
                                        {errors.azure_subscription_id}
                                    </p>
                                )}
                            </label>

                            <label className="block">
                                App ID
                                <input
                                    className="w-full border p-2 rounded-md mt-1"
                                    value={tenant.azure_app_id}
                                    onChange={(e) => handleChange("azure_app_id", e.target.value)}
                                />
                                {errors.azure_app_id && (
                                    <div className="text-red-500 text-sm">{errors.azure_app_id}</div>
                                )}
                            </label>

                            <label className="block">
                                App Secret
                                <input
                                    type="password"
                                    className="w-full border p-2 rounded-md mt-1"
                                    value={tenant.azure_app_secret}
                                    onChange={(e) => handleChange("azure_app_secret", e.target.value)}
                                />
                                {errors.azure_app_secret && (
                                    <div className="text-red-500 text-sm">{errors.azure_app_secret}</div>
                                )}
                            </label>
                        </div>
                    </div>

                    <div className="flex justify-end pt-4 border-t mt-6">
                        <div className="flex gap-4">
                            <button
                                type="button"
                                onClick={closeModal}
                                className="w-full rounded-md border border-solid border-slate-300 px-6 py-3 hover:bg-purple-700 hover:text-white dark:bg-transparent md:w-max"
                            >
                                Cancel
                            </button>
                            <button
                                type="button"
                                onClick={handleSave}
                                disabled={!isFormValid() || !hasChanges}
                                className={`px-6 py-3 rounded-md text-white ${!isFormValid() || !hasChanges
                                    ? "bg-gray-400 cursor-not-allowed"
                                    : "bg-purple-700 hover:bg-purple-800"
                                    }`}
                            >
                                {hasSubmitted ? "Saved" : "Save"}
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            {isOpenErrorModal && errorMessage && (
                <div className="fixed inset-0 flex items-center justify-center z-50 pointer-events-none">
                    <div className="bg-white dark:bg-gray-800 rounded-lg p-6 max-w-md w-full shadow-lg pointer-events-auto">
                        <h3 className="text-xl font-semibold mb-4 text-black dark:text-white">
                            {errorMessage.title || "Error"}
                        </h3>
                        <p className="mb-6 text-gray-700 dark:text-gray-300">{errorMessage.message}</p>
                        <div className="flex justify-end">
                            <button
                                onClick={() => setIsOpenErrorModal(false)}
                                className="px-4 py-2 bg-purple-700 text-white rounded hover:bg-purple-800"
                            >
                                Close
                            </button>
                        </div>
                    </div>
                </div>
            )}

        </>
    );
}

