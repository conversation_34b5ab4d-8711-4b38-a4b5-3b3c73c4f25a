import { useState } from "react";
import { BsPlusSquare } from "react-icons/bs";
import AddAccountsModal from "./AccountsManagementModal.tsx";

type Props = {
    engagementID: string;
};

export default function AddAccountsButton({ engagementID }: Props) {
    const [isOpen, setIsOpen] = useState(false);

    function openModal() {
        setIsOpen(true);
    }

    function closeModal() {
        setIsOpen(false);
    }

    return (
        <>
            <div className="inset-0 flex w-1/3 flex-row items-center justify-center md:mr-4">
                <button
                    type="button"
                    onClick={openModal}
                    className="flex items-center space-x-2 rounded-md px-6 py-3 text-sm font-medium bg-indigo-700 text-white hover:bg-indigo-800"
                >
                    <BsPlusSquare className="h-5 w-5" />
                    <span>Manage Accounts</span>
                </button>
            </div>

            <AddAccountsModal
                isOpen={isOpen}
                closeModal={closeModal}
                engagementID={engagementID}
            />
        </>
    );

}
