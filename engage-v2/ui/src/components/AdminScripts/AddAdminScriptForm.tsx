import { useState } from "react";

import { CreateAdminScriptInputBody } from "../../model";
import { ActionButtons, ButtonProps } from "../ActionButtons";
import HighlightedTextarea from "../HighlightedTextArea";

interface ScriptFormProps {
  setShowScriptForm: (showScriptForm: boolean) => void;
  handleCreateScript: (data: CreateAdminScriptInputBody) => void;
}

export default function AddAdminScriptForm({
  setShowScriptForm,
  handleCreateScript,
}: ScriptFormProps) {
  const [scriptData, setScriptData] = useState<CreateAdminScriptInputBody>({
    name: "",
    description: "",
    content: "",
    script_type: "ADMIN",
  });

  const primaryAddScriptButton: ButtonProps = {
    label: "Add Script",
    onClick: () => {
      setShowScriptForm(false);
      handleCreateScript(scriptData);
      setScriptData({
        name: "",
        description: "",
        content: "",
        script_type: "ADMIN",
      });
    },
    variant: "primary",
    disabled: !(
      scriptData.name !== "" &&
      scriptData.description !== "" &&
      scriptData.content !== ""
    ),
  };

  const secondaryAddScriptButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      setShowScriptForm(false);
      setScriptData({
        name: "",
        description: "",
        content: "",
        script_type: "ADMIN",
      });
    },
    variant: "secondary",
  };

  const handleScriptChange = (e: {
    target: { name: string; value: string };
  }) => {
    const { name, value } = e.target;
    setScriptData({
      ...scriptData,
      [name]: value,
    });
  };

  return (
    <>
      <hr className="my-8 h-px border-0 bg-gray-200 dark:bg-gray-700" />
      <div className="mb-4 text-xl font-semibold text-black dark:text-white">
        Add an Admin Script
      </div>
      <div className="mb-4 flex w-full flex-col space-y-4 md:mb-8 md:flex-row md:space-y-0">
        <div className="flex w-full flex-col md:w-1/2">
          <label className="mb-1 flex flex-row space-x-1 dark:text-white">
            <span className="font-semibold">Name</span>
            <span className="text-red-600">*</span>
          </label>
          <input
            className="w-full rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:outline-hidden focus:ring-2 focus:ring-purple-700 dark:border-transparent dark:bg-slate-600 dark:text-slate-100 md:w-5/6"
            type="text"
            name="name"
            value={scriptData.name}
            onChange={handleScriptChange}
            placeholder="Name"
          />
        </div>
        <div className="flex w-full flex-col md:w-1/2">
          <label className="mb-1 flex flex-row space-x-1 dark:text-white">
            <span className="font-semibold">Description</span>
            <span className="text-red-600">*</span>
          </label>
          <input
            className="rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:outline-hidden focus:ring-2 focus:ring-purple-700 dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
            type="text"
            name="description"
            value={scriptData.description}
            onChange={handleScriptChange}
            placeholder="Script description"
          />
        </div>
      </div>
      <div className="mb-4">
        <label className="mb-1 flex flex-row space-x-1 dark:text-white">
          <span className="font-semibold">Content</span>
          <span className="text-red-600">*</span>
        </label>
        <textarea
          className="w-full whitespace-pre-wrap break-words rounded-sm border border-solid border-gray-400 bg-gray-100 bg-transparent p-4 text-base font-normal focus:outline-hidden focus:ring-2 focus:ring-purple-700 dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
          value={scriptData.content}
          name="content"
          onChange={handleScriptChange}
          rows={4}
          placeholder="Add a script"
        />
      </div>
      <div className="mb-8">
        <HighlightedTextarea value={scriptData.content} />
      </div>
      <ActionButtons
        primaryButton={primaryAddScriptButton}
        secondaryButton={secondaryAddScriptButton}
      />
    </>
  );
}
