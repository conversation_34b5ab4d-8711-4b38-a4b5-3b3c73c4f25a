import { useEffect, useState } from "react";

import { EditScriptInputBody, Script } from "../../model";
import { ActionButtons, ButtonProps } from "../ActionButtons";
import HighlightedTextarea from "../HighlightedTextArea";
import Modal from "../Modal";

interface ScriptFormProps {
  setShowScriptForm: () => void;
  handleEditScript: (scriptId: string, data: EditScriptInputBody) => void;
  script: Script;
  isOpen: boolean;
  closeModal: () => void;
}

export default function EditScriptForm({
  setShowScriptForm,
  handleEditScript,
  script,
  isOpen,
  closeModal,
}: ScriptFormProps) {
  const [scriptData, setScriptData] = useState<EditScriptInputBody>({
    name: script.name,
    description: script.description,
    content: script.content,
    script_type: script.script_type,
  });

  useEffect(() => {
    setScriptData({
      name: script.name,
      description: script.description,
      content: script.content,
      script_type: script.script_type,
    });
  }, [script]);

  const primaryAddScriptButton: ButtonProps = {
    label: "Save Changes",
    onClick: () => {
      setShowScriptForm();
      handleEditScript(script.id, scriptData);
      setScriptData({
        name: "",
        description: "",
        content: "",
        script_type: "STANDARD",
      });
    },
    variant: "primary",
    disabled: !(scriptData.name !== "" && scriptData.description !== ""),
  };

  const secondaryAddScriptButton: ButtonProps = {
    label: "Cancel",
    onClick: () => {
      setShowScriptForm();
      setScriptData({
        name: "",
        description: "",
        content: "",
        script_type: "STANDARD",
      });
    },
    variant: "secondary",
  };

  const handleScriptChange = (e: {
    target: { name: string; value: string };
  }) => {
    const { name, value } = e.target;
    setScriptData({
      ...scriptData,
      [name]: value,
    });
  };

  return (
    <>
      <Modal
        title={`Edit ${script.name}`}
        isOpen={isOpen}
        closeModal={closeModal}
      >
        <div className="mb-4 flex w-full flex-col space-y-4 md:mb-8 md:ml-4 md:flex-row md:space-y-0">
          <div className="flex w-full flex-col md:w-1/2">
            <label className="mb-1 flex flex-row space-x-1 dark:text-white">
              <span className="font-semibold">Name</span>
              <span className="text-red-600">*</span>
            </label>
            <input
              className="rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:outline-hidden focus:ring-2 focus:ring-purple-700 dark:border-transparent dark:bg-slate-600 dark:text-slate-100 md:w-5/6"
              type="text"
              name="name"
              value={scriptData.name}
              onChange={handleScriptChange}
              placeholder="Name"
            />
          </div>
          <div className="flex w-full flex-col md:w-1/2">
            <label className="mb-1 flex flex-row space-x-1 dark:text-white">
              <span className="font-semibold">Description</span>
              <span className="text-red-600">*</span>
            </label>
            <input
              className="rounded-sm border border-solid border-gray-400 px-4 py-2 text-gray-700 focus:outline-hidden focus:ring-2 focus:ring-purple-700 dark:border-transparent dark:bg-slate-600 dark:text-slate-100 md:mr-4"
              type="text"
              name="description"
              value={scriptData.description}
              onChange={handleScriptChange}
              placeholder="Script description"
            />
          </div>
        </div>
        <div className="mb-4">
          <label className="mb-1 flex flex-row space-x-1 dark:text-white">
            <span className="font-semibold">Content</span>
            <span className="text-red-600">*</span>
          </label>
          <textarea
            className="w-full max-w-(--breakpoint-lg) whitespace-pre-wrap break-words rounded-sm border border-solid border-gray-400 bg-gray-100 bg-transparent p-4 text-base font-normal focus:outline-hidden focus:ring-2 focus:ring-purple-700 dark:border-transparent dark:bg-slate-600 dark:text-slate-100"
            value={scriptData.content}
            name="content"
            onChange={handleScriptChange}
            rows={4}
            placeholder="Add a script"
          />
        </div>
        <div className="mb-8">
          <HighlightedTextarea value={scriptData.content} />
        </div>
        <div className="mt-4 flex justify-end space-x-2">
          <ActionButtons
            primaryButton={primaryAddScriptButton}
            secondaryButton={secondaryAddScriptButton}
          />
        </div>
      </Modal>
    </>
  );
}
