import { EditScriptInputBody } from "../../model";
import { ActionButtons, ButtonProps } from "../ActionButtons";
import HighlightedTextarea from "../HighlightedTextArea";
import Modal from "../Modal";

type ViewScriptModalProps = {
  isOpen: boolean;
  closeModal: () => void;
  scriptDetails?: EditScriptInputBody;
};

export default function ViewScriptModal({
  isOpen,
  closeModal,
  scriptDetails,
}: ViewScriptModalProps) {
  const primaryButton: ButtonProps = {
    label: "Close",
    onClick: () => {
      closeModal();
    },
    variant: "primary",
  };

  return (
    <Modal
      title={`View ${scriptDetails?.name}`}
      isOpen={isOpen}
      closeModal={closeModal}
    >
      <div className="flex flex-col">
        <div className="mb-1 font-semibold dark:text-slate-100">
          Description:
        </div>
        <div className="ml-4 pb-4 dark:text-slate-300">
          {scriptDetails?.description}
        </div>
        <div className="font-semibold dark:text-slate-100">Content:</div>
        <HighlightedTextarea value={scriptDetails?.content} />
      </div>
      <div className="mt-4 flex justify-end space-x-2">
        <ActionButtons primaryButton={primaryButton} />
      </div>
    </Modal>
  );
}
