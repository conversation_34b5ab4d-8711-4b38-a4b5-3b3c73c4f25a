import { NodeType } from "./types.ts";

type Props = {
  closeModal: () => void;
  nodeType: NodeType | null;
  setNodeType: (nodeType: NodeType) => void;
  updateCreateNodeScreen: () => void;
};

export default function SelectNodeType({ closeModal, setNodeType }: Props) {
  return (
    <div className="dark:bg-slate-700">
      <div className="py-6 text-xl font-bold dark:text-white">
        Select Node Type
      </div>
      <div
        id="node-type-selection"
        className="grid grid-cols-1 gap-6 sm:grid-cols-3 xl:grid-cols-4"
      >
        <button
          onClick={() => {
            setNodeType(NodeType.CloudInstance);
          }}
          type="button"
          className="flex flex-col justify-center space-y-4 rounded-md border border-solid border-gray-400 bg-white fill-svg-purple p-4 text-black drop-shadow-lg hover:bg-purple-700 hover:fill-white hover:text-white dark:border-transparent dark:bg-slate-600 dark:fill-purple-400 dark:text-white dark:hover:bg-purple-700 dark:hover:fill-white xl:w-60"
        >
          <svg
            width="40"
            height="40"
            viewBox="0 0 40 40"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M35.7313 18.925C35.9062 18.3063 36 17.6625 36 17C36 13.1312 32.8688 10 29 10C27.9562 10 26.9438 10.225 26 10.675C24.025 7.8625 20.7313 6 17 6C11.1 6 6.26875 10.6562 6.0125 16.5125C2.45 17.7625 0 21.1438 0 25C0 29.975 4.025 34 9 34H32C36.4188 34 40 30.425 40 26C40 23.0625 38.3875 20.325 35.7313 18.925ZM32 32H9C5.13125 32 2 28.8687 2 25C2 21.45 4.6375 18.5188 8.0625 18.0625C8.01875 17.7125 8 17.3562 8 17C8 12.0312 12.0312 8 17 8C20.7687 8 23.9937 10.3125 25.3375 13.6C26.25 12.6188 27.55 12 29 12C31.7625 12 34 14.2375 34 17C34 18.1562 33.6063 19.225 32.9437 20.075C35.8125 20.525 38 23.0063 38 26C38 29.3125 35.3125 32 32 32Z"
            />
          </svg>
          <span className="text-lg font-semibold">Cloud Instance</span>
        </button>
        <button
          onClick={() => {
            setNodeType(NodeType.EmailAddress);
          }}
          type="button"
          className="flex flex-col justify-center space-y-4 rounded-md border border-solid border-gray-400 bg-white fill-svg-purple p-4 text-black drop-shadow-lg hover:bg-purple-700 hover:fill-white hover:text-white dark:border-transparent dark:bg-slate-600 dark:fill-purple-400 dark:text-white dark:hover:bg-purple-700 dark:hover:fill-white xl:w-60"
        >
          <svg
            width="40"
            height="40"
            viewBox="0 0 40 40"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M20 0.625C9.29227 0.625 0.625 9.29055 0.625 20C0.625 30.7077 9.29055 39.375 20 39.375C24.1348 39.375 28.1781 38.0408 31.4916 35.6001C31.9213 35.2835 31.9934 34.6685 31.6552 34.2555L31.2588 33.7713C30.9449 33.3877 30.3844 33.3116 29.9848 33.6049C27.1039 35.7194 23.5917 36.875 20 36.875C10.674 36.875 3.125 29.3276 3.125 20C3.125 10.6738 10.6724 3.125 20 3.125C29.2535 3.125 36.875 9.37687 36.875 18.75C36.875 24.4432 32.7485 27.2063 27.8074 27.2063C26.2657 27.2063 25.9563 26.3613 26.2838 24.5604L28.7892 11.7423C28.9024 11.1634 28.4591 10.625 27.8691 10.625H26.8988C26.4497 10.625 26.0637 10.9434 25.9784 11.3842C25.7991 12.3097 25.7549 12.4361 25.6767 13.3879C24.7604 11.2724 22.658 10.0267 19.9443 10.0267C14.6284 10.0267 9.53992 14.9447 9.53992 22.2945C9.53992 27.1059 12.1895 29.9784 16.6277 29.9784C19.7315 29.9784 22.1434 28.0816 23.3959 26.3079C23.3012 28.4787 24.7595 29.6058 26.9877 29.6058C35.3976 29.6058 39.375 25.1351 39.375 18.75C39.375 8.11047 30.7705 0.625 20 0.625ZM17.0375 27.3927C14.1718 27.3927 12.4609 25.4312 12.4609 22.1454C12.4609 16.3102 16.427 12.6497 20.056 12.6497C23.0917 12.6497 24.5952 14.8208 24.5952 17.7852C24.5952 22.3269 21.5977 27.3927 17.0375 27.3927Z"
            />
          </svg>
          <span className="text-lg font-semibold">Email Address</span>
        </button>
        <button
          onClick={() => {
            setNodeType(NodeType.Host);
          }}
          type="button"
          className="flex flex-col justify-center space-y-4 rounded-md border border-solid border-gray-400 bg-white fill-svg-purple p-4 text-black drop-shadow-lg hover:bg-purple-700 hover:fill-white hover:text-white dark:border-transparent dark:bg-slate-600 dark:fill-purple-400 dark:text-white dark:hover:bg-purple-700 dark:hover:fill-white xl:w-60"
        >
          <svg
            width="40"
            height="40"
            viewBox="0 0 40 40"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M29.375 20C29.375 18.9645 30.2145 18.125 31.25 18.125C32.2855 18.125 33.125 18.9645 33.125 20C33.125 21.0355 32.2855 21.875 31.25 21.875C30.2145 21.875 29.375 21.0355 29.375 20ZM26.25 21.875C27.2855 21.875 28.125 21.0355 28.125 20C28.125 18.9645 27.2855 18.125 26.25 18.125C25.2145 18.125 24.375 18.9645 24.375 20C24.375 21.0355 25.2145 21.875 26.25 21.875ZM40 11.875C40 12.8356 39.6384 13.7115 39.0444 14.375C39.6384 15.0385 40 15.9144 40 16.875V23.125C40 24.0856 39.6384 24.9615 39.0444 25.625C39.6384 26.2885 40 27.1644 40 28.125V34.375C40 36.4461 38.3211 38.125 36.25 38.125H3.75C1.67891 38.125 0 36.4461 0 34.375V28.125C0 27.1644 0.361641 26.2885 0.955625 25.625C0.361641 24.9615 0 24.0856 0 23.125V16.875C0 15.9144 0.361641 15.0385 0.955625 14.375C0.361641 13.7115 0 12.8356 0 11.875V5.625C0 3.55391 1.67891 1.875 3.75 1.875H36.25C38.3211 1.875 40 3.55391 40 5.625V11.875ZM2.5 11.875C2.5 12.5642 3.06078 13.125 3.75 13.125H36.25C36.9392 13.125 37.5 12.5642 37.5 11.875V5.625C37.5 4.93578 36.9392 4.375 36.25 4.375H3.75C3.06078 4.375 2.5 4.93578 2.5 5.625V11.875ZM36.25 15.625H3.75C3.06078 15.625 2.5 16.1858 2.5 16.875V23.125C2.5 23.8142 3.06078 24.375 3.75 24.375H36.25C36.9392 24.375 37.5 23.8142 37.5 23.125V16.875C37.5 16.1858 36.9392 15.625 36.25 15.625ZM37.5 28.125C37.5 27.4358 36.9392 26.875 36.25 26.875H3.75C3.06078 26.875 2.5 27.4358 2.5 28.125V34.375C2.5 35.0642 3.06078 35.625 3.75 35.625H36.25C36.9392 35.625 37.5 35.0642 37.5 34.375V28.125ZM31.25 10.625C32.2855 10.625 33.125 9.78555 33.125 8.75C33.125 7.71445 32.2855 6.875 31.25 6.875C30.2145 6.875 29.375 7.71445 29.375 8.75C29.375 9.78555 30.2145 10.625 31.25 10.625ZM26.25 10.625C27.2855 10.625 28.125 9.78555 28.125 8.75C28.125 7.71445 27.2855 6.875 26.25 6.875C25.2145 6.875 24.375 7.71445 24.375 8.75C24.375 9.78555 25.2145 10.625 26.25 10.625ZM31.25 29.375C30.2145 29.375 29.375 30.2145 29.375 31.25C29.375 32.2855 30.2145 33.125 31.25 33.125C32.2855 33.125 33.125 32.2855 33.125 31.25C33.125 30.2145 32.2855 29.375 31.25 29.375ZM26.25 29.375C25.2145 29.375 24.375 30.2145 24.375 31.25C24.375 32.2855 25.2145 33.125 26.25 33.125C27.2855 33.125 28.125 32.2855 28.125 31.25C28.125 30.2145 27.2855 29.375 26.25 29.375Z"
            />
          </svg>
          <span className="text-lg font-semibold">Host</span>
        </button>
        <button
          onClick={() => {
            setNodeType(NodeType.Person);
          }}
          type="button"
          className="flex flex-col justify-center space-y-4 rounded-md border border-solid border-gray-400 bg-white fill-svg-purple p-4 text-black drop-shadow-lg hover:bg-purple-700 hover:fill-white hover:text-white dark:border-transparent dark:bg-slate-600 dark:fill-purple-400 dark:text-white dark:hover:bg-purple-700 dark:hover:fill-white xl:w-60"
        >
          <svg
            width="40"
            height="40"
            viewBox="0 0 40 40"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M27 22.5C24.7578 22.5 23.6797 23.75 20 23.75C16.3203 23.75 15.25 22.5 13 22.5C7.20312 22.5 2.5 27.2031 2.5 33V36.25C2.5 38.3203 4.17969 40 6.25 40H33.75C35.8203 40 37.5 38.3203 37.5 36.25V33C37.5 27.2031 32.7969 22.5 27 22.5ZM35 36.25C35 36.9375 34.4375 37.5 33.75 37.5H6.25C5.5625 37.5 5 36.9375 5 36.25V33C5 28.5859 8.58594 25 13 25C14.5312 25 16.0547 26.25 20 26.25C23.9375 26.25 25.4688 25 27 25C31.4141 25 35 28.5859 35 33V36.25ZM20 20C25.5234 20 30 15.5234 30 10C30 4.47656 25.5234 0 20 0C14.4766 0 10 4.47656 10 10C10 15.5234 14.4766 20 20 20ZM20 2.5C24.1328 2.5 27.5 5.86719 27.5 10C27.5 14.1328 24.1328 17.5 20 17.5C15.8672 17.5 12.5 14.1328 12.5 10C12.5 5.86719 15.8672 2.5 20 2.5Z"
            />
          </svg>
          <span className="text-lg font-semibold">Person</span>
        </button>
        <button
          onClick={() => {
            setNodeType(NodeType.URL);
          }}
          type="button"
          className="flex flex-col justify-center space-y-4 rounded-md border border-solid border-gray-400 bg-white fill-svg-purple p-4 text-black drop-shadow-lg hover:bg-purple-700 hover:fill-white hover:text-white dark:border-transparent dark:bg-slate-600 dark:fill-purple-400 dark:text-white dark:hover:bg-purple-700 dark:hover:fill-white xl:w-60"
        >
          <svg
            width="40"
            height="40"
            viewBox="0 0 40 40"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              fillRule="evenodd"
              clipRule="evenodd"
              d="M20 0.625C9.29688 0.625 0.625 9.29688 0.625 20C0.625 30.7031 9.29688 39.375 20 39.375C30.7031 39.375 39.375 30.7031 39.375 20C39.375 9.29688 30.7031 0.625 20 0.625ZM35.0938 12.5H28.6484C27.9453 9.03125 26.7656 6.07812 25.2734 3.97656C29.5703 5.39844 33.1016 8.5 35.0938 12.5ZM26.875 20C26.875 21.7891 26.75 23.4531 26.5391 25H13.4609C13.25 23.4531 13.125 21.7891 13.125 20C13.125 18.2109 13.25 16.5469 13.4609 15H26.5391C26.75 16.5469 26.875 18.2109 26.875 20ZM20 3.125C22.1016 3.125 24.7969 6.57031 26.1016 12.5H13.8984C15.2031 6.57031 17.8984 3.125 20 3.125ZM14.7266 3.97656C13.2422 6.07031 12.0547 9.02344 11.3516 12.5H4.90625C6.89844 8.5 10.4297 5.39844 14.7266 3.97656ZM3.125 20C3.125 18.2578 3.39062 16.5781 3.88281 15H10.9531C10.75 16.6016 10.625 18.2656 10.625 20C10.625 21.7344 10.7422 23.3984 10.9531 25H3.88281C3.39062 23.4219 3.125 21.7422 3.125 20ZM4.90625 27.5H11.3516C12.0547 30.9688 13.2344 33.9219 14.7266 36.0234C10.4297 34.6016 6.89844 31.5 4.90625 27.5ZM20 36.875C17.8984 36.875 15.2031 33.4297 13.8984 27.5H26.1016C24.7969 33.4297 22.1016 36.875 20 36.875ZM25.2734 36.0234C26.7578 33.9297 27.9453 30.9766 28.6484 27.5H35.0938C33.1016 31.5 29.5703 34.6016 25.2734 36.0234ZM29.0469 25C29.25 23.3984 29.375 21.7344 29.375 20C29.375 18.2656 29.2578 16.6016 29.0469 15H36.1172C36.6094 16.5781 36.875 18.2578 36.875 20C36.875 21.7422 36.6094 23.4219 36.1172 25H29.0469Z"
            />
          </svg>
          <span className="text-lg font-semibold">URL</span>
        </button>
      </div>
      <div
        id="node-selection-actions"
        className="flex w-full flex-row space-x-4 pt-6 md:justify-end md:pt-0"
      >
        <button
          onClick={() => closeModal()}
          className="w-full rounded-md border border-solid border-slate-300 px-6 py-3 hover:bg-purple-700 hover:text-white dark:bg-transparent md:w-max"
        >
          Cancel
        </button>
      </div>
    </div>
  );
}
