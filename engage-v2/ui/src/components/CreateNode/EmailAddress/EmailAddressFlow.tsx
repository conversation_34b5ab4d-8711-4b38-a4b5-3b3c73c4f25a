import { useQueryClient } from "@tanstack/react-query";
import { useState } from "react";
import { toast } from "react-toastify";

import {
  PostNodesEmailAddressMutationBody,
  getGetEngagementGraphsQueryKey,
  getGetEngagementQueryKey,
  usePostNodesEmailAddress,
} from "../../../client.ts";
import { validationSchemaType } from "../../../utils/validationutils.ts";
import ErrorMessageModal, { ErrorMessage } from "../../ErrorMessageModal.tsx";
import CreateNodeTypeModal, { Errors } from "../CreateNodeTypeModal";
import { CreateNodeScreen, ModalProps } from "../types";

export type FormValues = {
  email_address: string;
};

export default function EmailAddressFlow({
  engagementID,
  nodeGroupID,
  setCreateNodeScreen,
  closeModal,
  setNodeType,
}: ModalProps) {
  const queryClient = useQueryClient();
  const [errors, setErrors] = useState<Errors>({});
  const [isOpenErrorModal, setIsOpenErrorModal] = useState<boolean>(false);
  const [errorMessage, setErrorMessage] = useState<ErrorMessage | null>(null);

  const fields = [
    {
      name: "email_address",
      label: "E-mail Address",
      type: "text",
      required: true,
      validator: validationSchemaType.email,
    },
  ];

  const initialValues: FormValues = {
    email_address: "",
  };

  const validationSchema = fields.map((field) => ({
    name: field.name,
    required: field.required,
    validator: field.validator,
  }));

  const createEmailAddressMutation = usePostNodesEmailAddress({
    mutation: {
      onError: () => {
        openErrorModal({
          title: "Error creating node instance.",
          message: "Please try again later.",
        });
      },
      onSettled: (_data: any, error: unknown) => {
        if (!error) {
          toast.success("Node instance has been successfully created.");
        }
        const queryKey = getGetEngagementGraphsQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey });
        const engagementQueryKey = getGetEngagementQueryKey(engagementID);
        queryClient.invalidateQueries({ queryKey: engagementQueryKey });
        closeModal();
        setNodeType(null);
      },
    },
  });

  const handleSubmit = (formValues: PostNodesEmailAddressMutationBody) => {
    createEmailAddressMutation.mutate({
      data: {
        email_address: formValues.email_address,
        engagement_id: engagementID,
        node_group_id: nodeGroupID,
      },
    });
  };

  function openErrorModal(errorMessage: ErrorMessage) {
    if (errorMessage) {
      setErrorMessage(errorMessage);
    }
    setIsOpenErrorModal(true);
  }

  function closeErrorModal() {
    setIsOpenErrorModal(false);
  }

  return (
    <>
      <CreateNodeTypeModal
        fields={fields}
        initialValues={initialValues}
        validationSchema={validationSchema}
        onSubmit={handleSubmit}
        closeModal={() => {
          closeModal();
        }}
        setNodeType={setNodeType}
        setCreateNodeScreen={setCreateNodeScreen}
        CreateNodeScreen={CreateNodeScreen}
        errors={errors}
        setErrors={setErrors}
      />
      <ErrorMessageModal
        isOpen={isOpenErrorModal}
        closeModal={closeErrorModal}
        errorMessage={errorMessage}
      />
    </>
  );
}
