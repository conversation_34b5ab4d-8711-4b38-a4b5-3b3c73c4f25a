export enum NodeType {
  CloudInstance = "CLOUD_INSTANCE",
  EmailAddress = "EMAIL_ADDRESS",
  Host = "HOST",
  Person = "PERSON",
  URL = "URL",
}

export enum CreateNodeScreen {
  SelectNodeType = "SELECT_NODE_TYPE",
  AddCloudInstanceDetails = "ADD_CLOUD_INSTANCE_DETAILS",
  AddEmailAddressDetails = "ADD_EMAIL_ADDRESS_DETAILS",
  AddHostDetails = "ADD_HOST_DETAILS",
  AddURLDetails = "ADD_URL_DETAILS",
  AddPersonDetails = "ADD_PERSON_DETAILS",
}

export type ModalProps = {
  engagementID: string;
  nodeGroupID?: string;
  setCreateNodeScreen: (createNodeScreen: CreateNodeScreen) => void;
  closeModal: () => void;
  setNodeType: (nodeType: NodeType | null) => void;
};
