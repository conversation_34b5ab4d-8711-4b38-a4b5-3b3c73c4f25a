import Cytoscape from "cytoscape";

import { NodeGroup } from "../model";

export const createContextMenuItem = (
  id: string,
  content: string,
  color: string,
  nodeRef: { current: Cytoscape.NodeSingular | null },
  setNode: (node: Cytoscape.NodeSingular | null) => void,
  resetOtherNode: { current: Cytoscape.NodeSingular | null } | null,
  resetOtherNodeSetter?: (node: Cytoscape.NodeSingular | null) => void,
) => ({
  id,
  content,
  selector: "node",
  onClickFunction: (event: Cytoscape.EventObject) => {
    const target = event.target as Cytoscape.NodeSingular;
    if (nodeRef.current) {
      nodeRef.current.style({
        "border-color": "",
        "border-width": "",
      });
    }
    setNode(target);
    nodeRef.current = target;
    target.style({
      "border-color": color,
      "border-width": "3px",
    });
    if (resetOtherNode && resetOtherNode.current) {
      resetOtherNode.current.style({
        "border-color": "",
        "border-width": "",
      });
      resetOtherNodeSetter?.(null);
      resetOtherNode.current = null;
    }
  },
});

export function getNodeGroupName(nodeId: string, nodeGroups: NodeGroup[]) {
  const group = nodeGroups.find((group) =>
    group.nodes?.some((node) => node.id === nodeId),
  );
  return group ? group.name : "";
}
export function getNodeGroupId(nodeId: string, nodeGroups: NodeGroup[]) {
  const group = nodeGroups.find((group) =>
    group.nodes?.some((node) => node.id === nodeId),
  );
  return group ? group.id : "";
}

export function resetNodeHighlights(cy: Cytoscape.Core) {
  cy.nodes().forEach((node) => {
    node.style({
      "border-color": "",
      "border-width": "1px",
    });
    node.removeClass("highlight");
    node.removeClass("semitransp");
  });
  cy.edges().style({
    "line-color": "",
    "target-arrow-color": "",
    width: "",
  });
}
