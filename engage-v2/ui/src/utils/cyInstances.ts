import Cytoscape from "cytoscape";

const cyInstances: { [key: string]: Cytoscape.Core } = {};

export const getCyInstance = (key: string): Cytoscape.Core | null => {
  return cyInstances[key];
};

export const addCyInstance = (key: string, cyInstance: Cytoscape.Core) => {
  cyInstances[key] = cyInstance;
};

export const removeCyInstance = (key: string) => {
  const cy = cyInstances[key];
  if (cy) {
    cy.destroy();
    delete cyInstances[key];
  }
};
