/**
 * Generated by orval v7.10.0 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { EditNodeTypeHostInputBodyAlternativeNames } from "./editNodeTypeHostInputBodyAlternativeNames";
import type { EditNodeTypeHostInputBodyIpAddresses } from "./editNodeTypeHostInputBodyIpAddresses";

export interface EditNodeTypeHostInputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  alternative_names?: EditNodeTypeHostInputBodyAlternativeNames;
  ip_addresses: EditNodeTypeHostInputBodyIpAddresses;
  name: string;
}
