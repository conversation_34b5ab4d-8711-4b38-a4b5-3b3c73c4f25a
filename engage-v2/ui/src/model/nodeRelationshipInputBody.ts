/**
 * Generated by orval v7.10.0 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { NodeRelationshipInputBodyDescendants } from "./nodeRelationshipInputBodyDescendants";
import type { NodeRelationshipInputBodyEdgesToRemove } from "./nodeRelationshipInputBodyEdgesToRemove";

export interface NodeRelationshipInputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  descendants: NodeRelationshipInputBodyDescendants;
  edgesToRemove: NodeRelationshipInputBodyEdgesToRemove;
  source_node_id: string;
  target_node_id: string;
  type: string;
}
