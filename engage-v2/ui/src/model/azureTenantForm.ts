/**
 * Generated by orval v7.10.0 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */

export interface AzureTenantForm {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  /**
   * Azure App ID
   * @maxLength 50
   */
  azure_app_id?: string;
  /**
   * Azure App Secret
   * @maxLength 80
   */
  azure_app_secret?: string;
  /**
   * Azure Subscription ID
   * @maxLength 50
   */
  azure_subscription_id?: string;
  /**
   * Azure Tenant ID
   * @maxLength 50
   */
  azure_tenant_id: string;
}
