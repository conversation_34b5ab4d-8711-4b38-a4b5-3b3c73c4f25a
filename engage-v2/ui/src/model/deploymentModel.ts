/**
 * Generated by orval v7.10.0 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { DeploymentModelWarnings } from "./deploymentModelWarnings";

export interface DeploymentModel {
  created_at: string;
  engagement_id: string;
  error_message?: string;
  id: string;
  node_id: string;
  node_name: string;
  status: string;
  terraform_module: string;
  title: string;
  user_id: string;
  username: string;
  warnings?: DeploymentModelWarnings;
}
