/**
 * Generated by orval v7.10.0 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { NodeCloudInstanceInputBodyOpenIngressTcpPorts } from "./nodeCloudInstanceInputBodyOpenIngressTcpPorts";
import type { NodeCloudInstanceInputBodyProvider } from "./nodeCloudInstanceInputBodyProvider";
import type { OperatingSystemImageAzure } from "./operatingSystemImageAzure";

export interface NodeCloudInstanceInputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  /** Engagement ID */
  engagement_id: string;
  /** AWS Instance type */
  instance_type: string;
  /**
   * Instance name
   * @minLength 1
   * @maxLength 256
   * @pattern ^[a-zA-Z0-9 _.:/=+\-@]{1,256}$
   */
  name: string;
  /** Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created */
  node_group_id?: string;
  /** Open ingress TCP ports */
  open_ingress_tcp_ports: NodeCloudInstanceInputBodyOpenIngressTcpPorts;
  operating_system_image?: OperatingSystemImageAzure;
  operating_system_image_id?: string;
  provider: NodeCloudInstanceInputBodyProvider;
  region: string;
  selected_account_id: string;
  /** Startup script in base64 encoding */
  startup_script?: string;
}
