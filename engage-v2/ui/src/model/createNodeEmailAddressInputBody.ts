/**
 * Generated by orval v7.10.0 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */

export interface CreateNodeEmailAddressInputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  email_address: string;
  /** Engagement ID */
  engagement_id: string;
  /** Node Group ID of the Engagement that the Node will be created in. If this attribute is not provided, a new Node Group will be created */
  node_group_id?: string;
}
