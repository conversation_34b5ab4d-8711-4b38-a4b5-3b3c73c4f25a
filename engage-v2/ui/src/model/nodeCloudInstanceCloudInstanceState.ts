/**
 * Generated by orval v7.10.0 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */

export type NodeCloudInstanceCloudInstanceState =
  (typeof NodeCloudInstanceCloudInstanceState)[keyof typeof NodeCloudInstanceCloudInstanceState];

// eslint-disable-next-line @typescript-eslint/no-redeclare
export const NodeCloudInstanceCloudInstanceState = {
  pending: "pending",
  running: "running",
  stopping: "stopping",
  stopped: "stopped",
  "shutting-down": "shutting-down",
  terminated: "terminated",
  error: "error",
  new: "new",
} as const;
