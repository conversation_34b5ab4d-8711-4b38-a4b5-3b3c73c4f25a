/**
 * Generated by orval v7.10.0 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { CloudInstanceCiDeploymentStatus } from "./cloudInstanceCiDeploymentStatus";
import type { CloudInstanceCloudInstanceState } from "./cloudInstanceCloudInstanceState";
import type { CloudInstanceOpenPorts } from "./cloudInstanceOpenPorts";
import type { CloudInstancePublicIpv4Address } from "./cloudInstancePublicIpv4Address";

export interface CloudInstance {
  ci_deployment_status: CloudInstanceCiDeploymentStatus;
  cloud_instance_id: string;
  cloud_instance_state: CloudInstanceCloudInstanceState;
  created_at: string;
  id: string;
  name: string;
  open_ports: CloudInstanceOpenPorts;
  operating_system_image_id: string;
  provider: string;
  public_ipv4_address: CloudInstancePublicIpv4Address;
  region: string;
  type: string;
  updated_at: string;
}
