/**
 * Generated by orval v7.10.0 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { GetInventoryOutputBodyNodeGroups } from "./getInventoryOutputBodyNodeGroups";
import type { GetInventoryOutputBodyNodeTypes } from "./getInventoryOutputBodyNodeTypes";

export interface GetInventoryOutputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  node_groups: GetInventoryOutputBodyNodeGroups;
  node_types: GetInventoryOutputBodyNodeTypes;
  total_nodes: number;
}
