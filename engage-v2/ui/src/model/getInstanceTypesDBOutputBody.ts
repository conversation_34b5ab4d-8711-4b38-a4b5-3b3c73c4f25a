/**
 * Generated by orval v7.10.0 🍺
 * Do not edit manually.
 * Engage API
 * OpenAPI spec version: 2.0.0
 */
import type { GetInstanceTypesDBOutputBodyInstanceTypes } from "./getInstanceTypesDBOutputBodyInstanceTypes";

export interface GetInstanceTypesDBOutputBody {
  /** A URL to the JSON Schema for this object. */
  readonly $schema?: string;
  instance_types: GetInstanceTypesDBOutputBodyInstanceTypes;
}
