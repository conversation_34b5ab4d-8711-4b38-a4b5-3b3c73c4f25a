{"name": "engage-ui-v2", "private": true, "version": "0.0.0", "type": "module", "engines": {"node": "^22", "pnpm": "^10"}, "scripts": {"preinstall": "npx only-allow pnpm", "dev": "vite", "build": "tsc -b && vite build", "prod": "cross-env NODE_ENV=production vite", "lint": "eslint .", "preview": "vite preview", "orval": "orval", "format": "prettier . --write"}, "dependencies": {"@azure/msal-browser": "4.15.0", "@azure/msal-react": "3.0.15", "@headlessui/react": "^2.2.4", "@popperjs/core": "^2.11.8", "@tanstack/match-sorter-utils": "^8.19.4", "@tanstack/react-query": "^5.83.0", "@tanstack/react-query-devtools": "^5.83.0", "@tanstack/react-router": "^1.128.0", "@tanstack/react-table": "^8.21.3", "@tippyjs/react": "^4.2.6", "axios": "^1.10.0", "clsx": "^2.1.1", "cytoscape": "^3.32.1", "cytoscape-context-menus": "^4.2.1", "cytoscape-dagre": "^2.5.0", "cytoscape-popper": "^4.0.1", "date-fns": "^4.1.0", "dotenv": "^17.2.0", "highlight.js": "^11.11.1", "primereact": "^10.9.6", "react": "^19.1.0", "react-cytoscapejs": "^2.0.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-toastify": "^11.0.5", "tippy.js": "^6.3.7", "zod": "^4.0.5"}, "devDependencies": {"@eslint/js": "^9.31.0", "@tailwindcss/postcss": "^4.1.11", "@tanstack/eslint-plugin-query": "^5.81.2", "@tanstack/router-devtools": "^1.128.0", "@tanstack/router-vite-plugin": "^1.128.0", "@trivago/prettier-plugin-sort-imports": "^5.2.2", "@types/cytoscape-context-menus": "^4.1.4", "@types/cytoscape-dagre": "^2.3.3", "@types/cytoscape-popper": "^2.0.4", "@types/node": "^24.0.14", "@types/react": "^19.1.8", "@types/react-cytoscapejs": "^1.2.5", "@types/react-dom": "^19.1.6", "@typescript-eslint/eslint-plugin": "^8.37.0", "@typescript-eslint/parser": "^8.37.0", "@vitejs/plugin-react": "^4.6.0", "cross-env": "^7.0.3", "eslint": "^9.31.0", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "orval": "^7.10.0", "postcss": "^8.5.6", "prettier": "^3.6.2", "prettier-plugin-tailwindcss": "^0.6.14", "tailwindcss": "^4.1.11", "typescript": "^5.8.3", "typescript-eslint": "^8.37.0", "vite": "^7.0.5", "vite-plugin-svgr": "^4.3.0"}, "prettier": {"plugins": ["@trivago/prettier-plugin-sort-imports", "prettier-plugin-tailwindcss"], "importOrder": ["^@core/(.*)$", "^@server/(.*)$", "^@ui/(.*)$", "^[./]"], "importOrderSeparation": true, "importOrderSortSpecifiers": true}, "pnpm": {"overrides": {"esbuild@<=0.24.2": ">=0.25.0"}, "onlyBuiltDependencies": ["esbuild", "@tailwindcss/oxide"]}}