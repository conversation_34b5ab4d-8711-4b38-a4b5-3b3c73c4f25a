# Engage V2
- [Engage V2](#engage-v2)
  - [Azure setup](#azure-setup)

## Azure setup

1.  Create a UI and API app in Azure
   
    [CDI DevTest link](https://portal.azure.com/?feature.msaljs=true#view/Microsoft_AAD_RegisteredApps/ApplicationsListBlade)

    > Home > Microsoft Entra > App registrations > New registration

    * Pick the option: `Accounts in this organizational directory only`
    * The API platform option must be `Web`.
    * The UI platform option must be `Single-page application (SPA)`.

1. Expose API scopes in the API app

    [CDI DevTest link](https://portal.azure.com/?feature.msaljs=true#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/ProtectAnAPI/appId/4e117b8f-a0c9-4ff2-a0e5-41e203dd340e/isMSAApp~/false)

    > Home > Microsoft Entra > App registrations > API app > Manage (left menu) > API permissions > Add a scope

1. Use API scopes in the UI app

    [CDI DevTest link](https://portal.azure.com/?feature.msaljs=true#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/CallAnAPI/appId/c6bdd6d0-d0e1-4e22-a61c-8d933f720d72/isMSAApp~/false)


    > Home > Microsoft Entra > App registrations > UI app > Manage (left menu) > API permissions > Add a permission

    Use the scope created from the above step.

1. Create `admin` and `standard` user groups

    [CDI DevTest link](https://portal.azure.com/?feature.msaljs=true#view/Microsoft_AAD_IAM/GroupsManagementMenuBlade/~/AllGroups)

    > Home > Microsoft Entra > Manage (left menu) > Groups > New group

1. Assign the user groups to the API app

    [CDI DevTest link](https://portal.azure.com/?feature.msaljs=true#view/Microsoft_AAD_IAM/StartboardApplicationsMenuBlade/~/AppAppsPreview/menuId~/null)

    > Home > Enterprise applications > API app > Manage (left menu) > Users and Groups > Add user/group

1. Allow logins only from the user groups

    [CDI DevTest link](https://portal.azure.com/?feature.msaljs=true#view/Microsoft_AAD_IAM/StartboardApplicationsMenuBlade/~/AppAppsPreview/menuId~/null)

    [Microsoft documentation](https://learn.microsoft.com/en-us/entra/identity-platform/howto-restrict-your-app-to-a-set-of-users)

    > Home > Enterprise applications > API app > Manage (left menu) > Properties

    Switch `Assignment required?` to `Yes`.

1. Give the API app permissions to read user groups and user details

    [CDI DevTest link](https://portal.azure.com/?feature.msaljs=true#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/CallAnAPI/appId/4e117b8f-a0c9-4ff2-a0e5-41e203dd340e/isMSAApp~/false)

    > Home > Microsoft Entra > App registrations > API app > Manage (left menu) > API permissions > Add a permission

    **Required permissions**
    * >Microsoft Graph > Application permissions > Group.Read.All
    * >Microsoft Graph > Application permissions > User.Read.All
    * >Microsoft Graph > Delegated permissions > User.Read


1. Create a client secret in the API app

    [CDI DevTest link](https://portal.azure.com/?feature.msaljs=true#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/Credentials/appId/4e117b8f-a0c9-4ff2-a0e5-41e203dd340e/isMSAApp~/false)

    > Home > Microsoft Entra > App registrations > API app > Manage (left menu) > Certificates & secrets > New client secret


1. Allow the API app to embed groups of a user in a JWT 

    [CDI DevTest link](https://portal.azure.com/?feature.msaljs=true#view/Microsoft_AAD_RegisteredApps/ApplicationMenuBlade/~/TokenConfiguration/appId/4e117b8f-a0c9-4ff2-a0e5-41e203dd340e/isMSAApp~/false)


    > Home > Microsoft Entra > App registrations > API app > Manage (left menu) > Token configuration > Add groups claim > check 'Security groups'
